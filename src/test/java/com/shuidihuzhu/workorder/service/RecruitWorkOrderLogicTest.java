package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/4/2 14:14
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class RecruitWorkOrderLogicTest {

    @Resource
    private AssignateWorkOrderPublisher workOrderPublisher;

    private WorkOrderFacade getWorkOrderFacade(){
        return new WorkOrderFacade(){
            @Override
            public OpResult vlidate(WorkOrderBase wordOrder) {
                return null;
            }

            @Override
            public OpResult<Long> create(WorkOrderBase wordOrder) {
                return null;
            }

            @Override
            public OpResult handle(HandleOrderParam param) {
                return null;
            }

            @Override
            public OpResult getOrderList(WorkOrderListParam param) {
                return null;
            }
        };
    }

    @Test
    public void triggerOnlineService() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_online_service.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerFirstAudit() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(), WorkOrderType.pr_first_screen.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerSecondLineAudit() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_2line_first_screen_service.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerSecondAudit() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_second_screen.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerSSupplement() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_supplement_material.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerReturnVisit() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_return_visit_service.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerSubmitAudit() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_submit_audit.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

    @Test
    public void triggerSubmitAuditReject() {
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(getWorkOrderFacade(),
                WorkOrderType.pr_submit_audit_reject.getType());
        workOrderPublisher.publishEvent(assignateWorkOrderEvent);
    }

}
