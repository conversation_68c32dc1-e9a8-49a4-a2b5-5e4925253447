package com.shuidihuzhu.workorder.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.core.delegate.sea.SeaAuthHelper;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/10/24
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TestStatService {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Resource(name = "ugcProgressWorkorderStatService")
    private WorkOrderStatService progress;

    @Resource
    private OrderAssignFacade iAssignService;

    @Test
    public void tetsll(){
        iAssignService.doAssignate(WorkOrderType.report_split_draw.getType());
//        OpResult opResult =  progress.getWorkOrderStatList(6,"15",191);
//
//      log.info("opResult = {}", JSON.toJSONString(opResult));
    }

}
