package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.ChuciHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.service.impl.ChuciWorkOrderServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**t
 * @author: lixiaoshuang
 * @create: 2020-04-21 15:07
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TestWorkOrder {

    @Autowired
    private ChuciWorkOrderServiceImpl chuciWorkOrderService;
    @Autowired
    private OrderGroupService orderGroupService;


    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testGroup(){
        VonStaffStatusChangePayload e = new VonStaffStatusChangePayload();
        e.setOperatorId(103);
        e.setOrderType(110);
        e.setStaffStatus(1);
        orderGroupService.onStaffStatusChange(e);
    }

    @Test
    public void test() {
        ChuciHandleOrderParam chuciHandleOrderParam = new ChuciHandleOrderParam();
        chuciHandleOrderParam.setHandleResult(HandleResultEnum.audit_pass.getType());
        chuciHandleOrderParam.setWorkOrderId(117440);
        chuciHandleOrderParam.setUserId(102);
        chuciHandleOrderParam.setOrderType(57);
        chuciHandleOrderParam.setCaseId(2872186);
        chuciWorkOrderService.handle(chuciHandleOrderParam);
//        assignateWorkOrderListener.onApplicationEvent(new AssignateWorkOrderEvent(this, 29));
    }
}
