package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.workorder.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/5/25
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class OrganizationServiceTest {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Resource
    private OrganizationService organizationService;

    @Test
    public void testGetUserOrgId() {
        long userId = 1721;
        int operatorOrgId = organizationService.getUserOrgId(userId);
        System.out.println("operatorOrgId:" + operatorOrgId);
    }
}
