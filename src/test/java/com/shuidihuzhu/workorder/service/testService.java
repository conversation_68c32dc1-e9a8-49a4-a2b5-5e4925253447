package com.shuidihuzhu.workorder.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import com.shuidihuzhu.workorder.service.impl.*;
import com.shuidihuzhu.workorder.service.mq.producer.DianhuaCreateMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.joda.time.DateTime;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/19
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class testService {


    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private StaffStatusService staffStatusService;


    @Autowired
    private CrowdfundingFeignClient client;


    @Resource(name = "cailiaoWorkOrderService")
    private WorkOrderFacade workOrderFacade;

    @Autowired
    private BaseWorkOrderService workOrderService;


    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private PermissionFeignClient permissionFeignClient;


    @Resource(name = "cailiaoWorkOrderStatServiceImpl")
    private WorkOrderStatService cailiaoWorkOrderStatService;

    @Resource(name = "shouciWorkOrderStatService")
    private WorkOrderStatService shoucistat;

    @Resource(name = "shouciWorkOrderService")
    private WorkOrderFacade shouci;

    @Autowired
    private ShouciWorkOrderServiceImpl shouciWorkOrderService;

    @Autowired
    private AssignateWorkOrderPublisher publisher;


    @Resource(name = "chuciWorkOrderService")
    private WorkOrderFacade chuci;

    @Autowired
    private ClassifyTypeService classifyTypeService;


    @Autowired
    private WorkOrderTimelineSevice timelineSevice;


    @Autowired
    private EagleFacadeImpl eagleFacade;

    @Autowired
    private AssignWorkOrderService assignWorkOrderService;


    @Resource(name = "cailiaoWorkOrderService")
    private WorkOrderFacade cailiao;


    @Resource(name = "dianhuaWorkOrderService")
    private WorkOrderFacade dianhua;


    @Autowired
    private DianhuaCreateMq dianhuaCreateMq;


    @Resource(name = "ugcWorkOrderService")
    private WorkOrderFacade ugc;

    @Resource(name = "juanzhuanWorkOrderService")
    private WorkOrderFacade juanzhuan;



    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private juanzhuanStatServiceImpl juanzhuanStatService;


    @Autowired
    private OrganizationService organizationService;

    @Resource(name = "juanzhuanWorkOrderStatService")
    private JuanzhuanWorkOrderStatServiceImpl juanzhuanWorkOrderStatService;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void feign(){
        CrowdfundingInfo c = client.getCrowdfundingByuuid("b7bfc1d1-da65-4763-aecf-b0eb60c3c425").getData();

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(976597)).getData();

        System.out.println(c+"   999999    "+feignResponse);
    }




    @Test
    public void free(){

        workOrderService.freeWorkOrder(191,WorkOrderType.huifang.getType());

    }


    @Test
    public void sort(){
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(Lists.newArrayList(201L,327L,1469L,208L,101L), WorkOrderType.yanhou.getType(), HandleResultEnum.doing.getType());

        System.out.println("   999999    "+list);

        Optional<WorkOrderDoingCount> l =  list.stream().filter(r->r.getNum() < 2).sorted(Comparator.comparing(WorkOrderDoingCount::getNum)).findFirst();
        System.out.println("   999999    "+ l.isPresent());

       Map<Integer,List<WorkOrderDoingCount>> map = list.stream().filter(r->r.getNum() < WorkOrderConfig.assignation_count).collect(Collectors.groupingBy(WorkOrderDoingCount :: getNum));
        System.out.println("   999999    "+map);
    }





    @Test
    public void timeTest(){
        String start = "2019-03-19 00:00:00";
        String end = "2019-03-20 00:00:00";

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {

            Date startTime = sdf.parse(start);
            Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());

            boolean isSameDay = DateUtils.isSameDay(startTime, dateOfZero);

            System.out.println("**************");
            System.out.println(startTime);
            System.out.println(dateOfZero);
            System.out.println(isSameDay);
            System.out.println(OpResult.createSucResult());
            log.info("AssignateWorkOrderListener,result:{}", OpResult.createSucResult());
            System.out.println("**************");
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void see(){
//        List<Integer> l=  workOrderTypeService.getByOneLevel(2);
//        Map<Integer, List<Integer>> m = ClassifyTypeEnum.getClassiyByPermissions(Sets.newHashSet("cailiaoshenhe:yanhou"));
//        System.out.println("**************    "+l);
//
//        System.out.println("**************    "+m);


        System.out.println("**************    "+staffStatusService.getStaffStatusStat(191L));
    }

    @Test
    public void fefef(){
//        AuthRpcResponse<List<AdminUserAccountModel>> ll = clientV1.getUserAccountsByIds(Lists.newArrayList(191));
//
//        System.out.println("**************    "+ll);


//        OpResult<PageResult<StaffStatus>> opResult=  staffStatusService.getStaffStatusByTypes(Lists.newArrayList(1,2),2,"pre",327);
//
//        System.out.println("**************    "+opResult);

        List<StaffStatus> list = staffStatusService.getStaffStatusByTypes(191,Lists.newArrayList(1));

        System.out.println("**************    "+list);

    }




    @Test
    public void eelll(){
        OpResult opResult = cailiaoWorkOrderStatService.getWorkOrderStatList(1,"1",0);

        System.out.println("**************    "+opResult);

    }

    @Test
    public void elfl(){

        Response<List<AuthUserDto>> ge = permissionFeignClient.getUsersByPermission(WorkOrderType.sanlei.getPermission());

        System.out.println("**************    "+ge.getData());

    }


    @Test
    public void fefs(){

        ShouciWorkOrder shouciWorkOrder = new ShouciWorkOrder();

        shouciWorkOrder.setCaseId(980593);
        shouciWorkOrder.setOperComment("1211212");

        OpResult opResult = shouci.doCreate(shouciWorkOrder);

        System.out.println("**************"+ JSON.toJSONString(opResult));

    }

    @Test
    public void efe(){

        ShouciHandleOrderParam p = new ShouciHandleOrderParam();
        p.setUserId(191);
        p.setWorkOrderId(168);
        p.setHandleResult(HandleResultEnum.stop_case.getType());
        p.setOperComment("12121212121212");
        p.setCallStatus("2");
//        p.setCallUnicode("111111111111111111");
        shouci.handle(p);
    }

   @Test
    public void lisefefef(){

        ShouciWorkOrderListParam param = new ShouciWorkOrderListParam();
       param.setUserId(191);
       param.setHandleResult("2,5,6");
       param.setWorkOrderId(0);
       param.setPageSize(10);
       param.setPaging("next");
       param.setCaseId(0);
       param.setCallStatus("");
       param.setOrderType(3);
       param.setStartTime("2019-05-15 00:00:00");
       param.setEndTime("2019-05-19 00:00:00");

        OpResult opResult = shouci.getOrderList(param);
        System.out.println("**************"+ JSON.toJSONString(opResult));
    }



    @Test
    public void fefdwwds(){

        shouciWorkOrderService.changeShouciType(980593);


    }

    @Test
    public void effef(){
        publisher.publishEvent(new AssignateWorkOrderEvent(this,47));

    }

    @Test
    public void eellleeee(){

        OpResult opResult = shoucistat.getWorkOrderStatList(2,"4",0);

        System.out.println("**************    "+opResult);

    }

    @Test
    public void fefe(){
//        OpResult opResult = staffStatusService.getStaffStatusByTypes(Lists.newArrayList(2,3,4),100,"",191,true);
//        System.out.println("**************    "+opResult);
        staffStatusService.autoOff();
    }


    @Test
    public void chuci(){

//        ChuciWorkOrder workOrderCreate = new ChuciWorkOrder();
//        workOrderCreate.setCaseId(982533);
//        workOrderCreate.setOrderType(WorkOrderType.shenhe.getType());
//        workOrderCreate.setCreateOrder("1212121212");
//        workOrderCreate.setOperComment("efefefef");
//
//
//        OpResult opResult = chuci.doCreate(workOrderCreate);
//
//        System.out.println("**************    "+opResult);
        //--------------------------
        WorkOrderListParam param = new WorkOrderListParam();

        param.setUserId(191);
        param.setHandleResult("7,6,5,8,9");
        param.setWorkOrderId(0);
        param.setPageSize(1);
        param.setPaging("next");
        param.setCaseId(0);
        param.setOrderType(5);
        param.setStartTime("2020-07-13 00:00:00");
        param.setEndTime("2020-07-14 00:00:00");

        OpResult<PageResult> o = chuci.getOrderList(param);
        System.out.println("**************    "+o);
////        PageResult pageResult =  o.getData();
//        List<WorkOrderVO> voList = pageResult.getPageList();
//        voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getOrderLevel).reversed()).collect(Collectors.toList());
//        System.out.println("**************    "+voList);

//        ChuciHandleOrderParam p = new ChuciHandleOrderParam();
//        p.setWorkOrderId(1536);
//        p.setHandleResult(HandleResultEnum.exception_done.getType());
//        p.setOrderType(WorkOrderType.bohui.getType());
//        p.setOperComment("实验关闭驳回工单");
//        OpResult pp = chuci.doHandle(p);
//        System.out.println("**************    "+pp);
    }

    @Test
    public void fefe22222(){

        LocalDate date = LocalDate.of(2024, 4, 5);
        ZonedDateTime zonedDateTime = date.atStartOfDay(ZoneId.systemDefault());
        long dateTime = zonedDateTime.toInstant().toEpochMilli();
        Timestamp timestamp = new Timestamp(dateTime);
        Timestamp timestamp2 = DateUtil.getTimestampFromShortString("2024-04-05");

        System.out.println("**************    "+ LocalDate.now()+" "+ LocalTime.now().plusHours(-2));
        System.out.println("**************    "+LocalDate.now().plusDays(1));

        LocalDateTime createTime = LocalDateTime.ofEpochSecond(1558923677497L,0, ZoneOffset.ofHours(8));
        Instant instant = Instant.ofEpochMilli(1558923677497L);
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime c = LocalDateTime.ofInstant(instant, zone);
        System.out.println("**************    "+createTime);
        System.out.println("**************    "+c);
        System.out.println("**************    "+c.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        System.out.println("**************    "+ Joiner.on(",").join(Lists.newArrayList(1,2,3,4,5,6)));
    }

    @Test
    public void list(){
        QueryListParam q = new QueryListParam();

        q.setPageSize(20);
        q.setPaging("next");
//        q.setPageWorkOrderId(636);
//        q.setOperId(467);
        q.setHandleResult(-1);
        q.setOrderType("33,29,30,31");
        q.setOuterUser(true);
//        q.setWorkOrderId(545);
//        q.setMobile("13521088546");
//        q.setCaseId(981591);
        OpResult o =workOrderService.getWorkOrderList(q);

        System.out.println("**************    "+ o);
    }

    @Test
    public void etste(){
//        Map<Integer,List<Integer>> d = classifyTypeService.userClassify(367);
//
//        System.out.println("**************    "+ d);
       boolean b = WorkOrderFacade.class.isAssignableFrom(ChuciWorkOrderServiceImpl.class);

        System.out.println("**************    "+ b);

        b = WorkOrderFacade.class.isAssignableFrom(OrderExtServiceImpl.class);

        System.out.println("**************    "+ b);
    }


    @Test
    public void testTimeLine(){

        WorkOrderTimeline w = new WorkOrderTimeline();

        w.setOperatorId(1111);
        w.setWorkOrderType(WorkOrderType.bohui.getType());
        w.setWorkResult(HandleResultEnum.audit_pass.getType());
        w.setOperatorId(1212121);
        w.setOperateType(1);
        w.setComment("fefefefef");

        timelineSevice.insertTimeLine(w);
    }

    @Test
    public void testeagleFacade(){
        String result = eagleFacade.getStringByKey(1213,"isCallout","0");

        System.out.println("**************    "+ result);
    }

    @Test
    public void segfg(){
        Response<Long> response = assignWorkOrderService.reprocessWorkOrder(134831L,81599,"12121212");

        System.out.println("**************    "+ JSON.toJSONString(response));
    }

    @Test
    public void  testcailiao(){

//        CailiaoWorkOrder wordOrder = new CailiaoWorkOrder();
//        wordOrder.setOrderType(10);
//        wordOrder.setCaseId(984989);
//        OpResult<Long> opResult = cailiao.doCreate(wordOrder);
//
//        System.out.println("**************    "+ opResult);

        //andleResult=5, workOrderId=10358, userId=0, orderType=8
        CailiaoHandleOrderParam param = new CailiaoHandleOrderParam();
        param.setWorkOrderId(10358);
        param.setHandleResult(5);
        param.setOrderType(8);
        cailiao.doHandle(param);
    }


    @Test
    public void cailiaoList(){

        WorkOrderListParam param = new WorkOrderListParam();
        param.setUserId(191);
        param.setHandleResult("1");
        param.setWorkOrderId(0);
        param.setPageSize(30);
        param.setPaging("next");
        param.setCaseId(0);
        param.setOrderType(47);


        OpResult opResult = cailiao.getOrderList(param);
        System.out.println("**************"+ JSON.toJSONString(opResult));
    }

    @Test
    public void testDianhua(){
//        DianhuaWorkOrder dd = new DianhuaWorkOrder();
//        dd.setCaseId(985637);
//        dd.setOrderType(WorkOrderType.heshi.getType());
//        dd.setYiyuanHeshiId("1212");
//        dd.setYiyuanHeshiTime("2019-08-06 11:55:59");
//
//        OpResult opResult = dianhua.doCreate(dd);
//
//        System.out.println("**************"+ JSON.toJSONString(opResult));

//
        DianhuaWorkOrderListParam p = new DianhuaWorkOrderListParam();
        p.setUserId(327);
        p.setOrderType(WorkOrderType.heshi.getType());
        p.setHandleResult("1");
        p.setPageSize(30);
        p.setPage(0);

        OpResult<PageResult<DianhuaWorkOrderVo>> opResult = dianhua.getOrderList(p);

        System.out.println("************** "+ JSON.toJSONString(opResult));

//        GenjinModel genjinModel = new GenjinModel();
//        genjinModel.setCaseId(985680);
//        genjinModel.setTime(LocalDateTime.now().toString());
//        genjinModel.setOperation(GenjinModel.xiafa);
//        genjinModel.setYiyuanHeshiId("1212");
//        genjinModel.setYiyuanHeshiTime("2019-08-16 17:18:52");
//
//        dianhuaCreateMq.sendGenjinMq(genjinModel);
    }

    @Test
    public void testugc(){
        //生成新工单
        UgcWorkOrder workOrder = new UgcWorkOrder();
        workOrder.setCaseId(121213);
        workOrder.setOrderType(WorkOrderType.ugcpinglun.getType());
        workOrder.setExtId("232323");
        workOrder.setContentType("3423423423");
        workOrder.setWordId("234234234");

//        ugc.doCreate(workOrder);
        WorkOrderListParam param = new WorkOrderListParam();
        param.setUserId(191);
        param.setHandleResult("1");
        param.setOrderType(WorkOrderType.ugcpinglun.getType());
        param.setPageSize(30);
        param.setPaging("next");

        OpResult opResult = ugc.getOrderList(param);

        System.out.println("************** "+ JSON.toJSONString(opResult));

    }

    @Test
    public void testjuanzhuan(){
        JuanzhuanWorkOrder j = new JuanzhuanWorkOrder();
        j.setCaseId(2289631);
        j.setOrderType(WorkOrderType.d1_1v1.getType());
        j.setShowName("d0d0d0d0");
        OpResult result = juanzhuan.doCreate(j);

        System.out.println("************** "+ JSON.toJSONString(result));


    }

    @Test
    public void eooee(){
        List<WorkOrderVO> dd =  baseWorkOrderService.ListByCaseIdAndTypeAndResult(2288567,Lists.newArrayList(29),Lists.newArrayList(2,5));

        System.out.println("************** "+ JSON.toJSONString(dd));

    }
    @Test
    public void seterer(){
        juanzhuanStatService.statAll();
        juanzhuanStatService.statInner();
        juanzhuanStatService.statOuter();
        juanzhuanStatService.statUser();
//        String batchTag= "4_197";
//        String oo = batchTag.substring(batchTag.indexOf("_")+1);
//        System.out.println("************** "+ oo);
//        int orgId = Integer.valueOf(oo);
//        String name= organizationService.getOrgName(orgId);
//        System.out.println("************** "+ name);

    }

    @Test
    public void getBeforeHours(){
        LocalDateTime time = LocalDateTime.now();

        String format = time.plusHours(-1).plusDays(-7).format(DateTimeFormatter.ofPattern("yyyyMMddHH"));


        System.out.println("************** "+ Integer.valueOf(format));

        String endTime = time.plusHours(-1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:00:00"));
        System.out.println("************** "+ endTime);

        System.out.println("************** "+ getPercent(28,348));

        juanzhuanStatService.statAll();

    }

    public String getPercent(int divisor,int dividend){

        if (divisor == 0 || dividend == 0){
            return "0";
        }

        BigDecimal a = BigDecimal.valueOf(divisor);
        BigDecimal b = BigDecimal.valueOf(dividend);

        return a.multiply(BigDecimal.valueOf(100)).divide(b,2,RoundingMode.HALF_UP).toString();
    }

    @Test
    public void fefoe(){
//        juanzhuanWorkOrderStatService.getWorkOrderStatList(11,"",0,1);
        juanzhuanWorkOrderStatService.getWorkOrderStatList(11,"29",0,1);

    }
    @Resource(name="caiLiaoWorkOrderAssignate")
    private AssignateWorkOrderFacade caiLiaoWorkOrderAssignate;


    @Test
    public void fefeeef1(){
        AssignateWorkOrderEvent assignateWorkOrderEvent = new AssignateWorkOrderEvent(this, 19);

//        orderAssignFacade.onAssignEvent();
    }
}
