package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.workorder.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @DATE 2019/1/20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TestReactor {


    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }


    @Test
    public void esfe(){

    }
}
