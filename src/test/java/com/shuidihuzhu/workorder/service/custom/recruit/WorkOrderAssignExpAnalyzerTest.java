package com.shuidihuzhu.workorder.service.custom.recruit; 

import com.shuidihuzhu.workorder.Application;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

/** 
* WorkOrderAssignExpAnalyzer Tester. 
* 
* <AUTHOR> 
* @since <pre>7月 9, 2021</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class WorkOrderAssignExpAnalyzerTest { 

    @Autowired
    private WorkOrderAssignExpAnalyzer workOrderAssignExpAnalyzer;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: assignExpAnalyze() 
     * 
     */ 
    @Test
    public void testAssignExpAnalyze() throws Exception {
        workOrderAssignExpAnalyzer.assignExpAnalyze();
    } 
    
    
}
