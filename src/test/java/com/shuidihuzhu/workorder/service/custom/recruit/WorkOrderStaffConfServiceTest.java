package com.shuidihuzhu.workorder.service.custom.recruit; 

import com.shuidihuzhu.workorder.Application;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

/** 
* WorkOrderStaffConfService Tester. 
* 
* <AUTHOR> 
* @since <pre>10月 18, 2021</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class WorkOrderStaffConfServiceTest { 

    @Autowired
    private WorkOrderStaffConfService workOrderStaffConfService;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    } 
    
    /** 
     * 
     * Method: assignForFirstAudit(long currWorkOrderId, long patientId) 
     * 
     */ 
    @Test
    public void testAssignForFirstAudit() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: assignForSecondLine(long currWorkOrderId, long patientId) 
     * 
     */ 
    @Test
    public void testAssignForSecondLine() throws Exception {
        workOrderStaffConfService.assignForSecondLine(1345806, 282080);
    } 
    
    /** 
     * 
     * Method: assignForSecondAudit(long currWorkOrderId, long patientId, long firstScreenId) 
     * 
     */ 
    @Test
    public void testAssignForSecondAudit() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: assignForSupplement(long currWorkOrderId, long patientId, long supplementTaskId) 
     * 
     */ 
    @Test
    public void testAssignForSupplement() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: assignForOnlineService(long currWorkOrderId, long patientId, int workOrderType) 
     * 
     */ 
    @Test
    public void testAssignForOnlineService() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    /** 
     * 
     * Method: assignForReturnVisit(int workOrderType, long workOrderId, long patientId) 
     * 
     */ 
    @Test
    public void testAssignForReturnVisit() throws Exception { 
        //TODO: Test goes here... 
    } 
    
    
    /** 
     * 
     * Method: useRecruitSpecialChannel(long patientId) 
     * 
     */ 
    @Test
    public void testUseRecruitSpecialChannel() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = WorkOrderStaffConfService.getClass().getMethod("useRecruitSpecialChannel", long.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: assignForSupplementByTaskId(long supplementTaskId, long patientId, long currWorkOrderId) 
     * 
     */ 
    @Test
    public void testAssignForSupplementByTaskId() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = WorkOrderStaffConfService.getClass().getMethod("assignForSupplementByTaskId", long.class, long.class, long.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: certainUrgentRuleResponsible(long workOrderId, long patientId, long lastOptUid, int currOrderType, PrAllocationEnum allocateType) 
     * 
     */ 
    @Test
    public void testCertainUrgentRuleResponsible() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = WorkOrderStaffConfService.getClass().getMethod("certainUrgentRuleResponsible", long.class, long.class, long.class, int.class, PrAllocationEnum.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: certainRuleResponsible(long workOrderId, long patientId, long lastOptUid, int currOrderType, PrAllocationEnum allocateType) 
     * 
     */ 
    @Test
    public void testCertainRuleResponsible() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = WorkOrderStaffConfService.getClass().getMethod("certainRuleResponsible", long.class, long.class, long.class, int.class, PrAllocationEnum.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
    /** 
     * 
     * Method: obtainResponsibleRecent(long patientId, int workOrderType) 
     * 
     */ 
    @Test
    public void testObtainResponsibleRecent() throws Exception { 
        //TODO: Test goes here... 
                /* 
                try { 
                   Method method = WorkOrderStaffConfService.getClass().getMethod("obtainResponsibleRecent", long.class, int.class); 
                   method.setAccessible(true); 
                   method.invoke(<Object>, <Parameters>); 
                } catch(NoSuchMethodException e) { 
                } catch(IllegalAccessException e) { 
                } catch(InvocationTargetException e) { 
                } 
                */ 
            }
}
