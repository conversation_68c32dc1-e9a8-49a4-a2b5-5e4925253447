package com.shuidihuzhu.workorder.service;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.admin.client.CfHospitalAuditClient;
import com.shuidihuzhu.client.cf.admin.model.HospitalAudit;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @DATE 2019/9/11
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class TestFeign {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private CfHospitalAuditClient auditClient;

    @Test
    public void test(){
        //33fbd8c9-0227-4931-9dc0-9119feab48ba
        Response<HospitalAudit> response = auditClient.getHospitalAudit("33fbd8c9-0227-4931-9dc0-9119feab48ba");

        log.info("kkkkkk " + JSON.toJSONString(response));
    }


}
