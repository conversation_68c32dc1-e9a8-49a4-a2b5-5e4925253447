package com.shuidihuzhu.workorder.service.impl; 

import com.shuidihuzhu.workorder.Application;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

/** 
* AssignWorkOrderServiceImpl Tester. 
* 
* <AUTHOR> 
* @since <pre>4月 25, 2021</pre> 
* @version 1.0 
*/ 
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@AutoConfigureMockMvc
public class AssignWorkOrderServiceImplTest { 

    @Autowired
    private AssignWorkOrderServiceImpl assignWorkOrderServiceImpl;
    @Autowired
    protected MockMvc mockMvc;

    @Before
    public void before() throws Exception { 
    } 
    
    @After
    public void after() throws Exception { 
    }

    @Test
    public void testTransferWorkOrder() {
        assignWorkOrderServiceImpl.transferWorkOrder(List.of(116214L), 1031L, 1030L, null, null);
    }
    
}
