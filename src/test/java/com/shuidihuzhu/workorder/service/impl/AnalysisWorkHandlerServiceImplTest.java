package com.shuidihuzhu.workorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderAnalysisVO;
import com.shuidihuzhu.workorder.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @author: fengxuan
 * @create 2019-11-22 19:33
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class AnalysisWorkHandlerServiceImplTest {

    @Autowired
    AnalysisWorkHandlerServiceImpl handlerService;

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Test
    public void wholeAnalysis() {
        List<WorkOrderAnalysisVO> analysisVOList = handlerService.wholeAnalysis();
        for (WorkOrderAnalysisVO analysisVO : analysisVOList) {
            System.out.println(JSON.toJSONString(analysisVO));
        }
    }

    @Test
    public void periodAnalysis() {
        List<WorkOrderAnalysisVO> analysisVOList = handlerService.periodAnalysis(1);
        for (WorkOrderAnalysisVO analysisVO : analysisVOList) {
            log.info("analysisVOList:{}", JSON.toJSONString(analysisVO));
        }
    }
}