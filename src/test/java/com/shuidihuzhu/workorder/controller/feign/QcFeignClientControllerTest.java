package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.QcHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.mq.QcWorkOrderAssignateImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class QcFeignClientControllerTest {

    @Resource(name = "qcWorkOrderServiceImpl")
    private WorkOrderFacade workOrderFacade;
    @Autowired
    private AssignWorkOrderService assignWorkOrderService;

    @Resource(name = "qcWorkOrderAssignateImpl")
    private QcWorkOrderAssignateImpl qcWorkOrderAssignateImpl;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void testCreateQcWorkOrder() {
        QcWorkOrder qcWorkOrder = new QcWorkOrder();
        qcWorkOrder.setCaseId(2289779);
        qcWorkOrder.setQcId(1);
        qcWorkOrder.setOrderType(WorkOrderType.qc_common.getType());
        qcWorkOrder.setHandleResult(HandleResultEnum.undoing.getType());
        qcWorkOrder.setComment("生成普通质检工单");
        workOrderFacade.doCreate(qcWorkOrder);
    }

    @Test
    public void testHandleQc() {
        QcHandleOrderParam qcHandleOrderParam = new QcHandleOrderParam();
        qcHandleOrderParam.setCaseId(2289782);
        qcHandleOrderParam.setWorkOrderId(35158);
        qcHandleOrderParam.setHandleResult(HandleResultEnum.later_doing.getType());
        qcHandleOrderParam.setOperComment(OrderExtName.endDealTime.getDesc());
        qcHandleOrderParam.setUserId(129);
        qcHandleOrderParam.setOrderType(WorkOrderType.qc_common.getType());
        OpResult opResult = workOrderFacade.doHandle(qcHandleOrderParam);
    }

    @Test
    public void testWorkOrderTransfer() {
        assignWorkOrderService.manualAssignWorkOrder(35158, 129, 129);
    }

    @Test
    public void testQcOrderlist() {
        WorkOrderListParam workOrderListParam = new WorkOrderListParam();
        workOrderListParam.setPageSize(5);
        workOrderListParam.setUserId(14492);
        workOrderListParam.setHandleResult("1");
        workOrderListParam.setOrderType(WorkOrderType.qc_common.getType());
        OpResult opResult = workOrderFacade.getOrderList(workOrderListParam);

    }

    @Test
    public void doAssignate() {
        //qcWorkOrderAssignateImpl.doAssignate(WorkOrderType.qc_common_repeat);
    }
}