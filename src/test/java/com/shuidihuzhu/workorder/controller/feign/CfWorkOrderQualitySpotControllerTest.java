package com.shuidihuzhu.workorder.controller.feign;
 
import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.model.QueryListParam;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.dao.CfWorkOrderQualitySpotDao;
import com.shuidihuzhu.workorder.service.impl.BaseWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
* MinaAnswerController Tester.
*
* <AUTHOR>
* @since <pre>Nov 15, 2019</pre>
* @version 1.0
*/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class CfWorkOrderQualitySpotControllerTest {

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Resource
    private CfWorkOrderQualitySpotDao cfWorkOrderQualitySpotDao;
    @Autowired
    private BaseWorkOrderServiceImpl baseWorkOrderService;

    @Before
    public void before() throws Exception {
    }
     
    @After
    public void after() throws Exception {
    }
     
    /**
     *
     * Method: listByUserIds(List<Long> userIds, Long scene)
     *
     */
    @Test
    public void testListByUserIdsForUserIdsScene() throws Exception {

        QueryListParam queryListParam = JSON.parseObject("{\"workOrderId\":\"68251\",\"userId\":\"\",\"orderType\":50,\"handleResult\":-1,\"createTimeStart\":\"\",\"createTimeEnd\":\"\",\"assignTimeStart\":\"\",\"assignTimeEnd\":\"\",\"handleTimeStart\":\"\",\"handleTimeEnd\":\"\",\"pageNum\":1,\"pageSize\":20,\"pageWorkOrderId\":0}",
                QueryListParam.class);
        fillWorkOrderStartAndEndTime(queryListParam);
        queryListParam.setOuterUser(checkOuterUser(queryListParam, 201));
        //queryListParam.setOuterUser(false);
        baseWorkOrderService.getWorkOrderListCount(queryListParam);
    }


    private boolean checkOuterUser(QueryListParam queryListParam,int userId){


        if (queryListParam.getWorkOrderId() > 0 || queryListParam.getCaseId() > 0 || org.apache.commons.lang3.StringUtils.isNotBlank(queryListParam.getMobile())){
            return false;
        }

        return true;

    }

    public void fillWorkOrderStartAndEndTime(QueryListParam queryListParam) {
        if (queryListParam == null) {
            return;
        }

        // 如果输入了 工单id、 案例id、 手机号 则不设置默认的创建时间
        if (queryListParam.getWorkOrderId() != 0 || queryListParam.getCaseId() != 0
                || StringUtils.isNotBlank(queryListParam.getMobile())) {
            return;
        }

        if (StringUtils.isBlank(queryListParam.getStartCreateTime())
                || StringUtils.isBlank(queryListParam.getEndCreateTime())) {

            Date now = new Date();
            queryListParam.setStartCreateTime(com.shuidihuzhu.common.util.DateUtil.getDate2LStr(DateUtils.addDays(now, -7)));
            queryListParam.setEndCreateTime(com.shuidihuzhu.common.util.DateUtil.getDate2LStr(now));
        }
    }

    /**
     *
     * Method: getByUserId(Long userId, Long scene)
     *
     */
    @Test
    public void testGetByUserIdForUserIdScene() throws Exception {
        RiskQualitySpotWorkOrderUserConfig config = cfWorkOrderQualitySpotDao.getByUserId(1L, 3L);
        System.out.println(JSON.toJSONString(config));
    }
     
    /**
     *
     * Method: save(RiskQualitySpotWorkOrderUserConfig config)
     *
     */
    @Test
    public void testSaveConfig() throws Exception {
        RiskQualitySpotWorkOrderUserConfig config = new RiskQualitySpotWorkOrderUserConfig();
        config.setUserId(1);
        config.setRuleInfo("{}");
        config.setScene(3);
        config.setRangeLimit(1);
        config.setDistributionType(1);
        cfWorkOrderQualitySpotDao.save(config);
    }
     
    /**
     *
     * Method: update(RiskQualitySpotWorkOrderUserConfig config)
     *
     */
    @Test
    public void testUpdateConfig() throws Exception {
        RiskQualitySpotWorkOrderUserConfig config = new RiskQualitySpotWorkOrderUserConfig();
        config.setId(1);
        config.setUserId(1);
        config.setRuleInfo("{}");
        config.setRangeLimit(1);
        config.setDistributionType(2);
        cfWorkOrderQualitySpotDao.update(config);
    }
     
     
}
