package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderReportStatisticsVo;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.service.WorkOrderDataStatisticsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class WorkOrderDataStatisticsDaoTest {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Resource
    private WorkOrderDataStatisticsDao workOrderDataStatisticsDao;

    @Autowired
    private WorkOrderReportStatisticsDao workOrderReportStatisticsDao;
    @Resource
    private WorkOrderDataStatisticsService workOrderDataStatisticsService;

    @Test
    public void testUpdateAssignCount() {
        int i = workOrderDataStatisticsDao.updateAssignCount(20200603, 17, 2, 1);
        System.out.println("======:" + i);

        int j = workOrderDataStatisticsDao.updateAssignCount(20200603, 17, 1, 1);
        System.out.println("======:" + j);
    }

    @Test
    public void testReport(){
//        workOrderReportStatisticsDao.updateAssignCount(20200805, 17, 23, 129);
//
//        workOrderReportStatisticsDao.updateDoneCount(20200805, 17, 23, 129);
//        List<WorkOrderReportStatisticsVo> reportUserDataStat = workOrderDataStatisticsService.getReportUserDataStat(List.of(538L,636l,1112l,1116l,1849l,129l,1848l,14352l,60112l), List.of(23,24));

                List<WorkOrderReportStatisticsVo> reportUserDataStat = workOrderDataStatisticsService.getReportUserDataByOrderType(60112l, List.of(23, 24));
//        workOrderDataStatisticsService.handleExtInfo(41403,"callUnicode","10.10.56.80-1597115611.43652");

    }
}
