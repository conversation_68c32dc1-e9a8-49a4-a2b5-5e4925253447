package com.shuidihuzhu.workorder.dao;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty;
import com.shuidihuzhu.workorder.Application;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @DATE 2019/1/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class testDao {

    static {
        System.setProperty("spring.cloud.consul.host","consul.bedin.shuiditech.com:80");
    }

    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private StaffStatusDao staffStatusDao;
    @Autowired
    private WorkTypePropertyService workPropertyService;

    static {
        System.setProperty("spring.cloud.consul.host", "consul.bedin.shuiditech.com:80");
    }

    @Test
    public void tet(){
//        WorkOrderBase base =  workOrderDao.getWorkOrderById(10006,1);
//        System.out.println("   999999    "+base);

        WorkOrderBase base = workOrderDao.getOneUrgentWorkOrder(1,0,7);
        System.out.println("   999999    "+base);

    }


    @Test
    public void testCountOfflineTodayRecord() {
        workPropertyService.selectByType(2, Lists.newArrayList(60),
                Lists.newArrayList(WorkTypeProperty.PropertyType.BATH_ON_LINE_ORDER_TYPE.getCode(),
                WorkTypeProperty.PropertyType.HANDLING_COUNT.getCode()));
    }
}
