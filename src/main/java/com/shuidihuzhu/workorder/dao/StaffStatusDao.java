package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatusRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.StaffStatusNum;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */


@DataSource("workOrderDataSource")
public interface StaffStatusDao {


    int changeStatus(StaffStatus staffStatus);


    StaffStatus getStaffStatus(@Param("userId") long userId,
                               @Param("orderType") int orderType);


    List<StaffStatus> getStaffsByStatus(@Param("userIds") List<Long> userIds,
                                        @Param("orderType") int orderType,
                                        @Param("staffStatus") int staffStatus);

    List<StaffStatus> getStaffsByStatusList(@Param("userIds") List<Long> userIds,
                                            @Param("orderType") int orderType,
                                            @Param("staffStatusList") List<Integer> staffStatusList);

    List<StaffStatus> getStaffs(@Param("userIds") List<Long> userIds,
                                @Param("orderTypes") List<Integer> orderTypes);

    int saveStaffStatusRecord(StaffStatus staffStatus);

    int countOfflineTodayRecord(@Param("userId") long userId, @Param("orderType") int orderType,
                                @Param("status") int status, @Param("operatorType") int operatorType,
                                @Param("beginTime") String beginTime);


    int addStaffAssignTime(@Param("userId") long userId, @Param("orderType") int orderType);


    long getStaffOrderByAssignTime(@Param("userIds") List<Long> users, @Param("orderType") int orderType);


    List<StaffStatusNum> getOnJobStaffStatusStat(@Param("orderType") List<Integer> orderType, @Param("userStatus") List<Integer> userStatus);


    List<StaffStatusNum> getStaffOnlineStatusStat(@Param("orderType") List<Integer> orderType, @Param("today") String today);

    int getDistinctUserOnlineCount(@Param("orderType") List<Integer> orderType, @Param("userIds") List<Long> userIds);


    List<StaffStatus> getOnJobStaffStatusList(@Param("orderTypes") List<Integer> orderType,
                                              @Param("pageSize") int pageSize,
                                              @Param("paging") String paging,
                                              @Param("userId") long userId,
                                              @Param("operatorId") long operatorId);


    List<StaffStatus> getStaffStatusByTypes(@Param("userId") long userId, @Param("orderType") List<Integer> types);


    List<Long> getUserIdByTypes(@Param("orderType") List<Integer> types);

    List<Long> getUserIdByTypesAndStatus(@Param("orderType") List<Integer> types, @Param("staffStatus") int staffStatus);


    List<StaffStatus> getAllOnJobByTypes(@Param("orderType") List<Integer> types);

    List<Long> getAllUserIdOnJobByTypes(@Param("orderType") List<Integer> types);

    int autoOff(@Param("userId") long userId, @Param("orderType") int orderType);


    List<StaffStatus> getStaffsByStatusAndTime(@Param("status") List<Integer> status, @Param("time") String time);

    List<StaffStatus> queryStaffsByStatusAndType(@Param("status") List<Integer> status, @Param("orderTypes") List<Integer> orderTypes);

    int updateAutoOnlineStatus(@Param("userId") int userId, @Param("orderType") int orderType,
                               @Param("autoStatus") int autoStatus);

    int updateLeaveOfficeStatus(@Param("userIds") Collection<Integer> userIds, @Param("onJob") int onJob);

    List<Integer> selectOnJobUsers();

    int clearAutoOfflineStatus();

    List<Integer> selectOrderTypeByUser(@Param("userId") int userId);

    List<StaffStatusRecord> getStaffStatusRecords(@Param("userId") long userId, @Param("orderType") int orderType, @Param("createTime") Date date);

    StaffStatusRecord getLastOneLtCreateTime(@Param("userId") long userId, @Param("orderType") int orderType, @Param("createTime") Date date);

    int allowAssign(@Param("userId") long userId,
                    @Param("allowAssign") String allowAssign);

    int updateThresholdByType(@Param("orderType") int orderType, @Param("threshold") int threshold);

    StaffStatus getThresholdByType(@Param("orderType") int orderType, @Param("userId") long userId);

    int updateJobAndStatusBatch(@Param("userIds") List<Long> userIds, @Param("orderTypes") List<Integer> orderTypes,
                                @Param("staffStatus") int staffStatus, @Param("onJob") int onJob);
}
