package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("workOrderDataSource")
public interface WorkTypePropertyDao {

    int addPropertyList(@Param("propertyList") List<WorkTypeProperty> propertyList);


    List<WorkTypeProperty> selectByPropertyTypes(@Param("orderLevel") int orderLevel, @Param("orderTypes") List<Integer> orderTypes,
                                                @Param("propertyTypes") List<Integer> propertyType);


    int deleteWorkOrderPropertys(@Param("orderLevel") int orderLevel, @Param("orderTypes") List<Integer> orderTypes,
                                @Param("propertyTypes") List<Integer> propertyType);
}
