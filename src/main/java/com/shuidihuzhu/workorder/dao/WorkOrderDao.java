package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.RawWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.model.WorkOrderMini;
import com.shuidihuzhu.workorder.model.order.*;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@DataSource("workOrderDataSource")
public interface WorkOrderDao {

    int createWorkOrder(WorkOrderBase workOrder);


    int assignWorkOrder(@Param("id") long id,
                        @Param("operatorId") long operatorId,
                        @Param("operatorOrgId") int operatorOrgId,
                        @Param("handleResult") int handleResult);

    int assignWorkOrders(@Param("ids") List<Long> ids,
                         @Param("operatorId") long operatorId,
                         @Param("operatorOrgId") int operatorOrgId,
                         @Param("handleResult") int handleResult);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getWorkorderList(@Param("operatorIds") List<Long> operatorId,
                                         @Param("orderType") int orderType,
                                         @Param("handleResult") List<Integer> handleResult,
                                         @Param("pageSize") int pageSize,
                                         @Param("workOrderId") long workOrderId,
                                         @Param("paging") String paging,
                                         @Param("caseId") int caseId,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("orderLevel") int orderLevel);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getWorkOrderListByCaseIds(@Param("operatorIds") List<Long> operatorId,
                                                  @Param("orderType") int orderType,
                                                  @Param("handleResult") List<Integer> handleResult,
                                                  @Param("pageSize") int pageSize,
                                                  @Param("workOrderId") long workOrderId,
                                                  @Param("paging") String paging,
                                                  @Param("caseId") List<Integer> caseIds,
                                                  @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime,
                                                  @Param("orderLevel") int orderLevel);

    List<WorkOrderDoingCount> getDoingCount(@Param("operatorIds") List<Long> operatorId,
                                            @Param("orderType") int orderType,
                                            @Param("handleResult") int handleResult);

//    @DataSource(WorkOrderConfig.Ds.SLAVE)
    List<WorkOrderDoingCount> getDoingCountByOrderTypes(@Param("operatorIds") List<Long> operatorId,
                                                        @Param("orderTypes") List<Integer> orderTypes,
                                                        @Param("handleResult") int handleResult);

    List<WorkOrderAssignedCount> queryAssignedCount(@Param("operatorIds") List<Long> operatorIds,
                                                    @Param("orderType") int orderType,
                                                    @Param("handleTime") String handleTime);


    int getCountByHandleResult(@Param("operatorId") long operatorId,
                               @Param("orderType") int orderType,
                               @Param("handleResult") List<Integer> handleResult,
                               @Param("handleFinish") int handleFinish,
                               @Param("dayOfZero") Date dayOfZero);


    int getAllCountByHandleResult(@Param("orderType") int orderType,
                                  @Param("handleResult") int handleResult);

    int getAllCountByHandleResultAndGroupId(@Param("orderType") int orderType,
                                            @Param("handleResult") int handleResult,
                                            @Param("assignGroupId") long assignGroupId
    );


    List<WorkOrderBase> getWorkorderListByUser(@Param("operatorId") long operatorId,
                                               @Param("orderType") int orderType,
                                               @Param("handleResult") List<Integer> handleResults);


    int freeWorkOrder(@Param("wrokOrderIds") List<Long> wrokOrderIds, @Param("oldResult") int oldResult, @Param("newResult") int newResult);

    int freeWorkOrderWithNoCheckOldStatus(@Param("wrokOrderIds") List<Long> wrokOrderIds, @Param("newResult") int newResult);


    int handle(@Param("wrokOrderId") long workId, @Param("handleResult") int handleResult);

    int handleIsCheckStatus(@Param("wrokOrderId") long workId, @Param("handleResult") int handleResult, @Param("operatorId") long operatorId, @Param("checkOperator") boolean checkOperator);

    int updateFinishTime(@Param("workOrderId") long workId);

    int reportHandle(@Param("wrokOrderId") long workId, @Param("handleResult") int handleResult);

    int handleBatch(@Param("wrokOrderIds") List<Long> workIds, @Param("handleResult") int handleResult);

    int updateHandleResultByIds(@Param("workOrderIds") List<Long> workIds,
                                @Param("handleResult") int handleResult,
                                @Param("preHandleResultList") List<Integer> preHandleResultList);

    WorkOrderBase getOneUrgentWorkOrder(@Param("orderType") int orderType, @Param("handleResult") int handleResult, @Param("orderLevel") int orderLevel);


    List<WorkOrderBase> queryUndoOrderOrderByLevelAndTime(@Param("orderType") int orderType, @Param("handleResult") int handleResult,
                                                          @Param("createTime") String createTime, @Param("limit") Integer limit);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    List<WorkOrderBase> getUndoOrderList(@Param("orderType") int orderType, @Param("handleResult") int handleResult, @Param("limit") int limit);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    List<WorkOrderBase> getUndoGroupOrderList(@Param("orderType") int orderType,
                                              @Param("handleResult") int handleResult,
                                              @Param("limit") int limit,
                                              @Param("groupId") long groupId);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    List<WorkOrderBase> getUndoGroupOrderListByTime(@Param("orderType") int orderType,
                                                    @Param("handleResult") int handleResult,
                                                    @Param("limit") int limit,
                                                    @Param("groupId") long groupId,
                                                    @Param("dateTime") Timestamp dateTime);

    @DataSource("workOrderDataSourceSlave")
    WorkOrderBase recentAssignedOnExt(@Param("caseId") int caseId, @Param("orderType") int orderType, @Param("extName") String extName, @Param("extValues") Collection<String> extValues);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> queryUndoOrderExistsExtOrderByLevelAndTime(@Param("orderType") int orderType, @Param("createTime") String createTime,
                                                                   @Param("extName") String extName, @Param("extValue") String extValue, @Param("limit") int limit);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> queryUndoOrderOrderByLevelAndTimeFromTidb(@Param("orderType") int orderType, @Param("handleResult") int handleResult,
                                                                  @Param("createTime") String createTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBaseVo> queryUndoOrderOrderByLevel(@Param("orderType") int orderType,
                                                     @Param("handleResult") int handleResult);

    WorkOrderBase getWorkOrderBycaseIdAndTypes(@Param("caseId") int caseId,
                                               @Param("types") List<Integer> types,
                                               @Param("results") List<Integer> results);

    int updateOrdertype(@Param("id") long id, @Param("ordertype") int orderType, @Param("results") List<Integer> results);

    int updateOperator(@Param("workOrderId") long workOrderId, @Param("operatorId") long operatorId, @Param("operatorOrgId") int operatorOrgId);

    int getOrderCount(@Param("orderTypes") List<Integer> orderTypes, @Param("results") List<Integer> results, @Param("time") String time);

    int getCaseOrderCountByType(@Param("caseId") int caseId, @Param("orderType") Integer orderType);


    WorkOrderBase getWorkOrderById(@Param("wrokOrderId") long workId);

    List<WorkOrderBase> getWorkOrderListByIds(@Param("workOrderIds") List<Long> workOrderIds);


    int closeWorkOrderByCaseId(@Param("caseId") int caseId,
                               @Param("orderTypes") List<Integer> orderType,
                               @Param("handleResult") int HandleResult);


    int closeOrderById(@Param("ids") List<Long> ids, @Param("handleResult") int HandleResult);


    List<WorkOrderBase> getOrderByCaseId(@Param("caseId") int caseId,
                                         @Param("orderTypes") List<Integer> orderType,
                                         @Param("handleResults") List<Integer> handleResults);

    WorkOrderBase getOrderByTypeAndTime(@Param("caseId") int caseId, @Param("orderType") int orderType, @Param("time") String time);

    List<WorkOrderBase> listOrderByTypeAndTime(@Param("caseId") int caseId, @Param("orderType") int orderType, @Param("startTime") Date startTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getAllWorkorderList(@Param("operatorId") long operatorId,
                                            @Param("orderTypes") List<Integer> orderType,
                                            @Param("pageSize") int pageSize,
                                            @Param("handleResult") int handleResult,
                                            @Param("workOrderId") long workOrderId,
                                            @Param("pageWorkOrderId") long pageWorkOrderId,
                                            @Param("paging") String paging,
                                            @Param("caseIds") List<Integer> caseIds,
                                            @Param("startCreateTime") String startCreateTime,
                                            @Param("endCreateTime") String endCreateTime,
                                            @Param("startHandleTime") String startHandleTime,
                                            @Param("endHandleTime") String endHandleTime,
                                            @Param("startDoneTime") String startDoneTime,
                                            @Param("endDoneTime") String endDoneTime,
                                            @Param("outerUser") int outerUser,
                                            @Param("forceIndex") String forceIndex,
                                            @Param("belonger") long belonger);


    int changeOrderLevel(@Param("workOrderId") long workOrderId, @Param("orderLevel") int orderLevel);


    WorkOrderBase getOrderBycaseIdAndType(@Param("caseId") int caseId, @Param("orderType") int orderType);

    List<WorkOrderBase> listByCaseId(@Param("caseId") int caseId, int orderType);

    WorkOrderBase getLastByCaseIdAndOrderType(@Param("caseId") int caseId, @Param("orderType") int orderType);

    WorkOrderBase getLastByCaseIdAndOrderTypes(@Param("caseId") int caseId, @Param("orderTypes") List<Integer> orderTypes);

    List<WorkOrderBase> listByCaseIdAndTypeAndResult(int caseId, List<Integer> orderTypes, List<Integer> results);

    List<WorkOrderMini> listCaseIdsByTypeAndCount(@Param("caseIds") List<Integer> caseIds, @Param("orderType") Integer orderType);

    List<WorkOrderBase> queryByCaseAndTypes(@Param("caseId") int caseId, @Param("orderTypes") List<Integer> orderTypes);

    List<WorkOrderBase> listById(@Param("ids") List<Long> ids);

    int countForAnalysis(@Param("orderTypes") List<Integer> orderTypes, @Param("results") List<Integer> results,
                         @Param("startTime") String startTime, @Param("endTime") String endTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> analysisWholeDayData(@Param("startTime") String startTime, @Param("endTime") String endTime);

    //获取环比数据
    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getChainIndexData(@Param("startTime") String startTime, @Param("endTime") String endTime,
                                          @Param("handleStart") String handleStart, @Param("handleEnd") String handleEnd);


    int reportTransfer(@Param("ids") List<Long> ids, @Param("orderType") int orderType, @Param("operatorId") int operatorId, @Param("operatorOrgId") int operatorOrgId);


    List<WorkOrderBase> getWorkordesByUser(@Param("operatorIds") List<Long> operatorIds,
                                           @Param("orderType") int orderType,
                                           @Param("handleResult") List<Integer> handleResults,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> listWorkFor1v1(@Param("operatorId") long operatorId,
                                       @Param("orderType") int orderType,
                                       @Param("startTime") String startTime,
                                       @Param("endTime") String endTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> listByCaseIdsAndTypes(@Param("caseIds") List<Integer> caseIds,
                                              @Param("orderTypes") List<Integer> orderTypes);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> queryToDay(@Param("operatorIds") List<Long> operatorIds,
                                   @Param("orderTypes") List<Integer> orderTypes,
                                   @Param("handleResults") List<Integer> handleResults,
                                   @Param("handleTime") Date handleTime,
                                   @Param("finishTime") Date finishTime);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderDoingCount> queryCountByHandleResult(@Param("operatorIds") List<Long> operatorId,
                                                       @Param("orderTypes") List<Integer> orderTypes,
                                                       @Param("handleResult") int handleResult);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderStatisticalCount> queryCountGroupByOrderType(@Param("operatorId") long operatorId,
                                                               @Param("orderTypes") List<Integer> orderTypes,
                                                               @Param("handleResult") int handleResult);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> queryListByBatch(@Param("id") long id, @Param("handleResult") int handleResult,
                                         @Param("endTime") String endTime, @Param("orderType") int orderType,
                                         @Param("limit") int limit);

    int deleteWorkOrderByIdAndOrderType(@Param("id") long id);


    int updateHandleResultByIdAndOrderType(@Param("id") long id, @Param("handleResult") int handleResult,
                                           @Param("orderType") int orderType);


    List<WorkOrderBaseVo> getDoingOrders(@Param("operatorIds") List<Long> operatorId,
                                         @Param("orderType") int orderType,
                                         @Param("handleResult") int handleResult);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> findByFinishTime(@Param("orderType") int orderType, @Param("pageSize") int pageSize,
                                         @Param("currentWorkOrderId") long currentWorkOrderId,
                                         @Param("finishStartTime") String finishStartTime,
                                         @Param("finishEndTime") String finishEndTime,
                                         @Param("handleResult") Integer handleResult);

    int assignPrWorkOrder(@Param("id") long id,
                          @Param("operatorId") long operatorId,
                          @Param("operatorOrgId") int operatorOrgId,
                          @Param("handleResult") int handleResult);

    List<RawWorkOrder> listRawByIds(List<Long> ids);

    int updateWorkOrderOperatorId(@Param("workOrderIds") List<Long> workOrderIds,
                                  @Param("operatorId") Long operatorId,
                                  @Param("operatorOrgId") int operatorOrgId);

    int transferWorkOrders(@Param("ids") Collection<Long> ids,
                           @Param("operatorId") long operatorId,
                           @Param("operatorOrgId") int operatorOrgId,
                           @Param("handleResult") Integer handleResult);

    @DataSource(WorkOrderConfig.Ds.SLAVE)
    Long getLastOperatorIdByCaseIdAndType(@Param("caseId") int caseId,
                                          @Param("types") List<Integer> types);


    @DataSource(WorkOrderConfig.Ds.SLAVE)
    int getCountByOperatorIdAndHandleResult(@Param("operatorId") long operatorId,
                                            @Param("orderType") int orderType,
                                            @Param("handleResult") int handleResult);

    WorkOrderBase recentAssignedOrder(@Param("caseId") int caseId, @Param("workOrderType") int workOrderType);

    int callbackOrder(@Param("workOrderIds") List<Long> workOrderIds,
                      @Param("sourceStatusList") List<Integer> sourceStatusList,
                      @Param("targetStatus") int targetStatus);

    @DataSource(WorkOrderConfig.Ds.SLAVE)
    List<VonDoingCountTypeVO> getTypeWithCountByUserAndHandleResult(@Param("operatorId") long operatorId,
                                                                    @Param("orderTypes") List<Integer> orderTypes,
                                                                    @Param("handleResult") int handleResult);


    @DataSource("workOrderDataSourceSlave")
    List<Long> getIdsByTypeResultTime(@Param("orderTypes") List<Integer> orderTypes,
                                      @Param("results") List<Integer> results,
                                      @Param("createTime") Date createTime);

    @DataSource("workOrderDataSourceSlave")
    List<Long> getIdsByTypeResultBetweenTime(@Param("orderTypes") List<Integer> orderTypes,
                                             @Param("results") List<Integer> results,
                                             @Param("createTime") Date createTime,
                                             @Param("endTime") Date endTime);

    @DataSource("workOrderDataSourceSlave")
    List<OrderUserHandleResultCount> getCountByUserIdAndHandleResult(
            @Param("orderType") int orderType,
            @Param("userId") long userId,
            @Param("handleResultList") List<Integer> handleResultList);
}
