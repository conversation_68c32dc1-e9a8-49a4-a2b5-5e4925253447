package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/3/25
 */
@DataSource("workOrderDataSource")
public interface UgcWorkOrderDao {

    int handle(@Param("wrokOrderId") long workId,
               @Param("operatorId") long operatorId,
               @Param("handleResult") int handleResult);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getWorkorderList(@Param("operatorIds") List<Long> operatorId,
                                         @Param("orderType") int orderType,
                                         @Param("handleResult") List<Integer> handleResult,
                                         @Param("pageSize") int pageSize,
                                         @Param("workOrderId") long workOrderId,
                                         @Param("paging") String paging,
                                         @Param("caseId") int caseId,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("orderLevel") int orderLevel,
                                         @Param("reasonCode") String reasonCode);

}
