package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/22
 */
@DataSource("workOrderDataSource")
public interface ShouciWorkOrderDao {

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBaseVo> getWorkorderList(@Param("operatorId") Long operatorId,
                                           @Param("orderType") int orderType,
                                           @Param("pageSize") int pageSize,
                                           @Param("limit") int limit,
                                           @Param("caseId") int caseId,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("callStatus") String callStatus,
                                           @Param("handleResults") List<Integer>  HandleResult);


}
