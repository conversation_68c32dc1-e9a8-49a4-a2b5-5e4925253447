package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@DataSource("workOrderDataSource")
public interface CailiaoWorkOrderDao {


    int handle(@Param("wrokOrderId") long workId, @Param("handleResult") int handleResult);


}
