package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
@DataSource("workOrderDataSource")
public interface UserOperationRecordDao {

    int saveRecord(WorkOrderRecord workOrderRecord);


    int saveRecordList(@Param("workOrderRecords") List<WorkOrderRecord> workOrderRecords);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderRecord> listByWorkOrderId(long workOrderId);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderRecord> listByWorkOrderIdsAndOptModes(@Param("workOrderIds") List<Long> workOrderIds, @Param("optModes") List<Integer> optModes);
}
