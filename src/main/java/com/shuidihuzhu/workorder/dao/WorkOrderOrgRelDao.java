package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderOrgRel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-08-25 14:33
 **/
@DataSource("workOrderDataSource")
public interface WorkOrderOrgRelDao {

   int addList(@Param("workOrderOrgRels") List<WorkOrderOrgRel> workOrderOrgRels);

   List<WorkOrderOrgRel> getByWorkOrderId(long workOrderId);

}
