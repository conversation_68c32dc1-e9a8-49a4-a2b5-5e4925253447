package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("workOrderDataSource")
public interface WorkOrderOneTypeDAO {

    int insertWorkOrder(WorkOrderOneTypeDO workOrderOneTypeDO);

    WorkOrderOneTypeDO getByOneTypeCode(@Param("oneTypeCode") int oneTypeCode);

    List<WorkOrderOneTypeDO> getAllOrderType();


}
