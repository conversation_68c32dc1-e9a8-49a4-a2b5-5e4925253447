package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/6/20
 */
@DataSource("workOrderDataSource")
public interface ChuciWorkOrderDao {

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderBase> getWorkorderList(@Param("operatorIds") List<Long> operatorId,
                                         @Param("orderType") int orderType,
                                         @Param("handleResult") List<Integer> handleResult,
                                         @Param("pageSize") int pageSize,
                                         @Param("workOrderId") long workOrderId,
                                         @Param("paging") String paging,
                                         @Param("caseId") int caseId,
                                         @Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("orderLevel") int orderLevel,
                                         @Param("forceIndex") String forceIndex);
}
