package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@DataSource("workOrderDataSource")
public interface WorkOrderStatDao {



    int getRemainingAmount(@Param("types") List<Integer> types,
                           @Param("results") List<Integer> results,
                           @Param("time") String time);

    int getNewAmount(@Param("types") List<Integer> types,
                     @Param("time") String time);


    int getUndoingAmount(@Param("types") List<Integer> types);


    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderStat> getResultStat(@Param("types") List<Integer> types,
                                      @Param("users") List<Long> users,
                                      @Param("handleResult")int handleResult,
                                      @Param("time") String time);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderStat> getLaterStat(@Param("types") List<Integer> types,
                                     @Param("users") List<Long> users,
                                     @Param("start") String start,
                                     @Param("end") String end);

    @Deprecated
    List<WorkOrderStat> getCailiaoStat(@Param("types") List<Integer> types,
                                       @Param("auditStatus") int auditStatus,
                                       @Param("time") String time);
    @Deprecated
    List<WorkOrderStat> getCailiaoUserStat(@Param("types") List<Integer> types,
                                           @Param("auditStatus") int auditStatus,
                                           @Param("users") List<Long> users,
                                           @Param("time") String time);
    @Deprecated
    List<WorkOrderStat> getCailiaoTypeStat(@Param("types") List<Integer> types,
                                           @Param("auditStatus") int auditStatus,
                                           @Param("user") long user,
                                           @Param("time") String time);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderStat> getExtStat(@Param("types") List<Integer> types,
                                   @Param("extName") String extName,
                                   @Param("extValue") String extValue,
                                   @Param("time") String time);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderStat> getExtUserStat(@Param("types") List<Integer> types,
                                       @Param("users") List<Long> users,
                                       @Param("extName") String extName,
                                       @Param("extValue") String extValue,
                                       @Param("time") String time);


    List<WorkOrderStat> getExtTypeStat(@Param("types") List<Integer> types,
                                       @Param("user") long user,
                                       @Param("extName") String extName,
                                       @Param("extValue") String extValue,
                                       @Param("time") String time);


    int getTypeAndResult(@Param("orderTypes") List<Integer> orderTypes,
                         @Param("handleResults") List<Integer> handleResults,
                         @Param("startTime") String startTime,
                         @Param("endTime") String endTime,
                         @Param("statType") int statType,
                         @Param("userIdLimit") int userIdLimit,
                         @Param("userIds") List<Long> userIds);


    int getExtTypeStatNum(@Param("orderTypes") List<Integer> orderTypes,
                          @Param("extName") String extName,
                          @Param("extValues") List<String> extValues,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime,
                          @Param("statType") int statType,
                          @Param("userIdLimit") int userIdLimit,
                          @Param("userIds") List<Long> userIds);

    List<WorkOrderBase> getOrderData(@Param("startTime") String startTime,
                                     @Param("endTime") String endTime,
                                     @Param("orderTypes") List<Integer> orderTypes,
                                     @Param("statType") int statType,
                                     @Param("userIdLimit") int userIdLimit,
                                     @Param("userIds") List<Long> userIds);


    int getTypeAndAssign(@Param("orderTypes") List<Integer> orderTypes,
                         @Param("startTime") String startTime,
                         @Param("endTime") String endTime,
                         @Param("statType") int statType,
                         @Param("userIdLimit") int userIdLimit,
                         @Param("userIds") List<Long> userIds);



}
