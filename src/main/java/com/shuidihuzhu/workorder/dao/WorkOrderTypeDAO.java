package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("workOrderDataSource")
public interface WorkOrderTypeDAO {
    int insertWorkOrder(WorkOrderTypeDO workOrderTypeDO);

    WorkOrderTypeDO getByOrderTypeCode(@Param("orderType") int orderType);

    List<WorkOrderTypeDO> getAllOrderType();

    List<WorkOrderTypeDO> getAllOrderTypeByOneTypeCode(@Param("relateId") int relateId);

    WorkOrderTypeDO getByPermission(@Param("permission") String permission);
}
