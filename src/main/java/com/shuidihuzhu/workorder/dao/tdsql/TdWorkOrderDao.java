package com.shuidihuzhu.workorder.dao.tdsql;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.order.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource("shuidiCfAdminTdDataSource")
public interface TdWorkOrderDao {

    int getAllWorkorderCount(@Param("operatorId") long operatorId,
                             @Param("orderTypes") List<Integer> orderType,
                             @Param("handleResult") int handleResult,
                             @Param("workOrderId") long workOrderId,
                             @Param("caseIds") List<Integer> caseIds,
                             @Param("startCreateTime") String startCreateTime,
                             @Param("endCreateTime") String endCreateTime,
                             @Param("startHandleTime") String startHandleTime,
                             @Param("endHandleTime") String endHandleTime,
                             @Param("startDoneTime") String startDoneTime,
                             @Param("endDoneTime") String endDoneTime,
                             @Param("outerUser") int outerUser,
                             @Param("belonger") long belonger);

    Long getMinIdQcByBatchV3(@Param("orderType") int orderType,
                             @Param("handleResult") Integer handleResult,
                             @Param("finishStartTime") String finishStartTime,
                             @Param("finishEndTime") String finishEndTime);

    List<WorkOrderDoingCount> staffHandleCount(@Param("orderType") int orderType,
                                               @Param("handleResult") int handleResult);


    List<VonDoingCountTypeVO> getTodayTypeWithCountByUserAndHandleResult(@Param("operatorId") long operatorId,
                                                                         @Param("orderTypes") List<Integer> orderTypes,
                                                                         @Param("handleResults") List<Integer> handleResults);

}
