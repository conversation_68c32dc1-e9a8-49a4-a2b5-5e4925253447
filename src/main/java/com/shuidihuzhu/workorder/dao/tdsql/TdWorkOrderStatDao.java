package com.shuidihuzhu.workorder.dao.tdsql;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("shuidiCfAdminTdDataSource")
public interface TdWorkOrderStatDao {

    int getResultAmount(@Param("types") List<Integer> types,
                        @Param("results") List<Integer> result,
                        @Param("time") String time,
                        @Param("userId") long userId);

}
