package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTimeline;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @DATE 2019/7/5
 */
@DataSource("workOrderDataSource")
public interface WorkOrderTimelineDao {


    int insertTimeLine (WorkOrderTimeline workOrderTimeline);


    WorkOrderTimeline getWorkOrderTimeline(@Param("workOrderId") long workOrderId,
                                           @Param("workOrderType") int orderType,
                                           @Param("workResult") int orderResult);

}
