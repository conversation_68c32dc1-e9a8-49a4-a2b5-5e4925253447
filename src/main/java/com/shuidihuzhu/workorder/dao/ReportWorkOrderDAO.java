package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/12/31 下午2:21
 * @desc
 */
@DataSource("workOrderDataSource")
public interface ReportWorkOrderDAO {
    List<WorkOrderBase> queryWorkOrderList(@Param("operatorIds") List<Long> operatorIds,
                                           @Param("orderType") int orderType,
                                           @Param("handleResult") List<Integer> handleResult,
                                           @Param("pageSize") int pageSize,
                                           @Param("workOrderId") long workOrderId,
                                           @Param("paging") String paging,
                                           @Param("startTime") String startTime,
                                           @Param("endTime") String endTime,
                                           @Param("caseId") int caseId);
}
