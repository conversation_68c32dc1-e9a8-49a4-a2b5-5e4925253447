package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@DataSource("workOrderDataSource")
public interface WorkOrderDaoExt {

    int createWorkOrderExt(@Param("workOrderExts") List<WorkOrderExt> workOrderExts);

    int updateWorkOderExt(@Param("workOrderId") long workOrderId, @Param("materialAuditStatus") int materialAuditStatus);

    int updateByNameValue(@Param("workOrderId") long workOrderId, @Param("extName") String extName, @Param("extValue") String extValue);

    int delete(@Param("workOrderId") long workOrderId, @Param("extName") String extName);

    WorkOrderExt getWorkOrderExt(@Param("workOrderId") long workOrderId, @Param("extName") String extName);

    WorkOrderExt getWorkOrderExtDesc(@Param("workOrderId") long workOrderId, @Param("extName") String extName);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtByName(@Param("workOrderId") long workOrderId, @Param("extNames") List<String> extNames);

    List<WorkOrderExt> getWorkOrderExtByNameMaster(@Param("workOrderId") long workOrderId, @Param("extNames") List<String> extNames);


    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExts(@Param("workOrderIds") List<Long> workOrderId, @Param("extName") String extName);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtList(@Param("workOrderIds") List<Long> workOrderId, @Param("extName") String extName, @Param("extValue") String extValue);

    int insertWorkOrderExt(@Param("workOrderId") long workOrderId, @Param("extName") String extName, @Param("extValue") String extValue);

    int insertWorkOrderExtGetId(WorkOrderExt workOrderExt);

    int updateExtValue(@Param("workOrderIds") List<Long> workOrderIds, @Param("extName") String extName, @Param("extValue") String extValue);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtIgnoreDelete(@Param("workOrderId") long workOrderId, @Param("extName") String extName);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getAllOrderExtIgnoreDelete(@Param("workOrderId") long workOrderId, @Param("extNames") List<String> extNames);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> listOrderExtByIdsAndExtNames(@Param("workOrderIds") List<Long> workOrderIds, @Param("extNames") List<String> extNames);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtNoDelete(@Param("workOrderId") long workOrderId, @Param("extName") String extName);


    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtByWorkOrderId(@Param("workOrderId") long workOrderId);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderExt> getWorkOrderExtByWorkOrderIds(@Param("workOrderIds") List<Long> ids);
}
