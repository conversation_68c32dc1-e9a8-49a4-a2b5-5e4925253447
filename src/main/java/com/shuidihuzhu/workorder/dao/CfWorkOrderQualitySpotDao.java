package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.model.Response;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/23
 */
@DataSource("workOrderDataSource")
public interface CfWorkOrderQualitySpotDao {

    List<RiskQualitySpotWorkOrderUserConfig> listByUserIds(@Param("userIds") List<Long> userIds, @Param("scene") Long scene);

    RiskQualitySpotWorkOrderUserConfig getByUserId(@Param("userId") Long userId, @Param("scene") Long scene);

    int save(RiskQualitySpotWorkOrderUserConfig config);

    int update(RiskQualitySpotWorkOrderUserConfig config);

}
