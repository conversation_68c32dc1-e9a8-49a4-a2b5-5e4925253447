package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.WorkOrderDataStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("workOrderDataSource")
public interface WorkOrderDataStatisticsDao {

    int updateAssignCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateManualLockCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateExceptionDoneCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateStopCaseCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateAuditPassCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateAuditRejectCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateWithinThreeMinutesCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateOutsideThreeMinutesCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateWithinTenMinutesCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateOutsideTenMinutesCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateCallCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateDoneCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateHandleManualLockCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);
    int updateEffectiveCallCount(@Param("statDay") int statDay, @Param("statHour") int statHour, @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderDataStatistics> getByUserIdAndOrderType(@Param("statDay") int statDay, @Param("orderType") int orderType, @Param("userId") long userId);

    @DataSource("workOrderDataSourceSlave")
    List<WorkOrderDataStatistics> getByUserIdsAndOrderType(@Param("statDay") int statDay, @Param("orderType") int orderType, @Param("userIds") List<Long> userIds);

    @DataSource("workOrderDataSourceSlave")
    WorkOrderDataStatistics getSumByUserIdsAndOrderType(@Param("statDay") int statDay, @Param("orderType") int orderType, @Param("userIds") List<Long> userIds);
}
