package com.shuidihuzhu.workorder.dao;


import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.model.order.WorkOrderReportStatistics;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("workOrderDataSource")
public interface WorkOrderReportStatisticsDao {

    int updateAssignCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                          @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateOfflineCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                           @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateDoneCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                        @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateCallCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                        @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateCallDuration(@Param("statDay") int statDay, @Param("statHour") int statHour,
                           @Param("orderType") int orderType, @Param("operatorId") long operatorId, @Param("duration") int duration);

    int updateEndDealCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                           @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateNoneedDealCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                              @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateUpgradeCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                           @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    int updateLostCount(@Param("statDay") int statDay, @Param("statHour") int statHour,
                        @Param("orderType") int orderType, @Param("operatorId") long operatorId);

    List<WorkOrderReportStatistics> getSumByUserIdsAndOrderType(@Param("statDay") int statDay, @Param("orderTypes") List<Integer> orderTypes,
                                                                @Param("userIds") List<Long> userIds);


    List<WorkOrderReportStatistics> getByUserIdAndOrderType(@Param("statDay") int statDay, @Param("orderTypes") List<Integer> orderTypes,
                                                            @Param("userId") long userId);


}
