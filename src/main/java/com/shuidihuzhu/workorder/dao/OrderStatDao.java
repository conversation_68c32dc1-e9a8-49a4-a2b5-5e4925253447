package com.shuidihuzhu.workorder.dao;

import com.shuidihuzhu.client.cf.workorder.model.OrderStat;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/6/20
 */
@DataSource("workOrderDataSource")
public interface OrderStatDao {

    int save(OrderStat orderStat);

    int saveBatch(@Param("orderStatList") List<OrderStat> orderStatList);

    @DataSource("workOrderDataSourceSlave")
    OrderStat getByBatchHourAndBatchTag(@Param("batchHour") int batchHour,
                                        @Param("batchTag") String batchType);
    @DataSource("workOrderDataSourceSlave")
    List<OrderStat> getStatList(@Param("batchHours") List<Integer> batchHours,
                                @Param("statType") int statType);
}
