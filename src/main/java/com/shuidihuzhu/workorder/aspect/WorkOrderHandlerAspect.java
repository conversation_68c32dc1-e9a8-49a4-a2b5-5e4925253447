package com.shuidihuzhu.workorder.aspect;

import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderMonitorConst;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/5/21 11:27
 */
@Slf4j
@Aspect
@Component
public class WorkOrderHandlerAspect {

    @Resource
    private MeterRegistry meterRegistry;

    @Around(value="execution(* com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade.doAssignate(..))")
    public Object assignAspect(ProceedingJoinPoint jp) throws Throwable{
        String orderType = Optional.ofNullable(jp.getArgs()).filter(array -> array.length > 0)
                .map(array -> array[0])
                .map(Object::toString).orElse("unknown");
        try {
            Object result = jp.proceed();
            if (result instanceof OpResult && ((OpResult<?>) result).isFail()) {
                if (Optional.ofNullable(((OpResult<?>) result).getErrorCode()).map(ErrorCode::getCode)
                        .filter(code -> code == ErrorCode.SYSTEM_ERROR.getCode()).isPresent()) {
                    reportExp(orderType, "assign", "SystemError");
                }
            }
            return result;
        } catch (Throwable throwable) {
            reportExp(orderType, "assign", throwable.getClass().getSimpleName());
            throw throwable;
        }
    }

    @Around(value="execution(* com.shuidihuzhu.workorder.controller.feign.PrOnlineWorkOrderClientController.*(..)) ||" +
            "execution(* com.shuidihuzhu.workorder.controller.feign.PrWorkOrderClientController.*(..))")
    public Object handleAspect(ProceedingJoinPoint jp) throws Throwable{
        try {
            return jp.proceed();
        } catch (Throwable throwable) {
            String orderType = Optional.ofNullable(jp.getArgs()).filter(array -> array.length > 0)
                    .map(array -> {
                        if (array[0] instanceof HandleOrderParam) {
                            return (HandleOrderParam)array[0];
                        }
                        return null;
                    })
                    .map(Object::toString).orElse("pr_work_order");
            reportExp(orderType, "handle", jp.getSignature().getName()+"."+throwable.getClass().getSimpleName());
            throw throwable;
        }
    }

    private void reportExp(String orderType, String phase, String extName) {
        try {
            meterRegistry.counter(
                    WorkOrderMonitorConst.WorkOrderAssign.WOEKORDER_EXP_STAT,
                    WorkOrderMonitorConst.WorkOrderAssign.ORDER_TYPE, orderType,
                    WorkOrderMonitorConst.WorkOrderAssign.EXP_NAME, extName,
                    WorkOrderMonitorConst.WorkOrderAssign.PHASE, phase
            ).increment();
        } catch (Exception e) {
            log.error("", e);
        }
    }

}
