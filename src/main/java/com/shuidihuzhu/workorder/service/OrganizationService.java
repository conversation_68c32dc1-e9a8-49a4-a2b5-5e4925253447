package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.feign.GroupFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleGroupVo;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/25
 */

@Slf4j
@Service
public class OrganizationService {

    private static final int MAX_ORGANIZATION = 128;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private GroupFeignClient groupFeignClient;



    public int getUserOrgId(long userId) {
        AuthGroupDto adminOrganization = getUserOrgInfo(userId);
        Long orgId = Optional.ofNullable(adminOrganization).map(AuthGroupDto::getGroupBizId).orElse(0L);
        return Math.toIntExact(orgId);
    }

    @Nullable
    public AuthGroupDto getUserOrgInfo(long userId) {
        if (userId <= 0) {
            return null;
        }
        try {
            Response<AuthGroupDto> organization = userGroupFeignClient.selectByUserId(userId);
            if (organization != null && organization.getData() != null) {
                return organization.getData();
            }
            log.warn("getUserOrgInfo fail. userId:{}, organization:{}", userId, organization);
        } catch (Exception e) {
            log.error("getUserOrgInfo error. userId:{}", userId, e);
        }
        return null;
    }

    public Long getOrgIdByUserId(long userId){
        if (userId <= 0) {
            return 0L;
        }
        try {
            Response<AuthGroupDto> organization = userGroupFeignClient.selectByUserId(userId);
            if (organization != null && organization.getData() != null) {
                return organization.getData().getGroupBizId();
            }
            log.warn("getOrgIdByUserId fail. userId:{}, organization:{}", userId, organization);
        } catch (Exception e) {
            log.error("getOrgIdByUserId error. userId:{}", userId, e);
        }
        return 0L;
    }


    public String getOrgName(long userId){
        if (userId <= 0) {
            return "";
        }
        try {
            Response<AuthGroupDto> organization = userGroupFeignClient.selectByUserId(userId);
            if (organization != null && organization.getData() != null) {
                return organization.getData().getGroupName();
            }
            log.warn("getUserOrgInfo fail. userId:{}, organization:{}", userId, organization);
        } catch (Exception e) {
            log.error("getUserOrgInfo error. userId:{}", userId, e);
        }
        return "";
    }


    public String getOrgName(int orgId) {
        Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(orgId)));
        if (dtoResponse.notOk() || dtoResponse.getData() == null){
            return StringUtils.EMPTY;
        }
        long groupId = dtoResponse.getData().getId();
        Response<GroupMembersResultDto> response = userGroupFeignClient.getGroupMebmbers(groupId);
        if (response != null && response.getData() != null) {
            return response.getData().getGroupName();
        }
        return StringUtils.EMPTY;
    }

    public String getUserName(long userId) {
        Response<AuthUserDto> response = userFeignClient.getAuthUserById(userId);
        return Optional.ofNullable(response).filter(Response::ok).map(Response::getData).map(AuthUserDto::getUserName).orElse(StringUtils.EMPTY);
    }

    /**
     * 获取组织快照
     * @param operatorId
     * @return
     */
    public String getSimpleOrganization(Integer operatorId) {
        String org = "";
        if (operatorId == null || operatorId <= 0) {
            return org;
        }

        org = userGroupFeignClient.getGroupNameByUserId(Long.valueOf(String.valueOf(operatorId))).getData();
        if (StringUtils.isEmpty(org)) {
            return "";
        }
        if (StringUtils.length(org) <= MAX_ORGANIZATION) {
            return org;
        }

        org = StringUtils.right(org, MAX_ORGANIZATION);
        return org;
    }


    public List<Integer> getSubOrgs(int orgId) {
        Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(orgId)));
        if (dtoResponse.notOk() || dtoResponse.getData() == null){
            return Lists.newArrayList();
        }
        long groupId = dtoResponse.getData().getId();
        Response<GroupMembersResultDto> response = userGroupFeignClient.getGroupMebmbers(groupId);
        if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getSubGroups())) {
            return Lists.newArrayList();
        }

        List<Long> groupIds = response.getData().getSubGroups().stream().map(SimpleGroupVo::getGroupId).collect(Collectors.toList());
        List<Integer> list = groupIds.stream().map(Long::intValue).collect(Collectors.toList());
        return list;
    }

    public Map<Long, String> getSubOrgsMap(int orgId) {
        Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(orgId)));
        if (dtoResponse.notOk() || dtoResponse.getData() == null){
            return Maps.newHashMap();
        }
        long groupId = dtoResponse.getData().getId();
        Response<GroupMembersResultDto> response = userGroupFeignClient.getGroupMebmbers(groupId);
        if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getSubGroups())) {
            return Maps.newHashMap();
        }

        return response.getData().getSubGroups().stream().collect(Collectors.toMap(SimpleGroupVo::getGroupId, SimpleGroupVo::getGroupName));
    }

    public List<Long> getAllUsersByOrgId(int orgId) {
        Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(orgId)));
        if (dtoResponse.notOk() || dtoResponse.getData() == null){
            return Lists.newArrayList();
        }
        long groupId = dtoResponse.getData().getId();
        Response<GroupMembersResultDto> response = userGroupFeignClient.getGroupMebmbers(groupId);
        if (response == null || response.getData() == null) {
            return Lists.newArrayList();
        }

        return getAllUsersByOrgId(response.getData()).stream().distinct().collect(Collectors.toList());
    }

    private List<Long> getAllUsersByOrgId(GroupMembersResultDto result) {
        List<Long> userIds = Lists.newArrayList();
        if (result == null) {
            return userIds;
        }

        if (CollectionUtils.isNotEmpty(result.getMembers())) {
            result.getMembers().forEach(x -> userIds.add((long)x.getId()));
        }

        if (CollectionUtils.isNotEmpty(result.getSubGroups())) {
            result.getSubGroups().parallelStream().forEach(x -> userIds.addAll(getAllUsersByOrgId(Math.toIntExact(x.getGroupId()))));
        }

        return userIds;

    }
}
