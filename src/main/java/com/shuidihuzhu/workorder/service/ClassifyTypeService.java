package com.shuidihuzhu.workorder.service;


import com.shuidihuzhu.client.cf.workorder.model.vo.AdminWorkOrderType;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/3/27
 */
public interface ClassifyTypeService {


    /**
     * 根据一级分类获取所有二级分类
     * @param classifyType
     * @return
     */
    List<Integer> classifyType(int classifyType);


    /**
     * 获取用户的分类
     * @param userId
     * @return
     */
    Map<Integer,List<Integer>> userClassify(long userId);

    Response<List<AdminWorkOrderType>> userClassifyAll(long userId);
}
