package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.workorder.dao.CfWorkOrderQualitySpotDao;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/24
 */
@Service
public class CfWorkOrderQualitySpotService {

    @Resource
    private CfWorkOrderQualitySpotDao cfWorkOrderQualitySpotDao;

    public List<RiskQualitySpotWorkOrderUserConfig> listByUserIds(List<Long> userIds, Long scene) {
        if (CollectionUtils.isEmpty(userIds) || scene<=0){
            return Lists.newArrayList();
        }
        return cfWorkOrderQualitySpotDao.listByUserIds(userIds, scene);
    }

    public RiskQualitySpotWorkOrderUserConfig getByUserId(long userId, Long scene) {
        if (userId <= 0  || scene<=0){
            return null;
        }
        return cfWorkOrderQualitySpotDao.getByUserId(userId, scene);
    }

    public Boolean save(RiskQualitySpotWorkOrderUserConfig config) {
        return cfWorkOrderQualitySpotDao.save(config) > 0;
    }

    public Boolean update(RiskQualitySpotWorkOrderUserConfig config) {
        return cfWorkOrderQualitySpotDao.update(config) > 0;
    }
}
