package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.pr.model.dto.workorder.GroupStaffVo;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:22
 */
@Service
@Slf4j
public class GroupRegularRule extends AbstractRuleChain{

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private StaffStatusDao staffStatusDao;

    protected GroupRegularRule() {
        super(null);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        log.info("Do default of regular group rule, orderType:{}, orderId:{}", orderType, workOrderId);
        Pair<Long, List<StaffStatus>> regularStaffPair = obtainOnlineStaffOfRegularGroup(orderType, patientId);
        return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(regularStaffPair.getRight(), orderType, null),
                PrAllocationEnum.NORMAL, regularStaffPair.getLeft());
    }

    public Pair<Long, List<StaffStatus>> obtainOnlineStaffOfRegularGroup(int workOrderType, long patientId){
        Response<GroupStaffVo> staffResp = orderStaffConfClient.candidateStaffByRegularGroup(workOrderType, patientId);
        if (staffResp.notOk() || staffResp.getData() == null || staffResp.getData().getStaffUids().size() <= 0) {
            return Pair.of(0L, Collections.emptyList());
        }
        if (staffResp.notOk() && CollectionUtils.isEmpty(staffResp.getData().getStaffUids())) {
            log.error("No staff is set in the regular group of the work order({})", workOrderType);
        }
        return Pair.of(staffResp.getData().getGroupId(),
                staffStatusDao.getStaffsByStatus(staffResp.getData().getStaffUids(), workOrderType, StaffStatusEnum.online.getType()));
    }
}
