package com.shuidihuzhu.workorder.service.custom.recruit.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PrAllocationEnum {

    DEFAULT(0, "未分配成功"),
    NORMAL(1, "默认分配方式"),
    LATELY(2, "相同工单上一次处理人"),
    SUPPLEMENT_PROMOTER(3, "补采发起人"),
    DISEASE(4, "按照顾问处理疾病分配"),
    PATIENT_COUNSELOR(5, "患者所属顾问"),
    CHANNEL(6, "按渠道分配"),
    PERMIT(7, "按权限分配"),
    TRIGGER_CREATE_STAFF(8, "触发工单生成的员工"),
    ;

    private int code;
    private String desc;
}
