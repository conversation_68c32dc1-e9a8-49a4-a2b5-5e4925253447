package com.shuidihuzhu.workorder.service.custom.recruit.model;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/18 20:42
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PriorityWorkOrder extends WorkOrderBase {

    private int priority;
    private boolean isDownGrade;

    public PriorityWorkOrder(WorkOrderBase workOrderBase, int priority, boolean isDownGrade) {
        BeanUtils.copyProperties(workOrderBase, this);
        this.priority = priority;
        this.isDownGrade = isDownGrade;
    }

    @Override
    public List<WorkOrderExt> getWorkOrderExt() {
        return List.of();
    }

}
