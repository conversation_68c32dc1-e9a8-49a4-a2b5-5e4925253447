package com.shuidihuzhu.workorder.service.custom.recruit;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/8 20:34
 */
@Service
@Slf4j
public class PrStaffStatusService {

    private static final int dimissionStatus = 40;

    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private UserOperationRecordDao recordDao;
    @Resource
    private BaseWorkOrderService baseWorkOrderService;
    @Resource
    private UserFeignClient userFeignClient;

    /**
     * 1. 强制离线离职员工&更新在职状态
     * 2. 强制释放处理中、稍后处理的工单
     * 用户状态枚举：
     * INACTIVE(10, "新注册待激活用户"),
     * NORMAL(20, "正常"),
     * LOCKED_NOT_UPDATE_PWD(30, "密码长时间未修改锁定"),
     * LOCKED_ADMIN(31, "管理员锁定"),
     * CANCEL(40, "注销"),
     */
    public void workOrderReleaseOnDimission(List<Long> allOnJobUsers) {
        log.info("PR 清除离职的员工");
        List<Long> offJobUserIds = Lists.newArrayList();
        Lists.partition(allOnJobUsers, 50).forEach(userIds -> {
                Response<List<AuthUserDto>> authRpcResponse = userFeignClient.getAuthUserByIds(userIds);
                log.info("查询用户的在职状态 userId:{} result:{}", userIds, authRpcResponse);
                if (authRpcResponse == null || CollectionUtils.isEmpty(authRpcResponse.getData())) {
                    return;
                }

                Set<Long> userIdResult = new HashSet<>();
                for (AuthUserDto model : authRpcResponse.getData()) {
                    if (model.getStatus() == dimissionStatus) {
                        offJobUserIds.add(model.getUserId());
                    }
                    userIdResult.add(model.getUserId());
                }
                for (Long user : userIds) {
                    if (!userIdResult.contains(user)) {
                        offJobUserIds.add(user);
                    }
                }
            }
        );

        if (CollectionUtils.isEmpty(offJobUserIds)) {
            return;
        }
        //强制离线离职员工
        List<StaffStatus> onlineStaffs = staffStatusDao.getStaffs(offJobUserIds, WorkOrderType.PR_WORK_ORDER_LIST)
                .stream()
                .filter(staffStatus -> staffStatus.getStaffStatus() != StaffStatusEnum.offline.getType())
                .collect(Collectors.toList());
        int offlineResult = staffStatusDao.updateJobAndStatusBatch(offJobUserIds, WorkOrderType.PR_WORK_ORDER_LIST, StaffStatusEnum.offline.getType(), 1);
        List<Long> dimissionUids = onlineStaffs.stream().map(StaffStatus::getUserId).distinct().collect(Collectors.toList());
        log.info("Dimission offline user ids {}, success {}", dimissionUids, offlineResult);
        for (StaffStatus dimissionStaff : onlineStaffs) {
            //强制释放稍后处理的工单
            List<WorkOrderBase> laterOrderList = workOrderDao.getWorkorderListByUser(dimissionStaff.getUserId(), dimissionStaff.getOrderType(),
                    List.of(HandleResultEnum.doing.getType(), HandleResultEnum.later_doing.getType()));
            if (CollectionUtils.isEmpty(laterOrderList)) {
                log.info("freeWorkOrder ignore on {} why work order is empty", dimissionStaff.getUserId());
                continue;
            }

            int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(laterOrderList.stream().map(WorkOrderBase::getId).collect(Collectors.toList()));
            //记录工单释放
            if (result > 0) {
                List<WorkOrderRecord> records = WorkOrderRecord.freeRecord(laterOrderList);
                recordDao.saveRecordList(records);
            }
        }
    }

}
