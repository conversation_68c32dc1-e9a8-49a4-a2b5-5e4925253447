package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:27
 */
@Service
@Slf4j
public class PrSpecialChannelRule extends AbstractRuleChain {

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private DiseaseRule diseaseRule;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;

    public PrSpecialChannelRule() {
        super(null);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        PrResponsibleVo prResponsibleVo = null;
        //1.1 按照指定渠道查询候选人
        List<Long> orgStaffs = obtainStaffBySpecialChannel(patientId, orderType);
        //1.2 筛查候选人是否有疾病规则配置
        List<Long> diseaseCandidate = diseaseRule.obtainDiseaseStaff(workOrderId, orderType, patientId);
        if (diseaseCandidate == null) {
            return new PrResponsibleVo(0L, null, null);
        }
        List<StaffStatus> staffStatuses = Optional.of(orgStaffs).filter(CollectionUtils::isNotEmpty)
                .map(dss->staffStatusDao.getStaffs(dss, List.of(orderType))).orElse(Collections.emptyList());
        Set<Long> commonUid = Sets.newHashSet(CollectionUtils.intersection(diseaseCandidate, orgStaffs));
        //2.1 优先疾病规则
        if (commonUid.size() > 0) {
            prResponsibleVo = doSelect(staffStatuses, commonUid, orderType, workOrderId, PrAllocationEnum.DISEASE);
        }

        //2.2 如果疾病的都离线了/没有疾病配置，走渠道规则
        if (prResponsibleVo == null) {
            prResponsibleVo = doSelect(staffStatuses, Sets.newHashSet(orgStaffs), orderType, workOrderId, PrAllocationEnum.CHANNEL);
        }

        return Optional.ofNullable(prResponsibleVo).orElse(new PrResponsibleVo(0L, null, null));
    }

    // 复筛是否需要自动分配
    public boolean isNoAssign(long patientId) {
        Response<Boolean> response = orderStaffConfClient.judgeNeedAutoAssign(patientId);
        return response.ok() && !response.getData();
    }

    private PrResponsibleVo doSelect(List<StaffStatus> staffStatuses, Set<Long> validCandidate, int orderType, long orderId,
                                     PrAllocationEnum prAllocationEnum) {
        staffStatuses = staffStatuses.stream().filter(ss -> validCandidate.contains(ss.getUserId())).collect(Collectors.toList());

        //提报工单特有逻辑，如果候选人中有提报工单的创建人，则直接分给他
        if (orderType == WorkOrderType.pr_submit_audit.getType()) {
            WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(orderId, OrderExtName.createOrderUser.getName());
            if (workOrderExt != null) {
                Optional<StaffStatus> preCandidate = staffStatuses.stream()
                        .filter(staffStatus -> staffStatus.getUserId() == Long.parseLong(workOrderExt.getExtValue())).findFirst();
                log.info("Prepare candidate is {}", JSON.toJSONString(preCandidate));
                if (preCandidate.isPresent()) {
                    if (preCandidate.get().getStaffStatus() == StaffStatusEnum.online.getType()) {
                        return new PrResponsibleVo(preCandidate.get().getUserId(), PrAllocationEnum.TRIGGER_CREATE_STAFF, null);
                    }
                    return new PrResponsibleVo(0L, null, null);
                }
            }
        }

        if (staffStatuses.stream().anyMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
            //1.1 根据默认选人规则选择一人
            log.info("Recruit channel rule candidate staffs:{}, workOrderType:{}", staffStatuses, orderType);
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(staffStatuses, orderType, null),
                    prAllocationEnum, null);
        }

        if (staffStatuses.stream().anyMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.stop.getType())) {
            //1.2 如果没有在线且存在暂停，则待分配
            log.info("Recruit channel rule keep Unassigned candidate staffs:{}, workOrderType:{}", staffStatuses, orderType);
            return new PrResponsibleVo(0L, null, null);
        }

        //1.3 全部离线，或者不存在候选人，返回null
        return null;
    }

    /**
     * 查询配置疾病的员工
     * @return null rpc failed, or else success
     */
    private List<Long> obtainStaffBySpecialChannel(long patientId, int workOrderType) {
        Response<List<Long>> resp = orderStaffConfClient.candidateStaffBySpecialChannel(workOrderType, patientId);
        log.info("Obtain candidate staff by special channel, workOrderType:{}, patientId:{}, resp:{}", workOrderType, patientId, resp);

        List<Long> candidates = Optional.of(resp).filter(Response::ok).map(Response::getData).orElse(List.of());
        if (CollectionUtils.isEmpty(candidates)) {
            log.warn("Channel work order is not configured with permissions, orderType {}, patientId {}", workOrderType, patientId);
        }
        return candidates;
    }

}
