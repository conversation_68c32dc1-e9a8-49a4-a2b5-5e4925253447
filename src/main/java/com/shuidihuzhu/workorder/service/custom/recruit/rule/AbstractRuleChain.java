package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:22
 */
@Slf4j
public abstract class AbstractRuleChain {

    protected AbstractRuleChain nextRule;
    protected AbstractRuleChain(AbstractRuleChain nextRule) {this.nextRule = nextRule;}

    /**
     * 筛选工单处理候选人
     * @param orderType
     * @param workOrderId
     * @param patientId
     * @return PrResponsibleVo
     */
    protected abstract PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId);

}
