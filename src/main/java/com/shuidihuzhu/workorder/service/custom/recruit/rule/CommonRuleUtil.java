package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.HandlingLimitInStaff;
import com.shuidihuzhu.workorder.service.mq.GeneralWorkOrderAssignateImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/1 21:02
 */
@Component
@Slf4j
public class CommonRuleUtil {

    @Value("${work.order.handling.limit.in.staff:[{\"orderTypes\":[50,51,62,64],\"userId\":75271,\"handlingLimit\":1},{\"orderTypes\":[50,51,62,64],\"userId\":87797,\"handlingLimit\":1}]}")
    private String handlingLimitInStaff;

    private List<HandlingLimitInStaff> handlingLimitInStaffs;

    @Resource
    private GeneralWorkOrderAssignateImpl generalWorkOrderAssignate;

    @PostConstruct
    public void init() {
        handlingLimitInStaffs = JSON.parseArray(handlingLimitInStaff, HandlingLimitInStaff.class);
    }

    @ApolloConfigChangeListener
    public void changeHandler(ConfigChangeEvent changeEvent) {
        ConfigChange change = changeEvent.getChange("work.order.handling.limit.in.staff");
        if (change != null) {
            handlingLimitInStaffs = JSON.parseArray(change.getNewValue(), HandlingLimitInStaff.class);
        }
    }

    /**
     * 根据候选员工，根据默认规则选择一个合适的员工分配
     */
    public long commonSelectResponsible(List<StaffStatus> staffStatuses, int workOrderType, Integer orderLimitCount) {
        //在线时进行分配
        List<Long> onlineCandidateUserIds = staffStatuses.stream()
                .filter(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.online.getType())
                .map(StaffStatus::getUserId).collect(Collectors.toList());
        log.info("Candidate staffs:{}, workorderType:{}", staffStatuses, workOrderType);
        if (CollectionUtils.isEmpty(onlineCandidateUserIds)) {
            return 0L;
        }
        if (onlineCandidateUserIds.size() == 1 && orderLimitCount != null && orderLimitCount < 0) {
            return onlineCandidateUserIds.get(0);
        }
        //2. 根据默认优先级查找具体的责任人
        return Optional.ofNullable(generalWorkOrderAssignate.calCanAssignStaff(
                onlineCandidateUserIds, workOrderType, orderLimitCount, this::finalOrderLimitJudge)).orElse(0L);
    }

    public Integer finalOrderLimitJudge(long staffUid, int orderType) {
        return handlingLimitInStaffs.stream()
                .filter(s -> s.getUserId() == staffUid && s.getOrderTypes().contains(orderType))
                .map(HandlingLimitInStaff::getHandlingLimit).findAny().orElse(null);
    }

}
