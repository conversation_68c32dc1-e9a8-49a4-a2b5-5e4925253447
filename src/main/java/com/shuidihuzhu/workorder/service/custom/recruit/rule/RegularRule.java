package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.UserRoleClient;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import com.shuidihuzhu.workorder.service.mq.GeneralWorkOrderAssignateImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:27
 */
@Service
@Slf4j
public class RegularRule extends AbstractRuleChain {

    @Resource
    private GeneralWorkOrderAssignateImpl generalWorkOrderAssignate;
    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private UserRoleClient userRoleClient;
    @Resource
    private StaffStatusService staffStatusService;

    public RegularRule() {super(null);}

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        log.info("Do default rule, orderType:{}, orderId:{}", orderType, workOrderId);

        List<StaffStatus> staffStatusesRegular = querySimpleOnlineStaff(Math.toIntExact(patientId), orderType);
        if (CollectionUtils.isNotEmpty(staffStatusesRegular)) {
            List<Long> userIds = staffStatusesRegular.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
            Long availableUser = generalWorkOrderAssignate.calCanAssignStaff(
                    userIds, orderType, null, commonRuleUtil::finalOrderLimitJudge);
            return new PrResponsibleVo(availableUser, PrAllocationEnum.NORMAL, null);
        }

        return new PrResponsibleVo(0L, null, null);
    }

    /**
     * 查询候选责任人默认方式
     * @param caseId
     * @param workOrderType
     * @return
     */
    public List<StaffStatus> querySimpleOnlineStaff(int caseId, int workOrderType) {
        List<Long> userIds = getHasPermissionUserIds(caseId, workOrderType);
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        //查询在线用户
        return staffStatusService.getStaffOnlineToday(userIds,workOrderType);
    }

    private List<Long> getHasPermissionUserIds(int caseId, int workOrderType) {
        Response<List<String>> getRpcResult = userRoleClient.obtainPermitStaffMisList(workOrderType, caseId);
        if (getRpcResult.notOk() || CollectionUtils.isEmpty(getRpcResult.getData())){
            return Lists.newArrayListWithCapacity(0);
        }

        Response<List<AuthUserDto>> response = userFeignClient.getByLoginNameList(getRpcResult.getData());
        if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())) {
            log.warn("Call account saas dont expect, caseId {}, orderType {}, resp {}", caseId, workOrderType, response);
            return null;
        }

        return response.getData().stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
    }

}
