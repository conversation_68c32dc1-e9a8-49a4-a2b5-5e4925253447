package com.shuidihuzhu.workorder.service.custom.recruit;

import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.PrWorkOrderRelClient;
import com.shuidihuzhu.pr.patient.client.patient.PrPatientClient;
import com.shuidihuzhu.pr.patient.model.enums.workorder.WorkOrderCreateSourceEnum;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import com.shuidihuzhu.workorder.service.custom.recruit.rule.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/31 16:16
 */
@Slf4j
@Service
public class WorkOrderStaffConfService {

    @Resource
    private BaseWorkOrderService baseWorkOrderService;
    @Resource
    private PrWorkOrderRelClient orderRelClient;
    @Resource
    private PrPatientClient prPatientClient;
    @Resource
    private StaffStatusService staffStatusService;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;
    @Resource
    private DiseaseUrgentRule diseaseUrgentRule;
    @Resource
    private DiseaseRule diseaseRule;
    @Resource
    private GroupRule groupRule;
    @Resource
    private PrSpecialChannelRule prSpecialChannelRule;
    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private SingleChannelRule singleChannelRule;

    @FunctionalInterface
    public interface CandidateFun {
        PrResponsibleVo select(int orderType, long workOrderId, long patientId);
    }

    public PrResponsibleVo assignForFirstAudit(long currWorkOrderId, long patientId) {
        int workOrderType = WorkOrderType.pr_first_screen.getType();
        long recentUid = obtainResponsibleRecent(patientId, workOrderType);
        if (recentUid > 0) {
            return certainUrgentRuleResponsible(currWorkOrderId, patientId, recentUid, workOrderType, PrAllocationEnum.LATELY);
        }

        return diseaseUrgentRule.selectResponsible(workOrderType, currWorkOrderId, patientId);
    }

    public PrResponsibleVo assignForSecondLine(long currWorkOrderId, long patientId) {
        int workOrderType = WorkOrderType.pr_2line_first_screen_service.getType();
        long recentUid = obtainResponsibleRecent(patientId, workOrderType);
        if (recentUid > 0) {
            return certainRuleResponsible(currWorkOrderId, patientId, recentUid, workOrderType, PrAllocationEnum.LATELY,
                    (orderType, orderId, pId) -> prSpecialChannelRule.selectResponsible(orderType, orderId, pId));
        }

        return prSpecialChannelRule.selectResponsible(workOrderType, currWorkOrderId, patientId);
    }

    public PrResponsibleVo assignForSecondAudit(long currWorkOrderId, long patientId, long firstScreenId) {

        WorkOrderVO lastWorkOrder = baseWorkOrderService.getWorkOrderById(firstScreenId);
        log.info("prCurrentAssign currWorkOrderId:{} patientId:{} lastWorkOrder.orderType:{}",
                currWorkOrderId, patientId, lastWorkOrder.getOrderType());
        if (lastWorkOrder.getOrderType() == WorkOrderType.pr_first_screen.getType()) {
            return prSpecialChannelRule.selectResponsible(WorkOrderType.pr_second_screen.getType(), currWorkOrderId, patientId);
        }
        if (lastWorkOrder.getOrderType() == WorkOrderType.pr_2line_first_screen_service.getType()) {
            return certainRuleResponsible(currWorkOrderId, patientId, lastWorkOrder.getOperatorId(), WorkOrderType.pr_second_screen.getType(), PrAllocationEnum.LATELY,
                    (orderType, orderId, pId) -> prSpecialChannelRule.selectResponsible(orderType, orderId, pId));
        }

        //如果百度线下的复筛工单 不进行分配
        boolean noAssign = prSpecialChannelRule.isNoAssign(patientId);
        log.info("prCurrentAssign patientId:{} noAssign:{}", patientId, noAssign);
        if (noAssign) {
            return new PrResponsibleVo(0L, null, null);
        }

        //该处主要防止数据有问题，导致的后续工单无法分配
        return prSpecialChannelRule.selectResponsible(WorkOrderType.pr_second_screen.getType(), currWorkOrderId, patientId);
    }

    public PrResponsibleVo assignForSubmitAudit(long workOrderId, long patientId) {
        int orderType = WorkOrderType.pr_submit_audit.getType();
        long recentUid = obtainResponsibleRecent(patientId, orderType);
        //如果有人处理过，则分给最近一次处理人
        if (recentUid > 0) {
            return certainSingleRuleResponsible(workOrderId, patientId, recentUid, orderType, PrAllocationEnum.LATELY, prSpecialChannelRule::selectResponsible);
        }

        return prSpecialChannelRule.selectResponsible(WorkOrderType.pr_submit_audit.getType(), workOrderId, patientId);
    }

    public PrResponsibleVo assignForSubmitAuditReject(long workOrderId, long patientId) {
        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, OrderExtName.createOrder.getName());
        log.info("Query reject ext info {}", workOrderExt);
        if (workOrderExt != null) {
            WorkOrderExt createSubmitAuditUser = workOrderDaoExt.getWorkOrderExt(Long.parseLong(workOrderExt.getExtValue()), OrderExtName.createOrderUser.getName());
            log.info("Query concrete ext info {}", createSubmitAuditUser);
            if (createSubmitAuditUser != null) {
                long concreteStaff = Long.parseLong(createSubmitAuditUser.getExtValue());
                StaffStatus staffStatus = staffStatusService.getStaffStatus(concreteStaff, WorkOrderType.pr_submit_audit_reject.getType());
                if (staffStatus != null && staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
                    return new PrResponsibleVo(concreteStaff, PrAllocationEnum.TRIGGER_CREATE_STAFF, null);
                }
            }
        }

        return new PrResponsibleVo(0L, null, null);
    }

    public PrResponsibleVo assignForSupplement(long currWorkOrderId, long patientId, long supplementTaskId){
        List<WorkOrderBase> recentSupplementWordOrders = workOrderDao.getOrderByCaseId(Math.toIntExact(patientId),
                List.of(WorkOrderType.pr_supplement_material.getType()),
                List.of(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType(), HandleResultEnum.later_doing.getType()))
                .stream().sorted(Comparator.comparing(WorkOrderBase::getCreateTime)).collect(Collectors.toList());
        //如果有当前工单前有未分配/处理的工单，则不分配
        if (recentSupplementWordOrders.get(0).getId() != currWorkOrderId) {
            log.info("Untreated work order found, skip assign:{}", recentSupplementWordOrders.get(0));
            return new PrResponsibleVo(0L, null, null);
        }

        int workOrderType = WorkOrderType.pr_supplement_material.getType();
        long recentOptUid = obtainResponsibleRecent(patientId, workOrderType);
        PrResponsibleVo prResponsibleVo;
        if (recentOptUid > 0 && Optional.ofNullable(staffStatusService.getStaffStatus(recentOptUid, workOrderType))
                .filter(ss -> List.of(StaffStatusEnum.online.getType(), StaffStatusEnum.stop.getType()).contains(ss.getStaffStatus())).isPresent()) {
            prResponsibleVo = certainRuleResponsible(currWorkOrderId, patientId, recentOptUid, workOrderType, PrAllocationEnum.LATELY);
        } else {
            prResponsibleVo = assignForSupplementByTaskId(supplementTaskId, patientId, currWorkOrderId);
        }

        if (prResponsibleVo == null) {
            prResponsibleVo = diseaseRule.selectResponsible(workOrderType, currWorkOrderId, patientId);
        }

        return prResponsibleVo;
    }

    private PrResponsibleVo assignForSupplementByTaskId(long supplementTaskId, long patientId, long currWorkOrderId) {
        if (supplementTaskId <= 0) {
            return null;
        }
        RpcResult<Long> taskRpc = orderRelClient.getIssnueUserIdBySupplementMaterialId(supplementTaskId);
        log.info("Query supplement optUid, patientId:{}, orderType:{}, resp:{}", patientId, WorkOrderType.pr_supplement_material, taskRpc);
        if (Optional.of(taskRpc).filter(RpcResult::isSuccess).filter(longRpcResult -> longRpcResult.getData() > 0).isPresent()) {
            return certainRuleResponsible(currWorkOrderId, patientId, taskRpc.getData(),
                    WorkOrderType.pr_supplement_material.getType(), PrAllocationEnum.SUPPLEMENT_PROMOTER);
        }

        return null;
    }

    public PrResponsibleVo assignForOnlineService(long currWorkOrderId, long patientId, int workOrderType) {
        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(currWorkOrderId, OrderExtName.createSource.name());
        if (workOrderExt != null && StringUtils.isNotBlank(workOrderExt.getExtValue())
                && Integer.parseInt(workOrderExt.getExtValue()) == WorkOrderCreateSourceEnum.transfer_service_group.getCode()) {
            return certainSingleRuleResponsible(currWorkOrderId, patientId, obtainPatientOfCounselor(patientId), workOrderType,
                    PrAllocationEnum.PATIENT_COUNSELOR, (o, w, p) -> new PrResponsibleVo(0L, null, null));
        }

        return groupRule.selectResponsible(workOrderType, currWorkOrderId, patientId);
    }

    public PrResponsibleVo assignForRejectOrSupplement(int workOrderType, long workOrderId, long patientId) {
        long counselorUid = obtainPatientOfCounselor(patientId);
        StaffStatus staffStatus = staffStatusService.getStaffStatus(counselorUid, workOrderType);
        if (staffStatus != null) {
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(List.of(staffStatus), workOrderType, null),
                    PrAllocationEnum.PATIENT_COUNSELOR, null);
        }

        return new PrResponsibleVo(0L, null, null);
    }

    public PrResponsibleVo assignForReturnVisit(int workOrderType, long workOrderId, long patientId) {
        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, OrderExtName.createSource.name());
        //针对移交随访组的随访工单，走单独的分配逻辑
        if (workOrderExt != null) {
            if (WorkOrderCreateSourceEnum.transfer_create_source_all.contains(workOrderExt.getExtValue())) {
                long recentUid = obtainRespRecentByExtVal(patientId, workOrderType, OrderExtName.createSource.name(),
                        WorkOrderCreateSourceEnum.convertCreateSourceGroup(Integer.parseInt(workOrderExt.getExtValue())));
                if (recentUid > 0) {
                    return certainSingleRuleResponsible(workOrderId, patientId, recentUid, workOrderType,
                            PrAllocationEnum.LATELY, singleChannelRule::selectResponsible);
                }

                return singleChannelRule.selectResponsible(workOrderType, workOrderId, patientId);
            }

            if (Objects.equals(WorkOrderCreateSourceEnum.own_submit_return_visit.getCode()+"", workOrderExt.getExtValue())) {
                long recentUid = obtainRespRecentByExtVal(patientId, workOrderType, OrderExtName.createSource.name(),
                        List.of(WorkOrderCreateSourceEnum.own_submit_return_visit.getCode()+""));
                if (recentUid > 0) {
                    return certainSingleRuleResponsible(workOrderId, patientId, recentUid, workOrderType,
                            PrAllocationEnum.LATELY, this::assignForRejectOrSupplement);
                }
            }

            return assignForRejectOrSupplement(workOrderType, workOrderId, patientId);
        }

        //以下为兼容，历史上没数据时的情况
        return assignForReturnVisitOld(workOrderType, workOrderId, patientId);
    }

    public PrResponsibleVo assignForReturnVisitOld(int workOrderType, long workOrderId, long patientId) {
        long counselorUid;

        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, OrderExtName.submitReturnVisitUid.name());
        if (workOrderExt != null && StringUtils.isNotBlank(workOrderExt.getExtValue()) && Long.parseLong(workOrderExt.getExtValue()) > 0) {
            counselorUid = Long.parseLong(workOrderExt.getExtValue());
        } else {
            counselorUid = obtainPatientOfCounselor(patientId);
        }

        StaffStatus staffStatus = staffStatusService.getStaffStatus(counselorUid, workOrderType);
        if (staffStatus == null) {
            counselorUid = obtainPatientOfCounselor(patientId);
            staffStatus = staffStatusService.getStaffStatus(counselorUid, workOrderType);
        }
        if (staffStatus != null) {
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(List.of(staffStatus), workOrderType, null),
                    PrAllocationEnum.PATIENT_COUNSELOR, null);
        }

        return new PrResponsibleVo(0L, null, null);
    }

    /**
     * 确定人+疾病分配规则（在线就分，不在线降级规则）
     * @param currOrderType 当前需要处理的工单类型
     */
    private PrResponsibleVo certainUrgentRuleResponsible(long workOrderId, long patientId, long lastOptUid,
                                                         int currOrderType, PrAllocationEnum allocateType){
        StaffStatus staffStatus = staffStatusService.getStaffStatus(lastOptUid, currOrderType);
        if (staffStatus == null) {
            return diseaseUrgentRule.selectResponsible(currOrderType, workOrderId, patientId);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
            return new PrResponsibleVo(lastOptUid, allocateType, null);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType() ||
                staffStatus.getStaffStatus() == StaffStatusEnum.stop.getType()) {
            return diseaseUrgentRule.selectResponsible(currOrderType, workOrderId, patientId);
        }

        throw new IllegalStateException("Unknown staff status "+staffStatus.getStaffStatus());
    }

    /**
     * 确定人分配规则（在线就分，不在线/暂停等待）
     * @param candidateFun 当员工不存在时，查找候选人方法
     */
    private PrResponsibleVo certainSingleRuleResponsible(long workOrderId, long patientId, long lastOptUid,
                                                         int currOrderType, PrAllocationEnum allocateType, CandidateFun candidateFun){
        StaffStatus staffStatus = staffStatusService.getStaffStatus(lastOptUid, currOrderType);
        if (staffStatus == null) {
            return candidateFun.select(currOrderType, workOrderId, patientId);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
            return new PrResponsibleVo(lastOptUid, allocateType, null);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType() ||
                staffStatus.getStaffStatus() == StaffStatusEnum.stop.getType()) {
            return new PrResponsibleVo(0L, null, null);
        }

        throw new IllegalStateException("Unknown staff status "+staffStatus.getStaffStatus());
    }

    /**
     * 确定人+疾病分配规则
     * @param currOrderType 当前需要处理的工单类型
     */
    private PrResponsibleVo certainRuleResponsible(long workOrderId, long patientId, long lastOptUid,
                                                         int currOrderType, PrAllocationEnum allocateType, CandidateFun... candidateFun){
        StaffStatus staffStatus = staffStatusService.getStaffStatus(lastOptUid, currOrderType);
        if (staffStatus == null) {
            return diseaseRule.selectResponsible(currOrderType, workOrderId, patientId);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
            return new PrResponsibleVo(lastOptUid, allocateType, null);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.stop.getType()) {
            return new PrResponsibleVo(0L, null, null);
        }
        if (staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType()) {
            if (candidateFun.length == 0) {
                return diseaseRule.selectResponsible(currOrderType, workOrderId, patientId);
            }
            return candidateFun[0].select(currOrderType, workOrderId, patientId);
        }

        throw new IllegalStateException("Unknown staff status "+staffStatus.getStaffStatus());
    }



    private long obtainResponsibleRecent(long patientId, int workOrderType){
        WorkOrderBase orderBase = workOrderDao.recentAssignedOrder((int) patientId, workOrderType);
        log.info("Recent assigned, orderType:{}, patientId:{}, order:{}", workOrderType, patientId, orderBase);
        if (orderBase == null) {
            return 0L;
        }
        return orderBase.getOperatorId();
    }

    private long obtainRespRecentByExtVal(long patientId, int workOrderType, String extName, Collection<String> extValues){
        WorkOrderBase orderBase = workOrderDao.recentAssignedOnExt((int) patientId, workOrderType, extName, extValues);
        log.info("Recent assigned on ext, orderType:{}, patientId:{}, order:{}", workOrderType, patientId, orderBase);
        if (orderBase == null) {
            return 0L;
        }
        return orderBase.getOperatorId();
    }

    private long obtainPatientOfCounselor(long patientId) {
        Response<Long> rpcResult = prPatientClient.getCounselorUidByPatientId(patientId);
        log.info("Get counselor user id by patientId:{}, resp:{}", patientId, rpcResult);
        return Optional.of(rpcResult).filter(Response::ok).map(Response::getData).orElse(0L);
    }

}
