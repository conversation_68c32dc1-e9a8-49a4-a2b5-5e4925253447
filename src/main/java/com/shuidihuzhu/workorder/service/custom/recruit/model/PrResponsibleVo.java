package com.shuidihuzhu.workorder.service.custom.recruit.model;

import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import lombok.Data;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/4/15 10:59
 */
@Data
public class PrResponsibleVo {

    public PrResponsibleVo(Long responsible, PrAllocationEnum prAllocationEnum, Long groupId) {
        this.responsible = Optional.ofNullable(responsible).orElse(0L);
        this.prAllocationEnum = Optional.ofNullable(responsible).filter(o -> o > 0)
                .map(o -> prAllocationEnum).orElse(PrAllocationEnum.DEFAULT);
        this.groupId = Optional.ofNullable(groupId).orElse(0L);
    }

    private long responsible;
    private PrAllocationEnum prAllocationEnum;
    private long groupId;

}
