package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:22
 */
@Service
@Slf4j
public class DiseaseUrgentRule extends AbstractRuleChain{

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private DiseaseRule diseaseRule;

    protected DiseaseUrgentRule(@Autowired @Qualifier("regularRule") AbstractRuleChain nextRule) {
        super(nextRule);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        List<Long> diseaseStaffs = diseaseRule.obtainDiseaseStaff(workOrderId, orderType, patientId);
        if (diseaseStaffs == null) {
            return new PrResponsibleVo(0L, null, null);
        }
        List<StaffStatus> staffStatuses = Optional.of(diseaseStaffs).filter(CollectionUtils::isNotEmpty)
                .map(dss->staffStatusDao.getStaffs(dss, List.of(orderType))).orElse(Collections.emptyList());
        if (staffStatuses.stream().anyMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
            //1.1 根据默认选人规则选择一人
            log.info("Disease rule candidate staffs:{}, workOrderType:{}", staffStatuses, orderType);
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(staffStatuses, orderType, null),
                    PrAllocationEnum.DISEASE, null);
        }

        return nextRule.selectResponsible(orderType, workOrderId, patientId);
    }

}
