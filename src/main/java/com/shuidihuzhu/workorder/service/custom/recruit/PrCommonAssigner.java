package com.shuidihuzhu.workorder.service.custom.recruit;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.pr.common.constant.MQConstant;
import com.shuidihuzhu.pr.common.model.enums.rejectreason.PatientAuditTypeEnum;
import com.shuidihuzhu.pr.common.model.po.RecruitPatientAuditLog;
import com.shuidihuzhu.pr.patient.client.audit.PrAuditLogClient;
import com.shuidihuzhu.pr.patient.client.patient.PrRepetitionCaseClient;
import com.shuidihuzhu.pr.patient.model.dto.mqpayload.rv.clew.outside.NodeStatusPayload;
import com.shuidihuzhu.pr.patient.model.enums.clew.PatientNodeStatus;
import com.shuidihuzhu.pr.patient.model.enums.clew.outside.NodeStatusNoticeSourceEnum;
import com.shuidihuzhu.pr.patient.model.enums.workorder.WorkOrderCreateSourceEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PriorityWorkOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/15 11:36
 */
@Slf4j
@RefreshScope
public abstract class PrCommonAssigner extends AssignateWorkOrderFacade {

    @Value("#{'${pr-order-no-assign-type:}'.split(',')}")
    private List<Integer> prOrderNoAssignTypeList;

    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    protected WorkOrderDaoExt workOrderDaoExt;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private PrRepetitionCaseClient repetitionCaseClient;
    @Resource
    private Producer producer;
    @Resource
    private PrAuditLogClient prAuditLogClient;
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    protected abstract PrResponsibleVo selectResponsible(int orderType, WorkOrderBase workOrderBase);

    /**
     * 关联ext表扩展字段来查询待分配工单
     * @param extName 扩展字段名，不能为空
     * @param extValue 扩展字段值，==null 会按照>''查询，否则，使用=extValue来查询
     * @param limit 一次分配最大的分配工单数
     */
    protected List<PriorityWorkOrder> fetchPendingOnAssociated(int orderType, HandleResultEnum resultEnum,
                                                               String extName, String extValue, int limit, WorkOrderBase lastOrder) {
        if (StringUtils.isBlank(extName)) {
            throw new IllegalArgumentException();
        }
        if(null == workOrderTypeService.getFromOrderTypeCode(orderType) || null == resultEnum){
            return Lists.newArrayList();
        }

        List<WorkOrderBase> workOrderBases;
        boolean isDownGrade = false;
        if (isDownGrade(lastOrder)) {
            isDownGrade = true;
            workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),
                    transferDateTimeStr(lastOrder), limit);
        } else {
            workOrderBases = workOrderDao.queryUndoOrderExistsExtOrderByLevelAndTime(orderType,
                    transferDateTimeStr(lastOrder), extName, extValue, limit);
            if (workOrderBases.size() < limit) {
                isDownGrade = true;
                Set<Long> diseaseOrderIdSet = workOrderBases.parallelStream().map(WorkOrderBase::getId).collect(Collectors.toSet());
                String fetchDate = null;
                List<WorkOrderBase> tempOrders;
                int count = 0;
                do {
                    tempOrders = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(), fetchDate, limit);
                    count += tempOrders.size();
                    if (count > 60000) {
                        log.error("", new RuntimeException("疑似死循环："+count));
                    }
                    if (CollectionUtils.isEmpty(tempOrders)) {
                        break;
                    }
                    workOrderBases.addAll(tempOrders.parallelStream().filter(wo -> !diseaseOrderIdSet.contains(wo.getId())).collect(Collectors.toList()));

                    fetchDate = transferDateTimeStr(tempOrders.get(tempOrders.size()-1));
                } while (workOrderBases.size() < limit && tempOrders.size() >= limit);
            }
        }

        boolean finalIsDownGrade = isDownGrade;
        return workOrderBases.parallelStream().map(wb -> new PriorityWorkOrder(wb, 0, finalIsDownGrade)).collect(Collectors.toList());
    }

    private String transferDateTimeStr(WorkOrderBase lastOrder) {
        return lastOrder == null ? null : DateUtil.getDate2Str(DateUtil.DATETIME_PATTERN_2 + ".SSS", lastOrder.getCreateTime());
    }

    private boolean isDownGrade(WorkOrderBase lastOrder) {
        if (lastOrder == null) {
            return false;
        }

        return ((PriorityWorkOrder)lastOrder).isDownGrade();
    }

    @Override
    public OpResult<List<? extends WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum,
                                                               WorkOrderBase lastOrder, int limit) {
        if(null == resultEnum || Optional.ofNullable(prOrderNoAssignTypeList).orElse(List.of()).contains(orderType)){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),
        transferDateTimeStr(lastOrder), limit);

        return OpResult.createSucResult(workOrderBases);
    }

    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBase) {
        if (checkRepetition(workOrderBase)) {
            log.info("pr checkRepetition id:{}", workOrderBase.getId());
            return null;
        }
        PrResponsibleVo prResponsibleVo = selectResponsible(orderType, workOrderBase);
        if (prResponsibleVo.getResponsible() > 0) {
            long workOrderId = workOrderBase.getId();
            Map<String, List<WorkOrderExt>> extMap = workOrderDaoExt.getWorkOrderExtByNameMaster(workOrderId,
                    List.of(OrderExtName.assignerRule.getName(), OrderExtName.staffGroupId.getName(), OrderExtName.createSource.getName()))
                    .stream().collect(Collectors.groupingBy(WorkOrderExt::getExtName));
            //保存分配方式
            saveAssigner(workOrderId, prResponsibleVo, extMap);
            //如果组发生变化时，需要修改groupId
            changeGroupId(workOrderId, orderType, prResponsibleVo, extMap);
            //如果是Z计划转交随访的，分配后，通知小鲸鱼
            noticeReturnVisitOfWhale(workOrderBase, extMap, prResponsibleVo.getResponsible());
            //分配日志
            addAssignLog(workOrderBase);
        }
        return OpResult.createSucResult(prResponsibleVo.getResponsible());
    }

    private boolean checkRepetition(WorkOrderBase workOrderBase) {
       Response<Boolean> resp = repetitionCaseClient.checkRepetitionCase(workOrderBase.getCaseId());
       if (resp == null || resp.notOk() || resp.getData() == null) {
           return false;
       }
       return resp.getData();
    }

    private void saveAssigner(long workOrderId, PrResponsibleVo prResponsibleVo, Map<String, List<WorkOrderExt>> extMap) {
        List<WorkOrderExt> workOrderExts = extMap.get(OrderExtName.assignerRule.getName());
        if (CollectionUtils.isNotEmpty(workOrderExts)) {
            workOrderDaoExt.updateByNameValue(workOrderId, OrderExtName.assignerRule.getName(),
                    prResponsibleVo.getPrAllocationEnum().getCode()+"");
        } else {
            workOrderDaoExt.insertWorkOrderExt(workOrderId, OrderExtName.assignerRule.getName(),
                    prResponsibleVo.getPrAllocationEnum().getCode()+"");
        }
    }

    private void changeGroupId(long workOrderId, int orderType, PrResponsibleVo prResponsibleVo,
                               Map<String, List<WorkOrderExt>> extMap) {
        if (!WorkOrderType.PR_ONLINE_WORK_ORDER_LIST.contains(orderType)) {
            return;
        }

        List<WorkOrderExt> workOrderExts = extMap.get(OrderExtName.staffGroupId.getName());
        if (workOrderExts != null && workOrderExts.stream().map(e -> Long.parseLong(e.getExtValue()))
                .anyMatch(v -> v != prResponsibleVo.getGroupId())) {
            if (prResponsibleVo.getGroupId() == 0) {
                Response<Long> resp = orderStaffConfClient.getGroupIdByStaffUid(prResponsibleVo.getResponsible());
                if (resp.ok()) {
                    prResponsibleVo.setGroupId(resp.getData());
                }
            }
            if (prResponsibleVo.getGroupId() > 0) {
                workOrderDaoExt.updateByNameValue(workOrderId, OrderExtName.staffGroupId.getName(),
                        prResponsibleVo.getGroupId()+"");
            }
        }
    }

    private void noticeReturnVisitOfWhale(WorkOrderBase workOrderBase, Map<String, List<WorkOrderExt>> extMap, long operatorUid) {
        List<WorkOrderExt> workOrderExts = extMap.get(OrderExtName.createSource.name());
        if (CollectionUtils.isNotEmpty(workOrderExts) &&
                Objects.equals(workOrderExts.get(0).getExtValue(), WorkOrderCreateSourceEnum.z_plan_transfer_visit_group_auto.getCode()+"")) {
            NodeStatusPayload nodeStatusPayload = new NodeStatusPayload();
            nodeStatusPayload.setPatientId(workOrderBase.getCaseId());
            nodeStatusPayload.setNodeStatus(PatientNodeStatus.SubmitPatientStatusStatus.PASS.getCode());
            nodeStatusPayload.setNodeId(PatientNodeStatus.PatientNodeClass.RECRUIT_AUDIT.getNodeId());
            nodeStatusPayload.setNoticeSource(NodeStatusNoticeSourceEnum.RETURN_VISIT.getCode());
            nodeStatusPayload.setFollowUpUid(operatorUid);
            Message<?> msg = Message.of(MQConstant.TopicCos.PR, MQConstant.TagCons.PATIENT_NODE_STATUS_UPDATE,
                    workOrderBase.getCaseId() + "_" + System.currentTimeMillis(), nodeStatusPayload);
            MessageResult result = producer.send(msg);
            log.info("Send notice which return visit of whale, param {}, result {}", msg, result);
        }

    }

    private void addAssignLog(WorkOrderBase workOrderBase) {
        List<Integer> orderType = ClassifyTypeEnum.getByOneLevel(OneTypeEnum.patient_recruit.getType());
        if (CollectionUtils.isEmpty(orderType) || !orderType.contains(workOrderBase.getOrderType())) {
            return;
        }
        RecruitPatientAuditLog recruitPatientAuditLog =
                RecruitPatientAuditLog.builder().auditStatus(PatientAuditTypeEnum.ASSIGN_ORDER.getDesc())
                        .operationId(0)
                        .operationName("系统")
                        .orderTypeDesc(PatientAuditTypeEnum.ASSIGN_ORDER.getDesc()).workOrderId(workOrderBase.getId())
                        .comment("")
                        .type(PatientAuditTypeEnum.ASSIGN_ORDER.getCode())
                        .patientId(workOrderBase.getCaseId()).build();
        prAuditLogClient.addAuditLog(recruitPatientAuditLog);
    }
}
