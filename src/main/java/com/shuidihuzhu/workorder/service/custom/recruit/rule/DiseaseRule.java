package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:22
 */
@Service
@Slf4j
public class DiseaseRule extends AbstractRuleChain{

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private OrderExtService orderExtService;
    @Resource
    private StaffStatusDao staffStatusDao;

    protected DiseaseRule(@Autowired @Qualifier("regularRule") AbstractRuleChain nextRule) {
        super(nextRule);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        List<Long> diseaseStaffs = obtainDiseaseStaff(workOrderId, orderType, patientId);
        if (diseaseStaffs == null) {
            return new PrResponsibleVo(0L, null, null);
        }
        List<StaffStatus> staffStatuses = Optional.of(diseaseStaffs).filter(CollectionUtils::isNotEmpty)
                .map(dss->staffStatusDao.getStaffs(dss, List.of(orderType))).orElse(Collections.emptyList());
        if (staffStatuses.stream().anyMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
            //1.1 根据默认选人规则选择一人
            log.info("Disease rule candidate staffs:{}, workOrderType:{}", staffStatuses, orderType);
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(staffStatuses, orderType, null),
                    PrAllocationEnum.DISEASE, null);
        }

        if (staffStatuses.stream().allMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType())) {
            return nextRule.selectResponsible(orderType, workOrderId, patientId);
        }

        return new PrResponsibleVo(0L, null, null);
    }

    /**
     * 查询配置疾病的员工
     * @return null rpc failed, or else success
     */
    public List<Long> obtainDiseaseStaff(long workOrderId, int workOrderType, long patientId) {
        WorkOrderExt orderExt = orderExtService.getByOrderId(workOrderId, OrderExtName.disease);
        RpcResult<List<Long>> staffResp = orderStaffConfClient.candidateStaffByDiseases(workOrderType, patientId,
                Optional.ofNullable(orderExt).map(WorkOrderExt::getExtValue).orElse(""));
        log.info("Obtain candidate staff by disease, patientId:{}, orderType:{}, resp:{}", patientId, workOrderType, staffResp);
        if (staffResp.isFail()) {
            return null;
        }

        return staffResp.getData();
    }
}
