package com.shuidihuzhu.workorder.service.custom.recruit;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.msg.util.EnvLoadUtil;
import com.shuidihuzhu.pr.client.PrWorkOrderRelClient;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.pr.client.UserRoleClient;
import com.shuidihuzhu.pr.model.dto.workorder.GroupStaffVo;
import com.shuidihuzhu.pr.patient.client.patient.PrPatientClient;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.repository.WorkOrderRepository;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import com.shuidihuzhu.workorder.service.custom.recruit.rule.GroupRule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/9 17:50
 */
@Component
@Slf4j
public class WorkOrderAssignExpAnalyzer {

    @Value("#{${work.order.handling.upper.limit:{\"50\":5,\"62\":5,\"64\":30,\"51\":30,\"58\":5,\"59\":5,\"65\":10,\"60\":10}}}")
    private Map<Integer, Integer> handlingLimitMap;
    @Value("${pr.work.order.max.accept.unhandle.count:1}")
    private int maxAcceptUnHandleCount;
    @Value("${pr.work.order.sample.analyze.count:10}")
    private int sampleAnalyzeCount;

    private static final String QX_GROUP_ID = "wx-alarm-prod-20201216-0001";
    private static final String QX_GROUP_ID_TEST = "wx-alarm-20210611-0001";

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;
    @Resource
    private WorkTypePropertyService workTypePropertyService;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private WorkOrderRepository workOrderRepository;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;
    @Resource
    private StaffStatusService staffStatusService;
    @Resource
    private AlarmClient alarmClient;
    @Resource
    private EnvLoadUtil envLoadUtil;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private PrWorkOrderRelClient orderRelClient;
    @Resource
    private UserRoleClient userRoleClient;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private GroupRule groupRule;
    @Resource
    private PrPatientClient prPatientClient;
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    public void assignExpAnalyze() {
        List<WorkTypeProperty> workTypeProperties = workTypePropertyService.selectByPropertyTypes(WorkTypeProperty.SECOND_WORK_TYPE,
                WorkOrderType.PR_WORK_ORDER_LIST, List.of(WorkTypeProperty.PropertyType.HANDLING_COUNT.getCode()));
        StringBuilder sb = new StringBuilder();
        for (Integer orderType : WorkOrderType.PR_WORK_ORDER_LIST) {
            Integer maxLimit = obtainUpperLimit(workTypeProperties, orderType);
            if (maxLimit == null) {
                log.warn("Work order limit not found, orderType:{}", orderType);
                continue;
            }

            Pair<Map<Long, Integer>, Map<Integer, Set<Long>>> pair = queryStaffHandlingCount(orderType);
            if (pair == null) {
                continue;
            }
            Map<Long, Integer> unHandleMap = pair.getKey();
            Map<Integer, Set<Long>> statusMap = pair.getRight();

            List<WorkOrderBase> samplingUnHandle = workOrderDao.getAllWorkorderList(0, List.of(orderType), sampleAnalyzeCount,
                    HandleResultEnum.undoing.getType(), 0, 1, null, List.of(), null,
                    null, null, null, null, null, 0, null, 0);

            Map<Long, Object> expMap = staffPauseExclude(orderType, maxLimit, samplingUnHandle, statusMap, unHandleMap);

            log.info("exp:{}", expMap);

            if (MapUtils.isNotEmpty(expMap) && unHandleMap.values().stream().filter(val -> val < maxLimit)
                    .mapToInt(val -> maxLimit - val).sum() > maxAcceptUnHandleCount) {
                sb.append(assembleMsg(orderType, expMap));
            }
        }

        if (StringUtils.isNotBlank(sb)) {
            sb.insert(0, "工单未分配异常报警\n");
            String groupId = QX_GROUP_ID_TEST;
            if (envLoadUtil.isOnline()) {
                groupId = QX_GROUP_ID;
            }
            alarmClient.sendByGroup(groupId, sb.toString());
        }
    }

    private Pair<Map<Long, Integer>, Map<Integer, Set<Long>>> queryStaffHandlingCount(int orderType) {
        Map<Long, Integer> unHandleMap = workOrderRepository.staffHandleCount(orderType, HandleResultEnum.doing.getType())
                .stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, WorkOrderDoingCount::getNum));
        if (MapUtils.isEmpty(unHandleMap)) {
            return null;
        }
        RpcResult<List<String>> getRpcResult = userRoleClient.getWorkOrderMisList(orderType);
        if (!getRpcResult.isSuccess() || CollectionUtils.isEmpty(getRpcResult.getData())){
            return null;
        }
        Response<List<AuthUserDto>> resp = userFeignClient.getByLoginNameList(getRpcResult.getData());

        if (resp.notOk() || CollectionUtils.isEmpty(resp.getData())){
            return null;
        }
        Set<Long> permit = resp.getData().stream().map(AuthUserDto::getUserId).collect(Collectors.toSet());
        unHandleMap = unHandleMap.entrySet().stream().filter(m -> permit.contains(m.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        for (Long u : permit) {
            if (!unHandleMap.containsKey(u)) {
                unHandleMap.put(u, 0);
            }
        }
        if (MapUtils.isEmpty(unHandleMap)) {
            return null;
        }

        Map<Integer, Set<Long>> statusMap = staffStatusService.getStaffOnlineAndStopToday(Lists.newArrayList(unHandleMap.keySet()), orderType)
                .stream().filter(s -> s.getOnJob() == 0)
                .collect(Collectors.groupingBy(StaffStatus::getStaffStatus, Collectors.mapping(StaffStatus::getUserId, Collectors.toSet())));
        unHandleMap = unHandleMap.entrySet().stream().filter(entry ->
                Optional.ofNullable(statusMap.get(StaffStatusEnum.online.getType())).orElse(Set.of()).contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (MapUtils.isEmpty(unHandleMap)) {
            return null;
        }

        Optional.ofNullable(statusMap.get(StaffStatusEnum.online.getType())).ifPresentOrElse(s -> {},
                () -> statusMap.put(StaffStatusEnum.online.getType(), Set.of()));
        Optional.ofNullable(statusMap.get(StaffStatusEnum.stop.getType())).ifPresentOrElse(s -> {},
                () -> statusMap.put(StaffStatusEnum.stop.getType(), Set.of()));
        return Pair.of(unHandleMap, statusMap);
    }

    private Map<Long, Object> staffPauseExclude(int orderType, int maxLimit, List<WorkOrderBase> samplingUnHandle,
                                   Map<Integer, Set<Long>> statusMap, Map<Long, Integer> handleMap) {
        Map<Long, Object> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(samplingUnHandle)) {
            return Map.of();
        }
        Set<Long> overrunStaff = handleMap.entrySet().stream()
                .filter(entry -> entry.getValue() >= maxLimit).map(Map.Entry::getKey).collect(Collectors.toSet());
        if (WorkOrderType.PR_ONLINE_WORK_ORDER_LIST.contains(orderType)) {
            for (WorkOrderBase orderBase : samplingUnHandle) {
                if (orderType == WorkOrderType.pr_online_service.getType()) {
                    Pair<Long, List<Long>> groupPair = groupRule.obtainStaffGroupStaff(orderBase.getId(), orderType, orderBase.getCaseId());
                    //检测待分配工单，处理人是否都暂停
                    Collection<Long> subtract = CollectionUtils.subtract(CollectionUtils.intersection(groupPair.getRight(), statusMap.get(StaffStatusEnum.online.getType())), overrunStaff);
                    if (subtract.size() > 0) {
                        resultMap.put(orderBase.getId(), Map.of("所在组可分配的人", availableMsgConcat(handleMap, subtract)));
                        continue;
                    }
                    //检测待分配工单，处理人是否都暂停
                    if (CollectionUtils.intersection(groupPair.getRight(), statusMap.entrySet().stream().flatMap(entry->entry.getValue().stream()).collect(Collectors.toSet())).size() <= 0) {
                        Response<GroupStaffVo> regularResp = orderStaffConfClient.candidateStaffByRegularGroup(orderType, orderBase.getCaseId());
                        if (regularResp.ok() && Optional.ofNullable(regularResp.getData()).map(GroupStaffVo::getStaffUids).filter(CollectionUtils::isNotEmpty).isPresent()) {
                            Collection<Long> subtract2 = CollectionUtils.subtract(
                                    CollectionUtils.intersection(regularResp.getData().getStaffUids(), statusMap.get(StaffStatusEnum.online.getType())), overrunStaff);
                            if (subtract2.size() > 0) {
                                resultMap.put(orderBase.getId(), Map.of("所在常规组可分配的人", availableMsgConcat(handleMap, subtract2)));
                                continue;
                            }
                        }
                    }

                    continue;
                }

                Response<Long> rpcResult = prPatientClient.getCounselorUidByPatientId(orderBase.getCaseId());
                if (rpcResult.notOk()) {
                    continue;
                }
                if (statusMap.get(StaffStatusEnum.online.getType()).contains(rpcResult.getData()) &&
                        !overrunStaff.contains(rpcResult.getData())) {
                    resultMap.put(orderBase.getId(), Map.of("可分配给顾问", rpcResult.getData()));
                }
            }
        } else {
            List<Long> orderIds = samplingUnHandle.stream()
                    .map(WorkOrderBase::getId).collect(Collectors.toList());
            Map<Long, String> supplementIdMap = Maps.newHashMap();
            if (orderType == WorkOrderType.pr_supplement_material.getType()) {
                supplementIdMap.putAll(workOrderDaoExt.listOrderExtByIdsAndExtNames(orderIds,
                        List.of(OrderExtName.supplementMaterialId.getName())).stream()
                        .collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, WorkOrderExt::getExtValue)));
            }
            Map<Long, List<Integer>> orderDiseaseMap = workOrderDaoExt.listOrderExtByIdsAndExtNames(orderIds,
                    List.of(OrderExtName.disease.getName())).stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,
                            e -> Lists.newArrayList(Splitter.on(",").omitEmptyStrings().split(e.getExtValue()))
                                    .stream().map(Integer::valueOf).collect(Collectors.toList())));
            Map<Integer, WorkOrderBase> recentCaseMap = workOrderDao.listByCaseIdsAndTypes(samplingUnHandle.stream().map(WorkOrderBase::getCaseId).collect(Collectors.toList()), List.of(orderType)).stream()
                    .filter(w -> !orderIds.contains(w.getId()) && !HandleResultEnum.unDoResult().contains(w.getHandleResult()))
                    .collect(Collectors.toMap(WorkOrderBase::getCaseId, Function.identity(), (a, b) -> a.getHandleTime().getTime() > b.getHandleTime().getTime() ? a : b));
            for (WorkOrderBase orderBase : samplingUnHandle) {
                //处理复筛工单
                if (orderType == WorkOrderType.pr_second_screen.getType()) {
                    WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(orderBase.getId(), OrderExtName.firstScreenId.getName());
                    if (workOrderExt != null && StringUtils.isNotBlank(workOrderExt.getExtValue())) {
                        WorkOrderBase previous = workOrderDao.getWorkOrderById(Long.parseLong(workOrderExt.getExtValue()));
                        if (previous != null && previous.getOrderType() == WorkOrderType.pr_2line_first_screen_service.getType()) {
                            if (statusMap.get(StaffStatusEnum.online.getType()).contains(previous.getOperatorId())) {
                                resultMap.put(orderBase.getId(), Map.of("可根据二线处理人分配", previous.getOperatorId()));
                            }
                            if (statusMap.get(StaffStatusEnum.stop.getType()).contains(previous.getOperatorId())) {
                                continue;
                            }
                        }
                    }
                    continue;
                }
                //判断最近一次处理人
                if (Optional.ofNullable(recentCaseMap.get(orderBase.getCaseId())).filter(w->statusMap.get(StaffStatusEnum.online.getType()).contains(w.getOperatorId())).isPresent()) {
                    resultMap.put(orderBase.getId(), Map.of("可分配给最近一次处理人", recentCaseMap.get(orderBase.getCaseId()).getOperatorId()));
                    continue;
                }
                if (Optional.ofNullable(recentCaseMap.get(orderBase.getCaseId())).filter(w->statusMap.get(StaffStatusEnum.stop.getType()).contains(w.getOperatorId())).isPresent()) {
                    continue;
                }
                //判断补采下发人
                if (orderType == WorkOrderType.pr_supplement_material.getType() && StringUtils.isNotBlank(supplementIdMap.get(orderBase.getId()))) {
                    RpcResult<Long> taskRpc = orderRelClient.getIssnueUserIdBySupplementMaterialId(Long.parseLong(supplementIdMap.get(orderBase.getId())));
                    if (Optional.of(taskRpc).filter(RpcResult::isSuccess).filter(w->statusMap.get(StaffStatusEnum.online.getType()).contains(w.getData())).isPresent()) {
                        resultMap.put(orderBase.getId(), Map.of("可以分配给补采下发人", taskRpc.getData()));
                        continue;
                    }
                    if (Optional.of(taskRpc).filter(RpcResult::isSuccess).filter(w->statusMap.get(StaffStatusEnum.stop.getType()).contains(w.getData())).isPresent()) {
                        continue;
                    }
                }

                if (orderDiseaseMap.get(orderBase.getId()) == null) {
                    continue;
                }
                RpcResult<List<Long>> resp = orderStaffConfClient.candidateStaffByDiseases(orderType, orderBase.getCaseId(),
                        orderDiseaseMap.get(orderBase.getId()).stream().map(Objects::toString).collect(Collectors.joining(",")));
                if (resp.isFail()) {
                    continue;
                }
                //检测待分配工单，处理人是否都暂停
                Collection<Long> subtract = CollectionUtils.subtract(CollectionUtils.intersection(resp.getData(), statusMap.get(StaffStatusEnum.online.getType())), overrunStaff);
                if (subtract.size() > 0) {
                    resultMap.put(orderBase.getId(), Map.of("可根据疾病分配的人", availableMsgConcat(handleMap, subtract)));
                    continue;
                }
                //检测待分配工单，处理人是否都暂停
                if (CollectionUtils.intersection(resp.getData(), statusMap.entrySet().stream().flatMap(entry->entry.getValue().stream()).collect(Collectors.toSet())).size() <= 0) {
                    Collection<Long> subtract2 = CollectionUtils.subtract(statusMap.get(StaffStatusEnum.online.getType()), overrunStaff);
                    if (subtract2.size() > 0) {
                        resultMap.put(orderBase.getId(), Map.of("兜底规则可分配的人", availableMsgConcat(handleMap, subtract2)));
                    }
                }
            }
        }

        return resultMap;
    }

    private String availableMsgConcat(Map<Long, Integer> handleMap, Collection<Long> candidateStaff) {
        return candidateStaff.stream().map(s->s+":"+handleMap.get(s)).collect(Collectors.joining("\n"));
    }

    private String assembleMsg(int workOrderType, Map<Long, Object> expMsg) {
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(workOrderType);
        return "-----------------------------\n" +
                "工单类型："+ workOrderTypeDO.getMsg() +"\n" +
                "采样分配异常工单：" + expMsg + "\n";
    }

    private Integer obtainUpperLimit(List<WorkTypeProperty> confList, int orderType) {
        Map<Integer, Integer> handlingMap = confList.stream().collect(Collectors.toMap(
                WorkTypeProperty::getOrderType, v->Integer.parseInt(v.getPropertyValue())));
        Integer maxLimit = handlingMap.get(orderType);
        if (maxLimit == null) {
            return handlingLimitMap.get(orderType);
        }

        return maxLimit;
    }

}

