package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:22
 */
@Service
@Slf4j
public class GroupRule extends AbstractRuleChain{

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;
    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;

    protected GroupRule(@Autowired @Qualifier("groupRegularRule") AbstractRuleChain nextRule) {
        super(nextRule);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        //1. 自定义组规则
        Pair<Long, List<Long>> groupStaffPair = obtainStaffGroupStaff(workOrderId, orderType, patientId);
        if (groupStaffPair == null) {
            return new PrResponsibleVo(0L, null, null);
        }
        List<Long> diseaseStaffs = groupStaffPair.getRight();
        List<StaffStatus> staffStatuses = Optional.of(diseaseStaffs).filter(CollectionUtils::isNotEmpty)
                .map(dss->staffStatusDao.getStaffs(dss, List.of(orderType))).orElse(Collections.emptyList());
        if (staffStatuses.stream().anyMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.online.getType())) {
            //1.1 根据默认选人规则选择一人
            log.info("Group rule candidate staffs:{}, workOrderType:{}", staffStatuses, orderType);
            return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(staffStatuses, orderType, null),
                    PrAllocationEnum.DISEASE, groupStaffPair.getLeft());
        }

        //2. 常规组(所有责任人都离线)
        if (staffStatuses.stream().allMatch(staffStatus -> staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType())) {
            return nextRule.selectResponsible(orderType, workOrderId, patientId);
        }

        return new PrResponsibleVo(0L, null, null);
    }

    /**
     * 查询配置疾病的员工
     * @return null rpc failed, or else success
     */
    public Pair<Long, List<Long>> obtainStaffGroupStaff(long workOrderId, int workOrderType, long patientId) {
        WorkOrderExt orderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, OrderExtName.staffGroupId.getName());
        long groupId = Optional.ofNullable(orderExt).map(ext -> Long.parseLong(ext.getExtValue())).orElse(0L);
        if (groupId > 0) {
            Response<List<Long>> staffResp = orderStaffConfClient.candidateStaffByGroup(workOrderType, groupId, patientId);
            log.info("Obtain candidate staff by group, groupId:{}, orderType:{}, resp:{}", groupId, workOrderType, staffResp);
            if (staffResp.notOk()) {
                return null;
            }
            if (CollectionUtils.isEmpty(staffResp.getData())) {
                log.warn("No staff is set in the group({}) of the work order({})", groupId, workOrderType);
            }
            return Pair.of(groupId, staffResp.getData());
        }

        return Pair.of(0L, Lists.newArrayListWithCapacity(0));
    }
}
