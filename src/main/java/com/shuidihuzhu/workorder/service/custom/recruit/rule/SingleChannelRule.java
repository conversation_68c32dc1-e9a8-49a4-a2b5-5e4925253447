package com.shuidihuzhu.workorder.service.custom.recruit.rule;

import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.pr.client.PrWorkOrderStaffConfClient;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.custom.recruit.enums.PrAllocationEnum;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/9/1 19:27
 */
@Service
@Slf4j
public class SingleChannelRule extends AbstractRuleChain {

    @Resource
    private CommonRuleUtil commonRuleUtil;
    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;
    @Resource
    private PrWorkOrderStaffConfClient orderStaffConfClient;

    public SingleChannelRule() {
        super(null);
    }

    @Override
    public PrResponsibleVo selectResponsible(int orderType, long workOrderId, long patientId) {
        //1.1 按照创建来源查询候选人
        List<Long> orgStaffs = obtainStaffByCreateSource(patientId, orderType, workOrderId);
        List<StaffStatus> staffStatuses = Optional.of(orgStaffs).filter(CollectionUtils::isNotEmpty)
                .map(dss->staffStatusDao.getStaffs(dss, List.of(orderType))).orElse(Collections.emptyList());
        return new PrResponsibleVo(commonRuleUtil.commonSelectResponsible(staffStatuses, orderType, -1),
                PrAllocationEnum.PERMIT, null);
    }

    private List<Long> obtainStaffByCreateSource(long patientId, int workOrderType, long workOrderId) {
        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, OrderExtName.createSource.name());
        Response<List<Long>> resp = orderStaffConfClient.candidateCreateSource(workOrderType, patientId, Integer.parseInt(workOrderExt.getExtValue()));
        log.info("Obtain candidate staff by create source, workOrderType:{}, patientId:{}, resp:{}", workOrderType, patientId, resp);

        List<Long> candidates = Optional.of(resp).filter(Response::ok).map(Response::getData).orElse(List.of());
        if (CollectionUtils.isEmpty(candidates)) {
            log.warn("Channel work order is not configured with permissions, orderType {}, patientId {}", workOrderType, patientId);
        }
        return candidates;
    }

}
