package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.OrderStat;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/6/20
 */
public interface juanzhuanStatService {

    /**
     * 统计全部
     */
    void statAll();

    /**
     * 统计内部员工
     */
    void statInner();

    /**
     * 统计外部员工
     */
    void statOuter();

    /**
     * 统计组织
     */
    void statOrg(List<Integer> orgIds);

    /**
     * 统计人员
     */
    void statUser();

    /**
     * 根据类型和时间查询统计结果
     * @param batchHours
     * @param statType
     * @return
     */
    List<OrderStat> getList(List<Integer> batchHours, int statType);

}
