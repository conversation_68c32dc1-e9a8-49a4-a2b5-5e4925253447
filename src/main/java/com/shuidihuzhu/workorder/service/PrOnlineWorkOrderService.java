package com.shuidihuzhu.workorder.service;


import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.mq.GeneralWorkOrderAssignateImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PrOnlineWorkOrderService {

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private StaffStatusService staffStatusService;
    @Autowired
    private WorkTypePropertyService orderTypePropertyService;
    @Resource(name = "generalWorkOrderAssignateImpl")
    private GeneralWorkOrderAssignateImpl generalWorkOrderAssignateImpl;
    @Autowired
    private WorkOrderDao workOrderDao;

    public long getOnlineAssign(List<String> misIdList){
        int workOrderType = WorkOrderType.pr_online_service.getType();
        Response<List<AuthUserDto>> listResponse = userFeignClient.getByLoginNameList(misIdList);
        if (listResponse.notOk()){
            return 0L;
        }
        List<AuthUserDto> result = listResponse.getData();
        if (CollectionUtils.isEmpty(result)){
            return 0L;
        }

        List<Long> userIds = result.stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
        //查询在线用户
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineAndStopToday(userIds , workOrderType);
        if (CollectionUtils.isEmpty(staffList)){
            return 0L;
        }
        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users,workOrderType, HandleResultEnum.doing.getType());

        //如果返回为空  直接分配给当前在线的人员
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)){
            //查找分配时间早的用户
            return staffStatusService.getStaffOrderByAssignTime(users,workOrderType);
        }

        Map<Long,WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount :: getOperatorId, Function.identity()));

        //确定在线人员工单数量
        list = users.stream().map(r->{
            if (doingMap.get(r) != null){
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r,0);
        }).collect(Collectors.toList());

        //取出最少工单数量
        Optional<WorkOrderDoingCount> minCount = list.stream().min(Comparator.comparing(WorkOrderDoingCount::getNum));

        //如果不存在不分配
        if (minCount.isEmpty()){
            return 0L;
        }
        //取出工单最少的人
        Map<Integer,List<WorkOrderDoingCount>> map = list.stream().collect(Collectors.groupingBy(WorkOrderDoingCount :: getNum));
        List<WorkOrderDoingCount> minUsers = map.get(minCount.get().getNum());


        if (minUsers.size()==1){        //如果只有一个直接分配
            return minUsers.get(0).getOperatorId();
        }

        List<Long> userList = minUsers.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
        //查找分配时间早的用户
        return staffStatusService.getStaffOrderByAssignTime(userList,workOrderType);

    }

}
