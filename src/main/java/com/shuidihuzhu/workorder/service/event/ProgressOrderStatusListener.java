package com.shuidihuzhu.workorder.service.event;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.event.WorkOrderStatusEvent;
import com.shuidihuzhu.workorder.service.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/12/18
 */
@Slf4j
@Component
public class ProgressOrderStatusListener {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Resource
    private OrganizationService organizationService;

    @EventListener
    public void onApplicationEvent(WorkOrderStatusEvent event) {


        if (event == null || event.getWorkOrderBase() == null) {
            return;
        }
        WorkOrderBase base = event.getWorkOrderBase();

        //动态工单创建时  后门直接分配给指定人
        if (base.getOrderType() == WorkOrderType.ugcprogress.getType() && base.getHandleResult()==HandleResultEnum.undoing.getType()){

            //正式环境问题单
            if (base.getCaseId()== 2220386){
                int operatorOrgId = organizationService.getUserOrgId(590L);
                workOrderDao.assignWorkOrder(base.getId(), 590L, operatorOrgId, HandleResultEnum.doing.getType());
            }

            //正式环境QA测试单
            if (base.getCaseId()== 2230808){
                int operatorOrgId = organizationService.getUserOrgId(1421L);
                workOrderDao.assignWorkOrder(base.getId(), 1421L, operatorOrgId, HandleResultEnum.doing.getType());
            }

        }
    }
}
