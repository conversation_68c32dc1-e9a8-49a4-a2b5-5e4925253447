package com.shuidihuzhu.workorder.service.event;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.RejectWorkOrderClose;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.delegate.UserInfoDelegate;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.event.WorkOrderStatusEvent;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @DATE 2019/5/20
 */
@Component
@Slf4j
public class WorkOrderStatusListener {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserOperationRecordDao recordDao;

    @Autowired
    private Analytics analytics;

    @Autowired
    private UserInfoDelegate userInfoDelegate;


    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired(required = false)
    private Producer producer;

    @EventListener
    public void onApplicationEvent(WorkOrderStatusEvent event){

        if (event == null|| event.getWorkOrderBase() == null){
            return;
        }
        log.info("WorkOrderStatusListener event={}",event.getWorkOrderBase() );
        WorkOrderBase base = event.getWorkOrderBase();

        long workOrderId = base.getId();
        //工单id为0的不发送消息
        if (workOrderId != 0){
            WorkOrderResultChangeEvent e = new WorkOrderResultChangeEvent();
            e.setHandleResult(base.getHandleResult());
            e.setWorkOrderId(workOrderId);
            e.setOrderType(base.getOrderType());

            Message<WorkOrderResultChangeEvent> message = MessageBuilder.createWithPayload(e)
                    .setTags(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE)
                    .addKey(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE, workOrderId)
                    .setDelayLevel(DelayLevel.S1)
                    .build();
            producer.send(message);
        }

        //如果是审核工单创建
        if (base.getOrderType() == WorkOrderType.shenhe.getType()){
            //创建时候需要关闭存在的回访工单和驳回工单
            List<Integer> boHuiOrHuiFang = Lists.newArrayList(WorkOrderType.huifang.getType(), WorkOrderType.bohui.getType());
            closeRelatedWork(base, boHuiOrHuiFang);
        }

        //如果是电话工单/高风险创建
        if (base.getOrderType() == WorkOrderType.dianhuashenhe.getType()
            || base.getOrderType() == WorkOrderType.highriskshenhe.getType()
            || base.getOrderType() == WorkOrderType.ai_photo.getType() || base.getOrderType() == WorkOrderType.ai_content.getType()){
            //创建时候需要关闭存在的驳回工单
            List<Integer> boHuiOrHuiFang = Lists.newArrayList(WorkOrderType.bohui.getType());
            closeRelatedWork(base, boHuiOrHuiFang);
        }

        // 如果创建驳回工单  线下BD  实时自动异常关闭
        if (base.getOrderType() == WorkOrderType.bohui.getType() && base.getHandleResult() == HandleResultEnum.undoing.getType()){

            Map<Integer,ChannelRefine.ChannelRefineResuleEnum> m = baseWorkOrderService.getChannel(base.getCaseId());

            //线下渠道和1v1 直接关闭
            if (ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.equals(m.get(base.getCaseId()))
                || ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.equals(m.get(base.getCaseId()))){

                WorkOrderRecord record = new WorkOrderRecord();
                record.setWorkOrderType(base.getOrderType());
                record.setOperatorId(99);
                record.setComment("自动异常关闭-BD发起");

                if (ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.equals(m.get(base.getCaseId()))){
                    record.setComment("自动异常关闭-微信1V1发起");
                }

                record.setOperateMode(OperateMode.handle.getType());
                record.setOperateDesc("系统自动关闭");
                record.setWorkOrderId(base.getId());

                workOrderDao.closeOrderById(Lists.newArrayList(base.getId()),HandleResultEnum.exception_done.getType());
                recordDao.saveRecord(record);
            }
        }


    }


    /**
     * 关闭之前的工单
     */
    private void closeRelatedWork(WorkOrderBase base, List<Integer> types) {
        log.debug("WorkOrderStatusListener close judge base:{}", base);
        //创建时候需要关闭存在的回访工单和驳回工单
        if (base.getHandleResult() == HandleResultEnum.undoing.getType() ||
                base.getHandleResult() == HandleResultEnum.doing.getType()){

            int caseId = base.getCaseId();

            List<Integer> results = Lists.newArrayList(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType());

            List<WorkOrderBase> list = workOrderDao.getOrderByCaseId(caseId, types, results);

            if (CollectionUtils.isEmpty(list)){
                return;
            }

            List<WorkOrderRecord> records = Lists.newArrayList();
            List<Long> ids = Lists.newArrayList();
            list.stream().forEach(r->{

                ids.add(r.getId());

                WorkOrderRecord record = new WorkOrderRecord();
                record.setWorkOrderType(r.getOrderType());
                record.setOperatorId(0);
                record.setComment("系统自动结束");
                record.setOperateMode(OperateMode.handle.getType());
                record.setOperateDesc("审核工单创建结束驳回和回访工单");
                record.setWorkOrderId(r.getId());
                records.add(record);

            });
            workOrderDao.closeOrderById(ids,HandleResultEnum.exception_done.getType());
            recordDao.saveRecordList(records);

            log.info("WorkOrderStatusListener close caseId={} result={}", caseId, ids);

            analytics(list);
        }
    }



    /**
     * 上报驳回工单关闭
     * @param list
     */
    private void analytics(List<WorkOrderBase> list){

        list.stream().forEach(r->{

            if (r.getOrderType() == WorkOrderType.bohui.getType()){
                analytics(r.getCaseId(),r.getId());
            }
        });
    }

    private void analytics(int caseId,long workOrderId){

        RejectWorkOrderClose rwoc = new RejectWorkOrderClose();

        try {

            FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(caseId));

        if (feignResponse == null || feignResponse.notOk()){
            return ;
        }
        List<CrowdfundingInfo> list = feignResponse.getData();
        if (CollectionUtils.isEmpty(list)){
            return ;
        }
        CrowdfundingInfo info = list.get(0);

        if (info == null){
            log.error("analytics info==null caseId={}",caseId);
            return;
        }
        long userId = info.getUserId();

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel != null){
            rwoc.setUser_encrypt_mobile(StringUtils.trimToEmpty(userInfoModel.getMobile()));
        }

        rwoc.setClose_reason("审核工单创建关闭驳回工单");
        rwoc.setWork_order_id(workOrderId);
        rwoc.setCase_id(StringUtils.trimToEmpty(info.getInfoId()));

        Optional<Integer> infoId = Optional.ofNullable(info.getId());
        rwoc.setInfo_id(Long.valueOf(infoId.orElse(0)));
        rwoc.setUser_tag(String.valueOf(userId));
        rwoc.setUser_tag_type(UserTagTypeEnum.userid);

        analytics.track(rwoc);
        log.info("大数据打点上报,驳回工单关闭:{}", JSONObject.toJSONString(rwoc));

        } catch (Exception e){
            log.error("大数据打点上报异常,驳回工单关闭:{}", JSONObject.toJSONString(rwoc), e);
        }

    }

}
