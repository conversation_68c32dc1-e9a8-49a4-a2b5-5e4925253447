package com.shuidihuzhu.workorder.service.event;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.event.WorkOrderStatusEvent;
import com.shuidihuzhu.workorder.service.OrganizationService;
import com.shuidihuzhu.workorder.service.StaffAssignPlugins;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: fengxuan
 * @create 2020-01-07 16:00
 * <p>
 * QA创建的工单只分给对应的QA,防止被抢单
 **/
@Component
@Slf4j
public class SpecialAssignListener {

    @Autowired
    private StaffAssignPlugins staffAssignPlugins;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Resource
    private OrganizationService organizationService;

    private static final String KEY_QA_ASSIGN_ORDER_TYPE = "apollo.qa-assign.order-type-list";

    @EventListener
    public void onApplicationEvent(WorkOrderStatusEvent event) {
        if (event == null || event.getWorkOrderBase() == null) {
            return;
        }
        WorkOrderBase base = event.getWorkOrderBase();
        final int orderTypeCode = base.getOrderType();
//        Integer oneFromTwo = ClassifyTypeEnum.getOneFromTwo(orderTypeCode);
        final String orderTypeListStr = ConfigService.getAppConfig().getProperty(KEY_QA_ASSIGN_ORDER_TYPE, "");
        final Set<Integer> orderTypeSet = Arrays.stream(StringUtils.split(orderTypeListStr, ","))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
        //工单创建分配后门
        if (!orderTypeSet.contains(orderTypeCode)) {
            log.debug("qa-assign 不在qa特殊分配配置中");
            return;
        }
        if (base.getHandleResult() != HandleResultEnum.undoing.getType()) {
            log.debug("qa-assign 工单不是待分配");
            return;
        }
        //防止异常影响主流程
        try {
            OpResult<Long> staffByPlugin = staffAssignPlugins.getStaffByPlugin(orderTypeCode, base);
            if (staffByPlugin.isSuccess() && staffByPlugin.getData() != null && staffByPlugin.getData() > 0) {
                int operatorOrgId = organizationService.getUserOrgId(staffByPlugin.getData());
                int result = workOrderDao.assignWorkOrder(base.getId(), staffByPlugin.getData(), operatorOrgId, HandleResultEnum.doing.getType());
                log.info("QA创建的案例分配给QA special assign event:{} userId:{} resutlt={}", event.getWorkOrderBase(), staffByPlugin.getData(),result);
            }
        } catch (Exception e) {
            log.error("SpecialAssignListener error", e);
        }
    }

}
