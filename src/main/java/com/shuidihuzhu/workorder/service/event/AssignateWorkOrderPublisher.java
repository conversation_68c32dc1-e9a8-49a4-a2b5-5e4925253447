package com.shuidihuzhu.workorder.service.event;

import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @time 2019/3/14 上午11:54
 * @desc 发送分配工单事件
 */
@Service
public class AssignateWorkOrderPublisher {
    @Autowired
    ApplicationContext applicationContext;

    public void publishEvent(AssignateWorkOrderEvent event){
        applicationContext.publishEvent(event);
    }

}
