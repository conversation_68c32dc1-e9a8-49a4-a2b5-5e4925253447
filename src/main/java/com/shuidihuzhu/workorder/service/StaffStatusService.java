package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.StaffStat;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.model.view.StaffSelectVO;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.core.model.view.StaffStatusStatVO;
import lombok.NonNull;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */
public interface StaffStatusService {

    /**
     * 修改员工在线状态
     * @param staffStatus
     * @return
     */
    int changeStatus(StaffStatus staffStatus);


    /**
     * 查询员工状态
     * @param userId
     * @param orderType
     * @return
     */
    StaffStatus getStaffStatus(long userId, int orderType);


    /**
     * 查询员工状态
     * @param userId
     * @return
     */
    List<StaffStatus> getStaffStatusByTypes(long userId,List<Integer> types);

    List<StaffStatus> getStaffs(List<Long> userId, int orderType);

    /**
     * 获取今天在线的员工
     * @param userId
     * @param orderType
     * @return
     */
    List<StaffStatus> getStaffOnlineToday(List<Long> userId,int orderType);

    /**
     * 获取今天在线或暂停员工
     */
    List<StaffStatus> getStaffOnlineAndStopToday(List<Long> userIds, int type);

    /**
     * 获取今天在线或暂停或离线的员工
     */
    List<StaffStatus> getStaffOnlineAndStopAndOfflineToday(List<Long> userIds, int orderType);

    /**
     * 更新用户最新分配时间
     * @param userId
     * @return
     */
    int addStaffAssignTime(long userId,int orderType);

    /**
     * 根据分配时间查询用户
     * @param users
     * @return
     */
    long getStaffOrderByAssignTime(List<Long> users,int orderType);


    /**
     * 根据工单类型 查询员工状态数量
     * @param userId
     * @return
     */
    List<StaffStat> getStaffStatusStat(long userId);


    /**
     * 分页查询用户
     * @param types
     * @param pageSize
     * @param paging
     * @param userId
     * @return
     */
    OpResult<PageResult<StaffStatus>> getStaffStatusByTypes(List<Integer> types,
                                                            int pageSize,
                                                            String paging,
                                                            long userId,
                                                            boolean isAll,
                                                            long operatorId);


    /**
     * 根据权限查询全部用户
     * @return
     */
    Set<StaffStatus> getStaffStatByPermissions();


    /**
     * 自动暂停  当前状态是 && 在线上线时间<今天凌晨
     *
     */
    void autoOff();

    /**
     * 举报处理人员每天00:00自动离线
     */
    void reportAutoOff();

    /**
     * 获取是否离线
     * @param userId 运营id
     * @param orderType {@link WorkOrderType}
     * @return
     */
    @Nullable
    Boolean isOffline(long userId, int orderType);

    int countOfflineTimesToday(@NonNull StaffStatus staffStatus);

    /**
     * 根据工单类型获取工作人员
     *
     * @param orderType
     * @return
     */
    List<StaffStatus> getStaffStatusByOrderType(String orderType);

    /**
     * 清除掉离职的员工
     */
    void clearOffJobUsers();

    /**
     * 让所有员工允许自动在线
     */
    void clearAutoOfflineStatus();

    /**
     * 是否可以自动在线
     * @param staffStatus
     */
    void changeAutoOnLineStatus(StaffStatus staffStatus);

    /**
     * 判断用户是否可以设置在线状态
     */
    boolean canSelfOnline(StaffStatus staffStatus);

    /**
     * 判读用户是否可以同时在线
     * @param staffStatus
     * @return
     */
    boolean canBothOnline(StaffStatus staffStatus);

    /**
     * 发送分配工单事件
     * @param orderType
     */
    void publishEvent(int orderType);

    long calcTodayPauseDuration(StaffStatus staffStatus, long userId, int orderType);

    int getDistinctUserOnlineCount(List<Integer> orderTypes, List<Long> userIds);


    /**
     * 设置他人可领取工单类型
     * @param userId
     * @param allowAssign
     */
    void allowAssign(long userId,String allowAssign);

    /**
     * 按照类型更新分配阈值
     * @param orderType
     * @param threshold
     * @return
     */
    int updateThresholdByType( int orderType, int threshold);

    /**
     * 手动离线
     * @param userIds
     * @param startTime
     * @param endTime
     * @return
     */
    int freeByUserId(List<Long> userIds, int orderType, Date startTime, Date endTime);

    /**
     * 查询阈值
     * @param orderType
     * @param userId
     * @return
     */
    StaffStatus getThresholdByType( int orderType, Long userId);


    List<StaffStat> getStaffStatusStatByPermissions(long userId, List<String> prPermissions);

    StaffStatusStatVO getStatusStatV2(StaffStatus user, long userId, int type);

    void onOrderCreate(WorkOrderBase wordOrder);

    Response<List<StaffSelectVO>> getAllStaffOfSelect(String orderType);
}
