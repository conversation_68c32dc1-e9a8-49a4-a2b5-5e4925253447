package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
@Slf4j
public abstract class WorkOrderFacade<T extends WorkOrderBase,R extends HandleOrderParam,S extends WorkOrderListParam> implements WorkOrderService<T,R,S> {

    @Autowired
    private UserOperationRecordDao recordDao;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    AssignateWorkOrderPublisher publisher;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private MeterRegistry meterRegistry;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private OrderGroupService orderGroupService;

    public OpResult<Long> doCreate(T wordOrder) {

        OpResult result = vlidate(wordOrder);

        RLock rLock = null;
        try {
            String lockName = getKey(wordOrder);

            if (result != null && result.isSuccess()){

                rLock = cf2RedissonHandler.getLock(lockName);
                if (!rLock.tryLock()){
                    log.info("doCreate caseId={} ordertype={}",wordOrder.getCaseId(),wordOrder.getOrderType());
                    return OpResult.createFailResult(ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
                }

                int operatorOrgId = organizationService.getUserOrgId(wordOrder.getOperatorId());
                wordOrder.setOperatorOrgId(operatorOrgId);

                //插入主表
                OpResult<Long> opResult = create(wordOrder);

                log.info("doCreate param={} result code={} msg={}",wordOrder,opResult.getErrorCode(),opResult.getMessage());

                if (opResult.isSuccess()){
                    //插入扩展表
                    if (CollectionUtils.isNotEmpty(wordOrder.getWorkOrderExt())){
                        orderExtService.createWorkOrderExt(wordOrder.getWorkOrderExt());
                    }
                    //工单操作记录
                    WorkOrderRecord workOrderRecord = WorkOrderRecord.createRecord(wordOrder);
                    if (WorkOrderType.QC_WORK_ORDER_LIST.contains(wordOrder.getOrderType()) && Objects.nonNull(workOrderRecord)) {
                        QcWorkOrder qcWorkOrder = (QcWorkOrder) wordOrder;
                        workOrderRecord.setOperatorId(qcWorkOrder.getLoginUserId());
                    }
                    this.saveWorkOrderRecord(workOrderRecord);
                    if (WorkOrderType.QC_WORK_ORDER_LIST.contains(wordOrder.getOrderType()) && wordOrder.getOperatorId() > 0) {
                        this.addAssignLog((QcWorkOrder) wordOrder, opResult.getData());
                    }

                    //发送工单状态变化事件
                    EventPublishHelper.sendOrderStatusChangeOld(this, wordOrder);

                    //发送分配工单event
                    EventPublishHelper.sendOrderAssign(this, wordOrder.getOrderType());

                    // 发送工单创建mq
                    WorkOrderResultChangeEvent e = new WorkOrderResultChangeEvent();
                    e.setHandleResult(wordOrder.getHandleResult());
                    e.setWorkOrderId(wordOrder.getId());
                    e.setOrderType(wordOrder.getOrderType());
                    Message message = Message.of(MQTag.CF,
                            CfClientMQTagCons.WORK_ORDER_CREATE,
                            String.valueOf(wordOrder.getId()),
                            e);
                    producer.send(message);

                }

                return opResult;
            }
        }catch (Exception e){
            log.error("doCreate",e);
        }finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }

        return result;
    }

    private void addAssignLog(QcWorkOrder wordOrder, long workOrderId) {
        try {
            var userName = organizationService.getUserName(wordOrder.getOperatorId());
            WorkOrderRecord orderRecord = WorkOrderRecord.qcAssignationRecord(workOrderId,
                    wordOrder.getOrderType(), wordOrder.getLoginUserId(), userName);
            this.saveWorkOrderRecord(orderRecord);
        } catch (Exception e) {
            log.error("addAssignLog error", e);
        }
    }


    public OpResult doHandle(R param) {

        OpResult opResult = this.handle(param);

        log.info("doHandle param={} result={}", param, opResult.getErrorCode());

        if (opResult.isSuccess()){
            //工单操作记录
            WorkOrderRecord workOrderRecord = WorkOrderRecord.handleRecord(param);
            this.saveWorkOrderRecord(workOrderRecord);
        }
        if(opResult.isSuccess()){
            orderGroupService.onOrderHandle(param.getWorkOrderId());

            WorkOrderBaseVo base = new WorkOrderBaseVo();
            base.setId(param.getWorkOrderId());
            base.setOrderType(param.getOrderType());
            base.setHandleResult(param.getHandleResult());
            base.setCaseId(param.getCaseId());

            //发送工单状态变化事件
            EventPublishHelper.sendOrderStatusChangeOld(this, base);

            //处理完成，发送分配工单event
            EventPublishHelper.sendOrderAssign(this, param.getOrderType());
        }

        meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION,OperationStat.handle,
                OperationStat.ORDERTYPE,param.getOrderType()+"",
                OperationStat.handle_result,param.getHandleResult()+""
        ).increment();

        return opResult;

    }

    /**
     * 工单操作记录
     * @param workOrderRecord
     */
    private void saveWorkOrderRecord(WorkOrderRecord workOrderRecord) {
        if (workOrderRecord == null){
            return;
        }
        recordDao.saveRecord(workOrderRecord);
    }



    private String getKey(T wordOrder){
        return "create_"+wordOrder.getCaseId()+"_"+wordOrder.getOrderType();
    }


    public Map<Long,String> getExt(List<Long> orderIds, String name){

        List<WorkOrderExt> list = daoExt.getWorkOrderExts(orderIds,name);

        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }

        return list.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,WorkOrderExt::getExtValue,(o1, o2)->o2));
    }
}
