package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.SimpleTransferParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.enums.OperateMode;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssignWorkOrderService {

    void assignWorkOrder(WorkOrderBase workOrder, Long operatorId);

    int assignWorkOrderByIds(List<Long> orderIds, int orderType, long operatorId, long assignerId, String comment, OperateMode operateMode);

    int assignWorkOrder(List<WorkOrderBase> workOrders, int orderType, long operatorId, long assignerId, String comment, OperateMode operateMode);

    void assignWorkOrder(List<WorkOrderBase> workOrders, Long operatorId);

    Response<Long> assignWorkOrder(long workOrderId, long operatorId);

    Response<Long> manualAssignWorkOrder(long workOrderId, long operatorId, long assignerId);

    Response<Long> callbackOrderAndAssign(long workOrderId, long operatorId, long assignerId);

    Response<Long> reprocessWorkOrder(long workOrderId, long operatorId, String comment);

    Response<Long> releaseWorkOrder(long workOrderId, long operatorId, String comment);

    /**
     * 质检系统修改质检结果后重新分配
     */
    Response<Long> qcAgainAssignWorkOrder(long workOrderId, long operatorId, long assignerId, String comment);


    /**
     * 质检工单分配
     */
    Response<Long> qcAssignWorkOrder(long workOrderId, long operatorId, long assignerId);

    /**
     * 转单，忽略当前工单是否有处理人
     * @param workOrderList
     * @param destOptUid 转单处理人
     * @param operatorId 转单人
     * @param reason 转单原因
     * @return 转单成功的工单ID
     */
    List<Long> transferWorkOrder(List<Long> workOrderList, Long destOptUid, Long operatorId, String reason, List<Integer> unChangeHandleResult);

    void simpleTransfer(List<SimpleTransferParam> simpleTransferParams);
}
