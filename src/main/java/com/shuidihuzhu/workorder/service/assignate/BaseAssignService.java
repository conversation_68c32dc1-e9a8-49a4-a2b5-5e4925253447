package com.shuidihuzhu.workorder.service.assignate;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockHelper;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderExtTableParam;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import com.shuidihuzhu.workorder.core.service.core.*;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.assign.VonAssignContext;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.IAssignService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseAssignService implements IAssignService {

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Resource
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private StaffStatusService staffStatusService;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private OrderGroupService orderGroupService;

    @Resource
    private VonPermissionService vonPermissionService;

    @Autowired
    private WorkOrderExtService orderExtService;

    @Resource
    private AssignGroupService assignGroupService;

    @Resource
    private AssignStatService assignStatService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    private static final String TAG = " VonAssignPlugin ";
    private static final int DEFAULT_ONE_TIME_ASSIGN_LIMIT = 200;

    /**
     * 默认的组类型工单分配逻辑，比如高风险工单，orderType=18 就走这里
     */
    @Override
    public Response<Void> doAssign(int orderType) {
        StopWatch stopWatch = StopWatch.createStarted();
        log.info("base assign start");
        // 设置 VonAssignContext 的 orderTypeCode
        VonAssignContext vonAssignContext = VonAssignContext.create(orderType);
        // 开始处理
        VonAssignContext context = handle(vonAssignContext);
        Response<?> resp = context.getResponse();
        stopWatch.stop();
        log.info("base assign end time type {}, time {}", orderType, stopWatch.getTime());
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
    }

    public abstract List<Integer> getOrderTypeCodeList();

    /**
     * 实现一个工单分配逻辑，需求如下
     * 工单有多种类型
     * 不同的处理人所能处理的工单类型不同
     * 工单有优先级
     *
     * @param context
     * @return
     */
    public VonAssignContext handle(VonAssignContext context) {
        // msg: "VonAssignPlugin on start" and msg : "18" 看一下高风险工单的自动分配逻辑
        log.info("{} on start {}", TAG, context);
        int orderType = context.getOrderTypeCode();
        context.setResponse(NewResponseUtil.makeSuccess(null));
        /**
         *          高风险工单配置，除了这些，其他的都是默认值
         *          config_key                     config_value
         *          assign.isUseOldAssigner	    false
         *          assign.assignLimitCount	    1
         *          assign.priorAssignLastAssign	true
         *          assign.workOrderReprocess   	true
         *          assign.workOrderReminder	    true
         */
        // 根据工单类型获取配置，如果没有，就默认配置
        /**
         * 默认配置
         *
         * isUseOldAssigner：如果 orderType <= 65 && WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType) 为 true isUseOldAssigner = true
         *
         * assignLimitCount = 5
         *
         * notAssignDoingOperator = false
         *
         * assignCountPearOperator = 1
         *
         * priorAssignLastAssign = false
         *
         * autoAssign = true
         *
         * priorAssignerEnable = false
         *
         * downWhenGroupNoOnlineUser = false
         *
         * dayAssignLimitEnable = false
         *
         * groupAssignCheckOrderPermission = true
         */
        VonAssignConfig config = vonConfigFacade.getAssignConfig(orderType);

        // 默认自动分配为false，就直接返回
        if (!config.getAutoAssign()) {
            context.setResponse(NewResponseUtil.makeSuccess(null));
            return context;
        }
        // Micrometer（一个应用监控指标框架，常用于 Spring Boot + Prometheus/Grafana 体系）记录指标的。
        meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION, OperationStat.assignate,
                OperationStat.ORDERTYPE, orderType + "",
                OperationStat.ONE_TYPE, workOrderTypeService.getOneFromTwo(orderType) + "").increment();

        // 开始处理
        Supplier<VonAssignContext> vonAssignContextSupplier = () -> handleAssign(context);
        String lockName = getLockKey(orderType);
        Response<VonAssignContext> resp = RedisLockHelper.callWithLock(cf2RedissonHandler, vonAssignContextSupplier, lockName, 0);

        if (resp.notOk()) {
            return buildContextWithError(context, ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
        }
        return resp.getData();
    }

    @NotNull
    public VonAssignContext handleAssign(VonAssignContext context) {
        // 获取工单类型和配置
        int orderType = context.getOrderTypeCode();
        VonAssignConfig config = vonConfigFacade.getAssignConfig(orderType);
        context.setConfig(config);

        // 检查是否是组分配
        Set<Integer> groupSet = orderGroupService.getGroupSet();
        boolean inGroup = groupSet.contains(orderType);
        context.setGroupAssign(inGroup);

        // 每次触发只分配一定数量的工单。暂不需要全部分配,现有业务足够
        // 默认200
        int queryLimit = DEFAULT_ONE_TIME_ASSIGN_LIMIT;
        if (config.getAssignCountPearOperator() > DEFAULT_ONE_TIME_ASSIGN_LIMIT) {
            queryLimit = config.getAssignCountPearOperator();
        }
        // 获取最少攒多少工单才会分配一次，必须小于 queryLimit(工单一次查询量)
        Integer minAssignOrderCount = config.getMinAssignOrderCount();
        if (minAssignOrderCount != null) {
            if (queryLimit < minAssignOrderCount) {
                log.error("{} 配置错误 ", TAG, new RuntimeException("配置的最小工单积攒数应小于工单一次查询量"));
            }
        }

        // 未指定组分配
        // 查询一些未分配组的工单，默认限制200条
//        select *
//        from work_order
//        where order_type=?
//        and handle_result=0
//        and operator_id=0 AND is_delete=0
//        and assign_group_id =0
//        order by order_level desc, create_time asc
//        limit 200
        List<WorkOrderBase> workOrderBases = workOrderDao.getUndoGroupOrderList(orderType,
                HandleResultEnum.undoing.getType(), queryLimit, 0);

        // 对未分配组的工单进行处理
        assignOrderList(context, workOrderBases);

        // 分配组分配
        /**
         * 查询一些分配了组的工单，并处理
         * select *
         * from von_assign_group
         * where order_type = ？
         */
        final List<AssignGroupDO> groupList = assignGroupService.getListByOrderType(orderType);
        for (AssignGroupDO group : groupList) {
            /**
             * select *
             * from work_order
             * where order_type= ？
             * and handle_result=0
             * and operator_id=0 AND is_delete=0
             * and assign_group_id = ？
             * order by order_level desc, create_time asc
             */
            List<WorkOrderBase> groupWorkOrder = workOrderDao.getUndoGroupOrderList(orderType,
                    HandleResultEnum.undoing.getType(), queryLimit, group.getId());
            assignOrderList(context, groupWorkOrder);
        }

        return context;
    }

    private int calculateCurDateDiff(Timestamp dateTime) {
        Timestamp curDateTime = DateUtil.getTimestampFromShortString(DateUtil.getCurrentDateStr());
        return (int) DateUtil.diffDate(dateTime, curDateTime);
    }

    private VonAssignContext assignOrderList(VonAssignContext context, List<WorkOrderBase> workOrderBases) {
        // 获取工单类型和工单配置
        final int orderType = context.getOrderTypeCode();
        final VonAssignConfig config = context.getConfig();
        if (CollectionUtils.isEmpty(workOrderBases)) {
            log.debug("{} no order ", TAG);
            return buildContextWithError(context, ErrorCode.NOT_MORE_WORK_ORDER);
        }

        workOrderBases = filterWorkOrder(workOrderBases, orderType);
        if (CollectionUtils.isEmpty(workOrderBases)) {
            log.debug("{} filter no order ", TAG);
            return buildContextWithError(context, ErrorCode.NOT_MORE_WORK_ORDER);
        }

        Integer minAssignOrderCount = config.getMinAssignOrderCount();
        if (minAssignOrderCount != null) {
            if (CollectionUtils.size(workOrderBases) < minAssignOrderCount) {
                log.debug("{} 工单积攒数量不足阈值先不分配", TAG);
                return buildContextWithError(context, ErrorCode.NOT_MORE_WORK_ORDER);
            }
        }

        // 批量分配
        List<List<WorkOrderBase>> orders = Lists.partition(workOrderBases, config.getAssignCountPearOperator());

        for (List<WorkOrderBase> workOrders : orders) {

            log.info("{} onOrderAssignStart {}", TAG, JSON.toJSONString(workOrders));

            // 批量分配不支持工单业务数据级别分配
            // 每一组拿一个工单信息放到上下文中
            context.setWorkOrder(workOrders.get(0));
            // 返回操作人id
            Long operatorId = getAssignor(context);
            context.cleanAssigner();
            log.info("doAssign work order type={} getAssignStaff:{}.", orderType, operatorId);

            // 查不到人 结束该组分配
            boolean noUser = null == operatorId || 0 == operatorId;
            if (noUser) {
                return buildContextWithError(context, ErrorCode.NOT_MORE_STAFF_ASSIGNATE);
            }

            // 真正的分配工单
            assignWorkOrderService.assignWorkOrder(workOrders, operatorId);
            log.info("doAssign work order success.type={},operatorId={}", orderType, operatorId);
        }
        return context;
    }

    public List<WorkOrderBase> filterWorkOrder(List<WorkOrderBase> workOrderBases, int orderTypeCode) {
        return workOrderBases;
    }

    public Long getAssignor(VonAssignContext context) {
        WorkOrderBase workOrder = context.getWorkOrder();
        int orderTypeCode = context.getOrderTypeCode();
        log.debug("{} 查询领取人 orderType {}, orders {}", TAG, orderTypeCode, workOrder);
        if (workOrder == null) {
            log.debug("{} 查询领取人 工单列表为空", TAG);
            return null;
        }

        // 遍历3个processors，然后调用process方法，context里面有工单类型Code和工单配置以及一个工单信息
        List<AssignerProcessor> processors = getProcessor();
        for (AssignerProcessor p : processors) {
            p.process(context);
            if (context.getAssignerUser() != null) {
                return context.getAssignerUser();
            }
        }
        log.debug("{} 查询领取人 结束 orderType {}, orders {}", TAG, orderTypeCode, workOrder);
        return null;
    }

    protected List<AssignerProcessor> getProcessor() {
        return Lists.newArrayList(
                priorAssignLastAssignProcessor,
                userQueryProcessor,
                userDoingFilterProcessor
        );
    }

    protected AssignerProcessor priorAssignLastAssignProcessor = new AssignerProcessor() {
        @Override
        public VonAssignContext process(VonAssignContext context) {
            // 获取工单配置和工单信息
            VonAssignConfig config = context.getConfig();
            WorkOrderBase workOrder = context.getWorkOrder();
            // 检查是否是组分配
            Set<Integer> groupSet = orderGroupService.getGroupSet();
            boolean inGroup = groupSet.contains(context.getOrderTypeCode());

            // 一人分配制
            if (config.getPriorAssignLastAssign()) {
                long lastOperatorId = getLastOperatorId(workOrder, inGroup);
                if (lastOperatorId != 0) {
                    log.info("{} 查询领取人B 一人分配制 lastOperatorId {}", TAG, lastOperatorId);
                    context.setAssignerUser(lastOperatorId);
                }
            }
            return context;
        }
    };

    protected AssignerProcessor userQueryProcessor = new AssignerProcessor() {
        @Override
        public VonAssignContext process(VonAssignContext context) {
            VonAssignConfig config = context.getConfig();
            WorkOrderBase workOrder = context.getWorkOrder();
            int orderType = context.getOrderTypeCode();
            Boolean inGroup = context.getGroupAssign();

            List<StaffStatus> staffList = queryOnlineStaff(orderType, workOrder.getAssignGroupId(), config);

            if (CollectionUtils.isEmpty(staffList)) {
                log.info("{} 查询领取人C 没有在线用户", TAG);
                context.setAssignerUser(0L);
                return context;
            }

            // 日接单限制
            final Boolean dayAssignLimitEnable = context.getConfig().getDayAssignLimitEnable();
            if (dayAssignLimitEnable) {
                List<StaffStatus> passStaffList = Lists.newArrayList();
                for (StaffStatus staffStatus : staffList) {
                    int count = assignStatService.getCurrentDayAssignCount(context.getOrderTypeCode(),
                            staffStatus.getUserId());
                    final boolean canAssign = count < staffStatus.getReceiptThreshold();
                    log.debug("日接单限制检查 orderType {}, userId {}, canAssign {}, count {}, limit {}",
                            context.getOrderTypeCode(), staffStatus.getUserId(), canAssign, count, staffStatus.getReceiptThreshold());
                    if (canAssign) {
                        passStaffList.add(staffStatus);
                    }
                }
                staffList = passStaffList;
            }

            //在线用户
            List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
            // 过滤分配中新来的用户
            if (inGroup) {
                users = orderGroupService.filterUser(users);
            }
            if (CollectionUtils.isEmpty(users)) {
                log.info("{} 查询领取人D 都被过滤了", TAG);
                context.setAssignerUser(0L);
                return context;
            }
            context.setUsers(users);
            return context;
        }
    };

    protected AssignerProcessor userDoingFilterProcessor = new AssignerProcessor() {
        @Override
        public VonAssignContext process(VonAssignContext context) {
            VonAssignConfig config = context.getConfig();
            WorkOrderType workOrderType = context.getOrderType();
            int orderType = context.getOrderTypeCode();
            Boolean inGroup = context.getGroupAssign();
            List<Long> users = context.getUsers();
            int count = config.getAssignLimitCount();

            if (CollectionUtils.isEmpty(users)) {
                log.info("{} 查询领取人 无用户 查询在线人员处理中都为空 直接查找分配时间早的用户", TAG);
                context.setAssignerUser(0L);
                return context;
            }

            //查询在线用户处理中工单数量 若为组分配则查整组类型处理中工单
            List<WorkOrderDoingCount> list = workOrderDao.getDoingCountByOrderTypes(
                    users,
                    inGroup ? orderGroupService.getGroupList() : Lists.newArrayList(orderType),
                    HandleResultEnum.doing.getType()
            );

            //如果返回为空  直接分配给当前在线的人员
            if (CollectionUtils.isEmpty(list)) {
                log.info("{} 查询领取人E 查询在线人员处理中都为空 直接查找分配时间早的用户", TAG);
                //查找分配时间早的用户
                long user = staffStatusService.getStaffOrderByAssignTime(users, orderType);
                context.setAssignerUser(user);
                return context;
            }


            // 去除有处理中工单的人 组分配只要有处理中就不分配
            if (config.getNotAssignDoingOperator() || inGroup) {
                List<Long> doingUsers = list.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
                users.removeAll(doingUsers);
                if (CollectionUtils.isEmpty(users)) {
                    log.info("{} 查询领取人F 去除处理中用户后 无符合用户", TAG);
                    context.setAssignerUser(0L);
                    return context;
                }
                log.info("{} 查询领取人G 去除处理中用户后 直接查找分配时间早的用户", TAG);
                long user = staffStatusService.getStaffOrderByAssignTime(users, orderType);
                context.setAssignerUser(user);
                return context;
            }


            Map<Long, WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));

            //确定在线人员工单数量
            list = users.stream().map(r -> {
                if (doingMap.get(r) != null) {
                    return doingMap.get(r);
                }
                return new WorkOrderDoingCount(r, 0);
            }).collect(Collectors.toList());

            //取出最少工单数量
            Optional<WorkOrderDoingCount> minCount = list.stream().filter(r -> r.getNum() < count).min(Comparator.comparing(WorkOrderDoingCount::getNum));

            //如果不存在不分配
            if (minCount.isEmpty()) {
                log.info("{} 查询领取人H 没有符合条件的用户 在线用户都已达处理中上限", TAG);
                return context;
            }
            //取出工单最少的人
            Map<Integer, List<WorkOrderDoingCount>> map = list.stream().filter(r -> r.getNum() < count).collect(Collectors.groupingBy(WorkOrderDoingCount::getNum));
            List<WorkOrderDoingCount> minUsers = map.get(minCount.get().getNum());


            if (minUsers.size() == 1) {        //如果只有一个直接分配
                log.info("{} 查询领取人I 只有一人符合 直接分配", TAG);
                long user = minUsers.get(0).getOperatorId();
                context.setAssignerUser(user);
                return context;
            }

            log.info("{} 查询领取人Z 多个用户处理中数量一致 查找分配时间早的用户分配", TAG);
            List<Long> userList = minUsers.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
            //查找分配时间早的用户
            long user = staffStatusService.getStaffOrderByAssignTime(userList, orderType);
            context.setAssignerUser(user);
            return context;
        }
    };

    protected Map<String, String> promoteExtMap(VonAssignContext context) {
        if (null != context.getExtMap()) {
            return context.getExtMap();
        }
        WorkOrderBase workOrder = context.getWorkOrder();
        long workOrderId = workOrder.getId();
        Map<String, String> extMap = orderExtService.getExtMapByOrderId(workOrderId);
        context.setExtMap(extMap);
        return extMap;
    }

    private VonAssignContext buildContextWithError(VonAssignContext context, ErrorCode error) {
        EhResponse<Object> resp = EhResponseUtils.fail(error);
        context.setResponse(resp);
        return context;
    }

    public List<StaffStatus> queryOnlineStaff(int orderType, long assignGroupId, VonAssignConfig config) {
        Boolean downWhenGroupNoOnlineUser = config.getDownWhenGroupNoOnlineUser();
        final Boolean groupAssignCheckOrderPermission = config.getGroupAssignCheckOrderPermission();
        List<StaffStatus> users = getOnlineStaff(orderType, assignGroupId, groupAssignCheckOrderPermission);
        // 若无在线 且 允许降级 则查询全部组在线人员分配
        if (downWhenGroupNoOnlineUser && CollectionUtils.isEmpty(users) && assignGroupId > 0) {
            return getOnlineStaff(orderType, 0L, groupAssignCheckOrderPermission);
        }
        return users;
    }

    public List<StaffStatus> getOnlineStaff(int orderType, long assignGroupId, Boolean groupAssignCheckOrderPermission) {
        List<Long> userIds = getHasPermissionUserIds(orderType, assignGroupId);
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        // 走组分配需要再查一次工单权限 过滤
        if (assignGroupId > 0 && groupAssignCheckOrderPermission) {
            userIds = filterOrderUser(orderType, userIds);
        }
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        //查询在线用户

        return staffStatusService.getStaffOnlineToday(userIds, orderType);
    }

    private List<Long> filterOrderUser(int orderType, List<Long> userIds) {
        List<Long> orderUserIds = getHasPermissionUserIds(orderType, 0L);
        if (CollectionUtils.isEmpty(orderUserIds)) {
            log.warn("没人有这个工单的权限 type {}", orderType);
            return Lists.newArrayList();
        }
        HashSet<Long> orderUserSet = Sets.newHashSet(orderUserIds);
        userIds = userIds.stream().filter(orderUserSet::contains).collect(Collectors.toList());
        return userIds;
    }

    protected long getLastOperatorId(WorkOrderBase base, boolean inGroup) {

        log.info("get last caseId={}", base.getCaseId());

        int orderType = base.getOrderType();
        Long last = workOrderDao.getLastOperatorIdByCaseIdAndType(
                base.getCaseId(),
                Lists.newArrayList(orderType));

        if (last == null || last <= 0) {
            log.info("caseId={} no OperatorId", base.getCaseId());
            return 0;
        }

        StaffStatus staffStatus = staffStatusService.getStaffStatus(last, orderType);
        if (staffStatus == null
                || staffStatus.getStaffStatus() != StaffStatusEnum.online.getType()) {
            log.info("caseId={} no online", base.getCaseId());
            return 0;
        }

        // 分组分配需要校验组别
        if (base.getAssignGroupId() > 0) {
            AssignGroupDO group = assignGroupService.getById(base.getAssignGroupId());
            boolean valid = vonPermissionService.validPermissionByUserId(last, group.getPermission());
            if (!valid) {
                log.info("{} 一人跟进 组别不一致过滤 lastOperator {}", TAG, last);
                return 0;
            }
        }
        VonAssignConfig config = vonConfigFacade.getAssignConfig(orderType);
        int count = workOrderDao.getCountByOperatorIdAndHandleResult(last, orderType, HandleResultEnum.doing.getType());
        if (count >= config.getAssignLimitCount()) {
            log.info("caseId={} last operator assign limit ", base.getCaseId());
            return 0;
        }
        if (inGroup) {
            //查询在线用户处理中工单数量 若为组分配则查整组类型处理中工单
            List<WorkOrderDoingCount> list = workOrderDao.getDoingCountByOrderTypes(
                    Lists.newArrayList(last),
                    orderGroupService.getGroupList(),
                    HandleResultEnum.doing.getType()
            );
            if (CollectionUtils.isNotEmpty(list)) {
                log.info("caseId={} 一人跟进 组分配不能有处理中 operator assign ", base.getCaseId());
                return 0;
            }
        }

        log.info("get last operatorId operatorId={} orderId={}", last, base.getId());
        return last;
    }

    @Nullable
    private List<Long> getHasPermissionUserIds(int orderType, long assignGroupId) {
        return vonPermissionService.getUserListByOrderTypeWithCache(orderType, assignGroupId);
    }

    public String getLockKey(int orderType) {
        return "Assignate_Work_Order_" + orderType;
    }


    public interface AssignerProcessor {

        VonAssignContext process(VonAssignContext context);

    }
}
