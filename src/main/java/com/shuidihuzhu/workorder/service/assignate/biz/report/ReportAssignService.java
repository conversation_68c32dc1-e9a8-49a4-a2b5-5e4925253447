package com.shuidihuzhu.workorder.service.assignate.biz.report;

import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.assign.VonAssignContext;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: wangpeng
 * @Date: 2022/7/29 14:55
 * @Description:
 */
@Slf4j
@Service
public class ReportAssignService {

    @Resource
    private BaseWorkOrderService baseWorkOrderService;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private PermissionFeignClient permissionFeignClient;

    @Resource
    private VonPermissionService vonPermissionService;

    public VonAssignContext assignProcessorLastReportWork(VonAssignContext context) {
        WorkOrderBase workOrder = context.getWorkOrder();
        int caseId = workOrder.getCaseId();
        WorkOrderVO lastByCaseIdAndOrderTypes = baseWorkOrderService.getLastByCaseIdAndOrderTypes(caseId, WorkOrderType.REPORT_TYPES);
        if (Objects.isNull(lastByCaseIdAndOrderTypes)) {
            return context;
        }
        long operatorId = lastByCaseIdAndOrderTypes.getOperatorId();
        if (operatorId <= 0) {
            return context;
        }
        int orderTypeCode = context.getOrderTypeCode();
        StaffStatus staffStatus = staffStatusService.getStaffStatus(operatorId, orderTypeCode);
        if (Objects.nonNull(staffStatus) && staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
            context.setAssignerUser(operatorId);
            return context;
        }
        return context;
    }

    public VonAssignContext assignProcessorLastBytWorkType(VonAssignContext context) {
        WorkOrderBase workOrder = context.getWorkOrder();
        int caseId = workOrder.getCaseId();
        List<WorkOrderBase> workOrderBases = baseWorkOrderService.listByCaseId(caseId, context.getOrderTypeCode());
        List<WorkOrderBase> collect = workOrderBases.stream()
                .filter(f -> f.getId() != workOrder.getId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return context;
        }
        WorkOrderBase workOrderBase = collect.get(collect.size() - 1);
        long operatorId = workOrderBase.getOperatorId();
        if (operatorId <= 0) {
            return context;
        }
        StaffStatus staffStatus = staffStatusService.getStaffStatus(operatorId, context.getOrderTypeCode());
        if (Objects.nonNull(staffStatus) && staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
            context.setAssignerUser(operatorId);
            return context;
        }
        return context;
    }

    public VonAssignContext assignProcessorByGroup(VonAssignContext context) {
        WorkOrderVO lastByCaseIdAndOrderTypes = baseWorkOrderService.getLastByCaseIdAndOrderTypes(context.getWorkOrder().getCaseId(), WorkOrderType.REPORT_TYPES);
        if (Objects.isNull(lastByCaseIdAndOrderTypes)) {
            return context;
        }
        long operatorId = lastByCaseIdAndOrderTypes.getOperatorId();
        if (operatorId <= 0) {
            return context;
        }
        boolean a = vonPermissionService.validPermissionByUserId(operatorId, "von-assign-group:report-risk-handle");
        Response<List<AuthUserDto>> usersByPermission = permissionFeignClient.getUsersByPermission(a ? "von-assign-group:report-risk-handle" : "von-assign-group:report-shijiazhuang");
        List<Long> userIdList = Optional.ofNullable(usersByPermission)
                .map(Response::getData)
                .orElse(new ArrayList<>())
                .stream()
                .map(AuthUserDto::getUserId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return context;
        }
        List<StaffStatus> staffOnlineToday = staffStatusService.getStaffOnlineToday(userIdList, context.getOrderTypeCode());
        List<Long> users = staffOnlineToday.stream()
                .map(StaffStatus::getUserId)
                .collect(Collectors.toList());
        context.setUsers(users);
        return context;
    }

}
