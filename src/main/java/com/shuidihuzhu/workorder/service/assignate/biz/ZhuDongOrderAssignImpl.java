package com.shuidihuzhu.workorder.service.assignate.biz;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.assign.VonAssignContext;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.OrganizationService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@Service
public class ZhuDongOrderAssignImpl extends BaseAssignService {

    @Autowired
    private WorkOrderExtService orderExtService;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Resource
    private WorkOrderDao workOrderDao;

    private static final String TAG = " ZhuDongOrderAssignImpl ";

    @Override
    public List<Integer> getOrderTypeCodeList() {
        return Lists.newArrayList(WorkOrderType.cailiao_fuwu.getType());
    }

    @Override
    protected List<AssignerProcessor> getProcessor() {
        return Lists.newArrayList(
                priorAssignLastAssignProcessor,
                priorAssignAssignProcessor,
                userQueryProcessor,
                userDoingFilterProcessor
        );
    }

    protected AssignerProcessor priorAssignAssignProcessor = new AssignerProcessor(){
        @Override
        public VonAssignContext process(VonAssignContext context) {
            VonAssignConfig config = context.getConfig();
            int orderTypeCode = context.getOrderTypeCode();
            // 检查是否是组分配
            Map<String, String> extMap = promoteExtMap(context);

            int vonPriorAssignOperatorId = Integer.parseInt(extMap.getOrDefault("vonPriorAssignOperatorId", "0"));

            if (!config.getPriorAssignerEnable()) {
                return context;
            }
            // 若指定优先分配人 则查看状态， 在线直接分配
            if (vonPriorAssignOperatorId > 0) {
                StaffStatus staffStatus = staffStatusService.getStaffStatus(vonPriorAssignOperatorId, orderTypeCode);
                if (staffStatus != null) {
                    log.info("优先人 存在 直接分配");
                    context.setAssignerUser((long) vonPriorAssignOperatorId);
                    return context;
                }
            }
            return context;
        }
    };

}
