package com.shuidihuzhu.workorder.service.assignate.biz;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class QcHighRiskOrderAssignImpl extends BaseAssignService {

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    @Resource
    protected AssignWorkOrderService assignWorkOrderService;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private WorkOrderDaoExt workOrderDaoExt;

    private static final String TAG = " QcHighRiskOrderAssignImpl ";

    @Override
    public List<Integer> getOrderTypeCodeList() {
        return Lists.newArrayList(WorkOrderType.qc_high_risk_quality_inspection.getType());
    }

    @Override
    public List<WorkOrderBase> filterWorkOrder(List<WorkOrderBase> workOrderBases, int orderTypeCode) {
        if (WorkOrderType.QC_WORK_ORDER_LIST.contains(orderTypeCode)) {
            log.info("{} filter work order .type={}", TAG, orderTypeCode);
            workOrderBases = qcWorkOrderJudge(workOrderBases);
        }
        return workOrderBases;
    }

    private List<WorkOrderBase> qcWorkOrderJudge(List<WorkOrderBase> workOrders) {
        List<Long> workOrderIds = workOrders.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        List<WorkOrderExt> workOrderExts = workOrderDaoExt.getWorkOrderExts(workOrderIds, OrderExtName.qcAssignType.getName());
        List<Long> mustAssignWorkOrderIds = workOrderExts.stream()
                .filter(workOrderExt -> Integer.parseInt(workOrderExt.getExtValue()) == AssignTypeEnum.MUST_ASSIGN.getCode())
                .map(WorkOrderExt::getWorkOrderId)
                .collect(Collectors.toList());

        return workOrders.stream()
                .filter(v -> mustAssignWorkOrderIds.contains(v.getId()))
                .collect(Collectors.toList());
    }

}
