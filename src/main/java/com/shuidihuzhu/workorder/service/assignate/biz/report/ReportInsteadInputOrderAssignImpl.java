package com.shuidihuzhu.workorder.service.assignate.biz.report;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/7/29 14:23
 * @Description:
 */
@Slf4j
@Service
public class ReportInsteadInputOrderAssignImpl extends BaseAssignService {

    @Resource
    private ReportAssignService reportAssignService;

    @Override
    public List<Integer> getOrderTypeCodeList() {
        return Lists.newArrayList(WorkOrderType.report_instead_input.getType(), WorkOrderType.report_split_draw.getType());
    }

    @Override
    protected List<AssignerProcessor> getProcessor() {
        return Lists.newArrayList(
                priorAssignLastAssignProcessor,
                priorAssignAssignProcessor,
                priorAssignAssignWorkTypeProcessor,
                priorAssignAssignByGroupProcessor,
                userDoingFilterProcessor
        );
    }

    protected AssignerProcessor priorAssignAssignProcessor = context -> reportAssignService.assignProcessorLastReportWork(context);

    protected AssignerProcessor priorAssignAssignWorkTypeProcessor = context -> reportAssignService.assignProcessorLastBytWorkType(context);

    protected AssignerProcessor priorAssignAssignByGroupProcessor = context -> reportAssignService.assignProcessorByGroup(context);
}
