package com.shuidihuzhu.workorder.service.assignate.biz;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.assign.VonAssignContext;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.OrganizationService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 */
@Slf4j
@Service
public class XiaFaOrderAssignImpl extends BaseAssignService {

    @Autowired
    private WorkOrderExtService orderExtService;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Resource
    private WorkOrderDao workOrderDao;

    private static final String TAG = " XiaFaOrderAssignImpl ";

    @Override
    public List<Integer> getOrderTypeCodeList() {
        return Lists.newArrayList(WorkOrderType.xiafaprogress.getType());
    }

    @Override
    protected List<AssignerProcessor> getProcessor() {
        return Lists.newArrayList(
                priorAssignLastAssignProcessor,
                priorAssignAssignProcessor,
                userQueryProcessor,
                filterNeiShenUsers,
                userDoingFilterProcessor
        );
    }

    protected AssignerProcessor priorAssignAssignProcessor = new AssignerProcessor(){
        @Override
        public VonAssignContext process(VonAssignContext context) {
            VonAssignConfig config = context.getConfig();
            int orderTypeCode = context.getOrderTypeCode();
            // 检查是否是组分配
            Map<String, String> extMap = promoteExtMap(context);

            int vonPriorAssignOperatorId = Integer.parseInt(extMap.getOrDefault("vonPriorAssignOperatorId", "0"));

            if (!config.getPriorAssignerEnable()) {
                return context;
            }
            // 若指定优先分配人 则查看状态， 在线直接分配
            if (vonPriorAssignOperatorId > 0) {
                StaffStatus staffStatus = staffStatusService.getStaffStatus(vonPriorAssignOperatorId, orderTypeCode);
                if (staffStatus != null
                        && staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
                    log.info("优先人 在线 直接分配");
                    context.setAssignerUser((long) vonPriorAssignOperatorId);
                    return context;
                }
            }
            return context;
        }
    };

    protected AssignerProcessor filterNeiShenUsers = context -> {
        List<Long> users = context.getUsers();
        if (CollectionUtils.isEmpty(users)) {
            context.setAssignerUser(0L);
            return context;
        }
        Map<String, String> extMap = promoteExtMap(context);
        String isNeiShenSupplyStr = extMap.get("isNeiShenSupply");
        Boolean isNeiShenSupply = StringUtils.isBlank(isNeiShenSupplyStr) ? null : Boolean.parseBoolean(isNeiShenSupplyStr);
        // 过滤内审人员 或非内审
        users = users.stream().filter(v -> filterNeiShen(v, isNeiShenSupply)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(users)) {
            context.setAssignerUser(0L);
            return context;
        }
        context.setUsers(users);
        return context;
    };

    private boolean filterNeiShen(Long userId, Boolean isNeiShenSupply) {
        // 下发未记录到组织 则不过滤
        if (isNeiShenSupply == null) {
            return true;
        }
        Long orgId = organizationService.getOrgIdByUserId(userId);
        boolean userIsNeiShen = getNerShenGroupIds().contains(orgId);
        return userIsNeiShen == isNeiShenSupply;
    }

    public Collection<Long> getNerShenGroupIds() {
        Config config = ConfigService.getConfig("sdc.inner");
        String value = config.getProperty("apollo.sea.group.nei-shen.id-arr", "876,877,878,879,880");
        String[] split = StringUtils.split(value, ",");
        return Arrays.stream(split)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

}
