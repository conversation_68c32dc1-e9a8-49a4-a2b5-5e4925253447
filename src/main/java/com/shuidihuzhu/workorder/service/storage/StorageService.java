package com.shuidihuzhu.workorder.service.storage;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.dao.StorageDAO;
import com.shuidihuzhu.workorder.core.model.domain.StorageDO;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class StorageService implements WorkOrderStorageFeignClient {

    @Resource
    private StorageDAO storageDAO;

    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Override
    public Response<VonStorageVO> getLastByType(long workOrderId, int key) {
        StorageDO d = storageDAO.getLastByType(workOrderId, key);
        return NewResponseUtil.makeSuccess(convert(d));
    }

    @Override
    public Response<List<VonStorageVO>> getListByType(long workOrderId, int key) {
        List<StorageDO> list = storageDAO.getListByType(workOrderId, key);
        return NewResponseUtil.makeSuccess(convertList(list));
    }

    @Override
    public Response<VonStorageVO> addByTypeValue(long workOrderId, int key, String value) {
        StorageDO d = new StorageDO();
        d.setWorkOrderId(workOrderId);
        d.setStorageType(key);
        d.setStorageValue(value);
        int res = storageDAO.add(d);
        if (res <= 0 ) {
            return NewResponseUtil.makeFail("插入失败");
        }
        return NewResponseUtil.makeSuccess(convert(d));
    }

    @Override
    public Response<Void> addByList(List<VonStorageVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return NewResponseUtil.makeSuccess(null);
        }
        storageDAO.addBatch(list);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> updateByTypeValue(long workOrderId, int key, String value) {
        storageDAO.updateByTypeValue(workOrderId, key, value);
        return NewResponseUtil.makeSuccess(null);
    }

    private List<VonStorageVO> convertList(List<StorageDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(this::convert).collect(Collectors.toList());
    }

    private VonStorageVO convert(StorageDO d) {
        if (d == null) {
            return null;
        }
        VonStorageVO v = new VonStorageVO();
        v.setId(d.getId());
        v.setWorkOrderId(d.getWorkOrderId());
        v.setType(d.getStorageType());
        v.setValue(d.getStorageValue());
        return v;
    }

    private StorageDO convertDO(VonStorageVO d) {
        if (d == null) {
            return null;
        }
        StorageDO v = new StorageDO();
        v.setId(d.getId());
        v.setWorkOrderId(d.getWorkOrderId());
        v.setStorageType(d.getType());
        v.setStorageValue(d.getValue());
        return v;
    }

    public List<BasicWorkOrder> fillStorages(List<BasicWorkOrder> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<Long> ids = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

        // 查询ext
        List<WorkOrderExt> allExtList = workOrderExtService.getWorkOrderExt(ids);
        Map<Long, List<WorkOrderExt>> extMap = Maps.newHashMap();
        for (WorkOrderExt ext : allExtList) {
            List<WorkOrderExt> e1 = extMap.computeIfAbsent(ext.getWorkOrderId(), k -> Lists.newArrayList());
            e1.add(ext);
        }

        // 查询storage
        List<StorageDO> allStorageList = storageDAO.getAllByOrderIds(ids);
        Map<Long, List<VonStorageVO>> map = Maps.newHashMap();
        for (StorageDO s : allStorageList) {
            List<VonStorageVO> storageList = map.computeIfAbsent(s.getWorkOrderId(), k -> Lists.newArrayList());
            storageList.add(convert(s));
        }

        // 写入数据
        list.forEach(v -> {
            List<VonStorageVO> storageList = map.getOrDefault(v.getId(), Lists.newArrayList());
            v.setStorageList(storageList);
            List<WorkOrderExt> extList = extMap.getOrDefault(v.getId(), Lists.newArrayList());
            v.setBizExtList(extList);
        });
        return list;
    }

    public BasicWorkOrder fillStorage(BasicWorkOrder o) {
        if (o == null) {
            return null;
        }
        return fillStorages(Lists.newArrayList(o)).get(0);
    }
}
