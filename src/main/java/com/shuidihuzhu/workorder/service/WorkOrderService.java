package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.workorder.model.OpResult;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
public interface WorkOrderService<T extends WorkOrderBase,R extends HandleOrderParam,S extends WorkOrderListParam> {


    OpResult vlidate(T wordOrder);

    /**
     * 创建工单
     * @param wordOrder
     * @return
     */
    OpResult<Long> create(T wordOrder);


    /**
     * 处理工单
     * @return
     */
     OpResult handle(R param);


    /**
     * 工单列表页
     * @return
     */
    OpResult getOrderList(S param);

}
