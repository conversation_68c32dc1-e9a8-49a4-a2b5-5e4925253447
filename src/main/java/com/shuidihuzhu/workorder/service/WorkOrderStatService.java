package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.workorder.model.OpResult;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/3/29
 */
public interface WorkOrderStatService<T extends WorkOrderStat> {

    /**
     * 查询统计列表
     * @param one     一级工单 必选
     * @param two     二级工单 可以选   默认 -1  未选
     * @param userId  人员  默认 -1   未选
     * @return
     */
    OpResult<List<T>> getWorkOrderStatList(int one, String two, long userId);



    default List<T> getOnlyOneLevel(int oneLevel){
        return Lists.newArrayList();
    }


    default List<T> getTypeStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList){
        return Lists.newArrayList();
    }


    default List<T> getUserStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList){
        return Lists.newArrayList();
    }

}
