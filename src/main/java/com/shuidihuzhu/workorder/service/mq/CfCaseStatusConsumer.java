package com.shuidihuzhu.workorder.service.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBaseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfStatusChangeRecord;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.service.impl.ShouciWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/25
 */
@Service
@RocketMQListener(id = "workorder-change",
        group = "cf-workorder-group",
        tags = MQTagCons.CF_STATUS_CHANGE_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCaseStatusConsumer implements MessageListener<CfStatusChangeRecord>{


    @Autowired
    private WorkOrderDao workOrderDao;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfStatusChangeRecord> mqMessage) {

        log.info("CfCaseStatusConsumer mqMessage={}",mqMessage);

        if (mqMessage == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfStatusChangeRecord record = mqMessage.getPayload();

        if (record == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int status = record.getStatus();
        if (status == CfBaseStatus.cf_end.getCode()) {
            int caseId = Long.valueOf(record.getCaseId()).intValue();

            List<Integer> types = Lists.newArrayList(
                    WorkOrderType.yilei.getType(),
                    WorkOrderType.erlei.getType(),
                    WorkOrderType.sanlei.getType(),
                    WorkOrderType.shenhe.getType(),
                    WorkOrderType.dianhuashenhe.getType(),
                    WorkOrderType.yiliaoshenhe.getType(),
                    WorkOrderType.highriskshenhe.getType(),
                    WorkOrderType.huifang.getType(),
                    WorkOrderType.bohui.getType());

            workOrderDao.closeWorkOrderByCaseId(caseId,types, HandleResultEnum.exception_done.getType());
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
