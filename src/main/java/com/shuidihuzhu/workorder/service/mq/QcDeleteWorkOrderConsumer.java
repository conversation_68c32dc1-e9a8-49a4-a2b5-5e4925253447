package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Auther: subing
 * @Date: 2020/9/17
 */

@RocketMQListener(id = CfClientMQTagCons.CF_CLUE_FUWU_CLOSE_MSG,
        tags = CfClientMQTagCons.CF_CLUE_FUWU_CLOSE_MSG,
        group = "qc_delete_work_order_group",
        topic = MQTag.CF)
@Service
@Slf4j
public class QcDeleteWorkOrderConsumer implements MessageListener<Long> {
    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> mqMessage) {
        log.info("QcDeleteWorkOrderConsumer mqMessage={}",mqMessage);
        Long taskId = mqMessage.getPayload();
        if (taskId != null && taskId > 0) {
            List<WorkOrderBase> workOrderBases =
                    baseWorkOrderService.listByCaseId(Math.toIntExact(taskId), WorkOrderType.qc_wx_1v1.getType());
            if (CollectionUtils.isEmpty(workOrderBases)){
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            WorkOrderBase workOrderBase = Optional.ofNullable(workOrderBases.get(0)).orElseGet(WorkOrderBaseVo::new);
            baseWorkOrderService.deleteWorkOrderByIdAndOrderType(workOrderBase.getId());
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
