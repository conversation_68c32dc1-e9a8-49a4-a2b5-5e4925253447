package com.shuidihuzhu.workorder.service.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTag.QC_WORK_ORDER_ASSIGN_EVENT_V2,
        tags = MQTag.QC_WORK_ORDER_ASSIGN_EVENT_V2,
        topic = MQTag.CF)
@Slf4j
public class QcWorkOrderAssignNewConsumer implements MessageListener<Integer> {
    @Resource
    private OrderAssignFacade orderAssignFacade;
    @Resource(name = "longTimeTaskThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {
        int workOrderType = mqMessage.getPayload();
        log.info("QcWorkOrderAssignConsumer workOrderType:{}", JSON.toJSONString(workOrderType));
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(workOrderType)) {
            log.info("Ignore order type:{} on async assign", workOrderType);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Response<Void> resp = orderAssignFacade.doAssignate(workOrderType);
        log.info("QcWorkOrderAssignConsumer workOrderType:{} opResult:{}", workOrderType, JSON.toJSONString(resp));

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
