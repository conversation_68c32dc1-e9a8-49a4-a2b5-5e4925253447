package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.service.impl.ShouciWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 根据规则，动态计算并更新未处理首次沟通工单的类型；
 * 规则: https://wiki.shuiditech.com/pages/viewpage.action?pageId=164987368
 * 捐款事件订阅
 * <AUTHOR>
 * @DATE 2019/4/19
 */
@Service
@RocketMQListener(id = "work_order_pay",
        group = "work_order_pay_group",
        tags = "CF_PAY_ONLY_SUCCESS_MSG",
        topic = MQTopicCons.CF)
@Slf4j
public class ShouciOrderTypeConsumer implements MessageListener<CrowdfundingOrder> {

    @Resource(name = "shouciWorkOrderService")
    private ShouciWorkOrderServiceImpl shouciWorkOrderService;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {

        CrowdfundingOrder successOrder = mqMessage.getPayload();
        if (successOrder != null){
            shouciWorkOrderService.changeShouciType(successOrder.getCrowdfundingId());
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
