package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.service.core.notice.AssignGroupNoticeService;
import com.shuidihuzhu.workorder.core.service.core.notice.AssignNoticeService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.impl.CailiaoWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 服务工单关闭   信息传递工单处理完成
 * <AUTHOR>
 * @DATE 2020/11/11
 */
@Service
@Slf4j
@RocketMQListener(id = CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC,
        group = "cf-work-"+CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC+"-group",
        tags = CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC,
        topic = MQTopicCons.CF)
public class WorkOrderAssignConsumer implements MessageListener<WorkOrderVO> {

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private CailiaoWorkOrderServiceImpl cailiaoWorkOrderService;

    @Autowired
    private AssignGroupNoticeService assignGroupNoticeService;

    @Autowired
    private AssignNoticeService assignNoticeService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WorkOrderVO> mqMessage) {

        WorkOrderVO vo = mqMessage.getPayload();
        log.info("WorkOrderAssignConsumer vo={}",vo);

        assignGroupNoticeService.onOrderAssign(vo);
        assignNoticeService.onOrderAssign(vo);

        noticeCaiLiao(vo);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void noticeCaiLiao(WorkOrderVO vo) {
        if (vo.getOrderType() != WorkOrderType.cailiao_fuwu.getType()){
            return ;
        }
        WorkOrderExt ext = orderExtService.getByOrderId(vo.getWorkOrderId(), OrderExtName.flowOrderId);
        if (ext == null){
            return ;
        }
        cailiaoWorkOrderService.noticeFlowOrder(Long.parseLong(ext.getExtValue()),vo.getOperatorId(),0);
    }
}
