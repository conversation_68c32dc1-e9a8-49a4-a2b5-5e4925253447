package com.shuidihuzhu.workorder.service.mq.producer;

import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.vo.GenjinModel;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2019/8/14
 *
 * 发送延迟电话审核工单创建消息
 */
@Component
@Slf4j
public class DianhuaCreateMq {


    @Autowired(required = false)
    private Producer producer;

    private final static long delay_2 = 2L;

    private final static long delay_72 = 72L;

    private final static long delay_120 = 120L;

    private final static long delay_168 = 168L;

    /**
     * 驳回发送的mq
     * @param genjinModel
     */
    public void sendGenjinMqForReject(GenjinModel genjinModel){

        sendDelay(genjinModel, delay_72);
        sendDelay(genjinModel, delay_120);
        sendDelay(genjinModel, delay_168);

    }

    /**
     * 下发发送的mq
     * @param genjinModel
     */
    public void sendGenjinMq(GenjinModel genjinModel){

        sendDelay(genjinModel, delay_2);
        sendDelay(genjinModel, delay_72);
        sendDelay(genjinModel, delay_120);
        sendDelay(genjinModel, delay_168);

    }

    private void sendDelay(GenjinModel genjinModel, long delayDays) {

        int caseId = genjinModel.getCaseId();

        String operation = genjinModel.getOperation();

        Message message = Message.ofDelay(
                MQTag.CF,
                MQTag.genjin_order_create_mq,
                delayDays+operation+caseId,
                genjinModel,
                delayDays,
                TimeUnit.HOURS);

        MessageResult result = producer.send(message);
        log.info("sendGenjinMq message={},result={}", message, result);
    }

}
