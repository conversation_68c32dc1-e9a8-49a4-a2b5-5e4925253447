package com.shuidihuzhu.workorder.service.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @DATE 2019/8/2
 */
@Service
@RocketMQListener(id = "report-deal",
        group = "report-deal-group",
        tags = WorkOrderMQ.report_close_order_create_new_order_mq,
        topic = MQTopicCons.CF)
@Slf4j
public class ReportDealResultConsumer implements MessageListener<ReportWorkOrder> {

    @Resource(name = "reportWorkOrderServiceImpl")
    private WorkOrderFacade workOrderFacade;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserOperationRecordDao recordDao;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<ReportWorkOrder> mqMessage) {

        log.info("ReportDealResultConsumer mqMessage={}", mqMessage);

        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        ReportWorkOrder reportWorkOrder = mqMessage.getPayload();
        this.orderOperate(reportWorkOrder);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void orderOperate(ReportWorkOrder reportWorkOrder) {
        WorkOrderRecord workOrderRecord = new WorkOrderRecord();
        workOrderRecord.setWorkOrderType(reportWorkOrder.getOrderType());
        workOrderRecord.setOperatorId(0);
        workOrderRecord.setComment("升级二线");
        workOrderRecord.setOperateMode(OperateMode.handle.getType());
        workOrderRecord.setOperateDesc("处理工单");
        workOrderRecord.setWorkOrderId(reportWorkOrder.getId());

        int res = workOrderDao.closeOrderById(Lists.newArrayList(reportWorkOrder.getId()), reportWorkOrder.getHandleResult());
        if (res > 0) {
            recordDao.saveRecordList(Lists.newArrayList(workOrderRecord));
            //创建二线工单
            ReportWorkOrder workOrder = new ReportWorkOrder();
            workOrder.setCaseId(reportWorkOrder.getCaseId());
            workOrder.setReportId(reportWorkOrder.getReportId());
            workOrder.setOrderType(WorkOrderType.up_grade_second.getType());
            workOrder.setHandleResult(HandleResultEnum.undoing.getType());
            workOrder.setOperatorId(0L);
            workOrder.setDealOperatorId(0L);
            workOrder.setComment("生成二线工单");
            workOrderFacade.doCreate(workOrder);
        }
    }
}
