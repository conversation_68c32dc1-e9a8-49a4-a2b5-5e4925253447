package com.shuidihuzhu.workorder.service.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoDealResult;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderMQ;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/8/2
 */
@Service
@RocketMQListener(id = "cailiao-deal",
        group = "cailiao-deal-group",
        tags = WorkOrderMQ.cailiao_deal_result_msg,
        topic = MQTopicCons.CF)
@Slf4j
public class CailiaoDealResultConsumer implements MessageListener<CailiaoDealResult> {


    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserOperationRecordDao recordDao;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CailiaoDealResult> mqMessage) {


        log.info("CailiaoDealResultConsumer mqMessage={}",mqMessage);

        if (mqMessage == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CailiaoDealResult record = mqMessage.getPayload();

        if (record == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = record.getCaseId();
        int deal = record.getDealResult();
        // @see CfDealWithStatusEnum
        //对于未分配的工单，延后操作 关闭 0次 1~3次 3+次工单；延后电话联系和不再处理 关闭所有材料工单
        if (deal == 2 || deal == 3 || deal == 1){
            List<Integer> types = Lists.newArrayList();
            if (deal == 2 || deal == 3) {
                types = Lists.newArrayList(
                        WorkOrderType.cailiao_0.getType(),
                        WorkOrderType.cailiao_1.getType(),
                        WorkOrderType.cailiao_3.getType(),
                        WorkOrderType.yanhou.getType(),
                        WorkOrderType.cailiao_4.getType(),
                        WorkOrderType.cailiao_5.getType(),
                        WorkOrderType.cailiao_fuwu.getType()
                );

            } else {
                types = Lists.newArrayList(
                        WorkOrderType.cailiao_0.getType(),
                        WorkOrderType.cailiao_1.getType(),
                        WorkOrderType.cailiao_3.getType());
            }

            closeZhuDong(caseId, deal, Lists.newArrayList(HandleResultEnum.undoing.getType()), types);

        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void closeZhuDong(int caseId, int deal, List<Integer> integers, List<Integer> integers2) {
        List<Integer> unDoResult = integers;

        List<WorkOrderBase> unDuList = workOrderDao.getOrderByCaseId(caseId, integers2, unDoResult);

        closeOrder(deal, unDuList);
    }

    private void closeOrder(int deal, List<WorkOrderBase> list) {
        if (CollectionUtils.isEmpty(list)){
            return;
        }

        List<WorkOrderRecord> records = Lists.newArrayList();
        List<Long> ids = Lists.newArrayList();
        list.stream().forEach(r->{

            ids.add(r.getId());

            WorkOrderRecord workOrderRecord = new WorkOrderRecord();
            workOrderRecord.setWorkOrderType(r.getOrderType());
            workOrderRecord.setOperatorId(0);
            workOrderRecord.setComment("系统自动结束");
            workOrderRecord.setOperateMode(OperateMode.handle.getType());
            workOrderRecord.setOperateDesc(deal == 2?"该案例不再处理":"延迟电话联系");
            workOrderRecord.setWorkOrderId(r.getId());
            records.add(workOrderRecord);

        });
        workOrderDao.closeOrderById(ids, HandleResultEnum.exception_done.getType());
        recordDao.saveRecordList(records);
        return;
    }
}
