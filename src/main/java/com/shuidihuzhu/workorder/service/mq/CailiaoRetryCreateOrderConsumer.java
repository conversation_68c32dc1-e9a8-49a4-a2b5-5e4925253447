package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderMQ;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @DATE 2019/8/2
 */

@Service
@RocketMQListener(id = "cf-cailiao-retry",
        group = "cf-cailiao-retry-group",
        tags = WorkOrderMQ.auto_cailiao_create_mq,
        topic = MQTopicCons.CF)
@Slf4j
public class CailiaoRetryCreateOrderConsumer implements MessageListener<CailiaoWorkOrder> {

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Resource(name = "cailiaoWorkOrderService")
    private WorkOrderFacade workOrderFacade;

    @Resource
    private MeterRegistry meterRegistry;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CailiaoWorkOrder> mqMessage) {
        log.info("CailiaoRetryCreateOrderConsumer mqMessage={}",mqMessage);

        CailiaoWorkOrder cailiaoWorkOrder = mqMessage.getPayload();

        if (cailiaoWorkOrder == null){
            log.error("CailiaoRetryCreateOrderConsumer cailiaoWorkOrder=null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String time = LocalDateTime.now().plusMinutes(-1).toString();

        //查询一分钟内是否有创建工单
        WorkOrderVO workOrderVO = baseWorkOrderService.getOrderByTypeAndTime(cailiaoWorkOrder.getCaseId(), cailiaoWorkOrder.getOrderType(),time);

        if (workOrderVO == null){

            cailiaoWorkOrder.setOrderType(cailiaoWorkOrder.getOrderType());
            cailiaoWorkOrder.setOperComment("系统自动补单");

            workOrderFacade.doCreate(cailiaoWorkOrder);

            meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,OperationStat.OPERATION,OperationStat.retry_create,OperationStat.ORDERTYPE,cailiaoWorkOrder.getOrderType()+"").increment();
        }


        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
