package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.custom.recruit.PrCommonAssigner;
import com.shuidihuzhu.workorder.service.custom.recruit.WorkOrderStaffConfService;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/01 下午3:00
 */
@Slf4j
@Service("prSecondLineAuditWorkOrderAssignImpl")
public class PrSecondLineAuditWorkOrderAssignImpl extends PrCommonAssigner {

    @Resource
    private WorkOrderStaffConfService workOrderStaffConfService;
    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;
    @Resource
    private GeneralWorkOrderAssignateImpl generalWorkOrderAssignate;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        return generalWorkOrderAssignate.getAssignateWorkOrder(orderType, resultEnum);
    }

    @Override
    protected PrResponsibleVo selectResponsible(int orderType, WorkOrderBase workOrderBase) {
        return workOrderStaffConfService.assignForSecondLine(workOrderBase.getId(), workOrderBase.getCaseId());
    }

}
