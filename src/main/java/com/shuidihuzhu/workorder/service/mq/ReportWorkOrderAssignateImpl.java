package com.shuidihuzhu.workorder.service.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.order.WorkOrderAssignedCount;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/12/29 下午7:39
 * @desc
 */
@Slf4j
@Service("reportWorkOrderAssignateImpl")
public class ReportWorkOrderAssignateImpl extends AssignateWorkOrderFacade {
    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if(Objects.isNull(workOrderTypeService.getFromOrderTypeCode(orderType)) || Objects.isNull(resultEnum)){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),null, null);

        return OpResult.createSucResult(workOrderBases);
    }


    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBase) {

        if (WorkOrderType.casehistoryreport.getType() == orderType || WorkOrderType.up_grade_second.getType() == orderType) {
            ArrayList<Integer> handleResultList = Lists.newArrayList(HandleResultEnum.noneed_deal.getType(),
                    HandleResultEnum.end_deal.getType());
            List<Integer> orderTypeList = Lists.newArrayList();
            if (WorkOrderType.up_grade_second.getType() == orderType) {
                orderTypeList.add(WorkOrderType.up_grade_second.getType());
            } else {
                orderTypeList.addAll(WorkOrderType.REPORT_TYPES);
            }
            //取案例所有处理完的举报工单
            List<WorkOrderBase> workOrderBaseList = workOrderDao.getOrderByCaseId(workOrderBase.getCaseId(), orderTypeList, handleResultList);
            //找到最新的一条
            WorkOrderBase workOrder = workOrderBaseList.stream().max(Comparator.comparing(WorkOrderBase::getCreateTime)).orElse(null);
            if (Objects.nonNull(workOrder)) {
                long operatorId = workOrder.getOperatorId();
                StaffStatus staffStatus = staffStatusService.getStaffStatus(operatorId, orderType);
                if (Objects.nonNull(staffStatus)
                        && staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()) {
                    //案例首次工单需要判断是否接受自动分配
                    if (orderType == WorkOrderType.casehistoryreport.getType() && staffStatus.getAutoAllocation() == 1) {
                        return OpResult.createSucResult(operatorId);
                    } else if (orderType == WorkOrderType.up_grade_second.getType()){
                        return OpResult.createSucResult(operatorId);
                    }
                }
            }
        }

        List<StaffStatus> staffStatuses = queryReportOnlineStaff(orderType);

        Long availableUser = reportAssignateStaff(staffStatuses, orderType);

        return OpResult.createSucResult(availableUser);
    }

    public List<StaffStatus> queryReportOnlineStaff(int orderType) {

        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if(workOrderTypeDO == null){
            log.info("queryReportOnlineStaff workOrderType={} workOrderTypeDO=null",orderType);
            return null;
        }
        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(workOrderTypeDO.getPermission());

        if (Objects.isNull(response) || response.notOk()){
            log.info("queryReportOnlineStaff workOrderTypeDO={} rpcRespons={}", workOrderTypeDO,response);
            return null;
        }

        if (CollectionUtils.isEmpty(response.getData())){
            log.info("queryReportOnlineStaff workOrderTypeDO={} userAccountModelList=null", workOrderTypeDO);
            return null;
        }

        List<Long> userIds = response.getData().stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
        //查询在线用户
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineToday(userIds,orderType);

        //autoAllocation==1表示接受自动分配工单
//        if (workOrderType.getType() != WorkOrderType.up_grade_second.getType()
//                && workOrderType.getType() != WorkOrderType.lost_report.getType()) {
//            staffList = staffList.stream().filter(s -> 1 == s.getAutoAllocation()).collect(Collectors.toList());
//        }

        return staffList;
    }

    private Long reportAssignateStaff(List<StaffStatus> staffList,int orderType){
        if (CollectionUtils.isEmpty(staffList)){
            log.info("reportAssignateStaff orderType={} StaffOnline=null",orderType);
            return null;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());

        //确定在线人员处理中的工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users, orderType, HandleResultEnum.doing.getType());
        Map<Long,WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));
        list = users.stream().map(r->{
            if (Objects.nonNull(doingMap.get(r))){
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r,0);
        }).collect(Collectors.toList());


        // 首次举报工单分配逻辑单独处理
        if (orderType == WorkOrderType.casefirstreport.getType()) {
            Map<Long, StaffStatus> thresholdMap = staffList.stream().collect(Collectors.toMap(StaffStatus::getUserId, Function.identity()));
            return this.firstReportAssign(users, orderType, doingMap, thresholdMap);
        }


        /**
         * 今日已分配工单量最少
         * 今日已分配工单量相同时，分配时间最早的
         */
        List<Long> operatorIds = list.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
        log.info("reportAssignateStaff operatorIds:{}",operatorIds);
        List<WorkOrderAssignedCount> assignedCounts = workOrderDao.queryAssignedCount(operatorIds, orderType, DateFormatUtils.format(new Date(), "yyyy-MM-dd 00:00:00"));
        log.info("reportAssignateStaff assignedCounts:{}",assignedCounts);
        Map<Long,WorkOrderAssignedCount> assignedMap = assignedCounts.stream().collect(Collectors.toMap(WorkOrderAssignedCount::getOperatorId, Function.identity()));
        log.info("reportAssignateStaff assignedMap:{}",assignedMap);

        assignedCounts = operatorIds.stream().map(o -> {
            if(Objects.nonNull(assignedMap.get(o))){
                return assignedMap.get(o);
            }
            return new WorkOrderAssignedCount(o, 0);
        }).collect(Collectors.toList());

        log.info("reportAssignateStaff assignedCounts after:{}",assignedCounts);

        Optional<WorkOrderAssignedCount> minAssignedCount = assignedCounts.stream().sorted(Comparator.comparing(WorkOrderAssignedCount::getNum)).findFirst();
        Map<Integer,List<WorkOrderAssignedCount>> minAssignedCountMap = assignedCounts.stream().filter(r -> r.getNum() <= minAssignedCount.get().getNum()).collect(Collectors.groupingBy(WorkOrderAssignedCount::getNum));
        List<WorkOrderAssignedCount> minAssigneds = minAssignedCountMap.get(minAssignedCount.get().getNum());
        if(1 == minAssigneds.size()){
            return minAssigneds.get(0).getOperatorId();
        }

        List<Long> sameMinAssignedUsers = minAssigneds.stream().map(WorkOrderAssignedCount::getOperatorId).collect(Collectors.toList());
        return staffStatusService.getStaffOrderByAssignTime(sameMinAssignedUsers, orderType);
    }

    private long firstReportAssign(List<Long> users, int orderType,
                                   Map<Long, WorkOrderDoingCount> doingMap, Map<Long, StaffStatus> staffStatusMap) {
        log.info("ReportWorkOrderAssignateImpl.firstReportAssign orderType:{},users:{}", orderType, JSON.toJSONString(users));
        if (CollectionUtils.isEmpty(users)) {
            return 0;
        }
        // 达成一致数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users, orderType, HandleResultEnum.reach_agree.getType());
        Map<Long, WorkOrderDoingCount> reachAgreeMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));

        // 稍后处理数量
        List<WorkOrderDoingCount> laterList = workOrderDao.getDoingCount(users, orderType, HandleResultEnum.later_doing.getType());
        Map<Long, WorkOrderDoingCount> laterDoingMap = laterList.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));

        //找出当前员工处理中工单小于阈值
        var handleCount = this.getHandleCount();
        users = users.stream().filter(userId -> {
            WorkOrderDoingCount orderDoingCount = doingMap.get(userId);
            if (Objects.nonNull(orderDoingCount) && orderDoingCount.getNum() >= handleCount) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        // 总跟进工单量小于阈值
        users = users.stream().filter(userId -> {
            var reachAgreeCount = reachAgreeMap.get(userId) == null ? 0 : reachAgreeMap.get(userId).getNum();
            var laterDoingCount = laterDoingMap.get(userId) == null ? 0 : laterDoingMap.get(userId).getNum();
            var orderDoingCount = doingMap.get(userId) == null ? 0 : doingMap.get(userId).getNum();

            var staffStatus = staffStatusMap.get(userId);
            var receiptThreshold = staffStatus.getReceiptThreshold();
            if (reachAgreeCount + laterDoingCount + orderDoingCount >= receiptThreshold) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        // 找员工当前处理中+达成一致+稍后处理工单量与总跟进工单量差值越大
        Map<Long, Integer> map = Maps.newHashMap();
        for (Long userId : users) {
            var reachAgreeCount = reachAgreeMap.get(userId) == null ? 0 : reachAgreeMap.get(userId).getNum();
            var laterDoingCount = laterDoingMap.get(userId) == null ? 0 : laterDoingMap.get(userId).getNum();
            var orderDoingCount = doingMap.get(userId) == null ? 0 : doingMap.get(userId).getNum();

            var staffStatus = staffStatusMap.get(userId);
            var receiptThreshold = staffStatus.getReceiptThreshold();
            map.put(userId, receiptThreshold - (reachAgreeCount + laterDoingCount + orderDoingCount));
        }
        if (MapUtils.isEmpty(map)) {
            return 0;
        }
        //排序
        var result = map.entrySet()
                .stream()
                .max(Map.Entry.comparingByValue()).get();

        return result.getKey();
    }

    public int getHandleCount() {
        final String key = "REPORT_SET_DOING_COUNT_KEY";
        var count = cf2RedissonHandler.get(key, Integer.class);
        if (null == count) {
            return 1;
        }
        return count;
    }
}
