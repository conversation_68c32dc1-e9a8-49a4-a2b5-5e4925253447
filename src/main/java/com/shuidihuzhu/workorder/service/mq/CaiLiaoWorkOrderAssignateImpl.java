package com.shuidihuzhu.workorder.service.mq;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/11/6
 */
@Service("caiLiaoWorkOrderAssignate")
@RefreshScope
@Slf4j
public class CaiLiaoWorkOrderAssignateImpl extends AssignateWorkOrderFacade {

    @Value("${apollo.work-order.zhudong-fuwu.threshold:2}")
    private int offLineThreshold;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private OrderExtService orderExtService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if(null == workOrderTypeService.getFromOrderTypeCode(orderType) || null == resultEnum){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),null, null);

        return OpResult.createSucResult(workOrderBases);
    }

    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrder) {

        if (workOrder == null){
            return OpResult.createSucResult(0L);
        }

        long userId = getlastOperatorId(workOrder);
        if (userId > 0){
            return OpResult.createSucResult(userId);
        }
        WorkOrderExt ext = orderExtService.getByOrderId(workOrder.getId(), OrderExtName.firstCreate);
        Boolean firstCreate = Optional.ofNullable(ext).map(WorkOrderExt::getExtValue).map(Boolean::valueOf).orElse(false);
        Boolean firstCreateAndNoLabel = false;
        if (firstCreate){
            ext = orderExtService.getByOrderId(workOrder.getId(), OrderExtName.followLabel);
            firstCreateAndNoLabel = Optional.ofNullable(ext).map(WorkOrderExt::getExtValue).orElse("").isEmpty();
        }

        return OpResult.createSucResult(calCanAssignateStaff(orderType, firstCreateAndNoLabel,firstCreate));
    }

    private Long calCanAssignateStaff(int orderType, @Nullable Boolean firstCreateAndNoLabel,Boolean firstCreate){

        List<StaffStatus> staffList = queryOnlineStaff(orderType);

        if (CollectionUtils.isEmpty(staffList)){
            log.info("calCanAssignateStaff orderType={} StaffOnline=null",orderType);
            return 0L;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        //限制数量
        Map<Long,Integer> countMap = staffList.stream().collect(Collectors.toMap(StaffStatus::getUserId,v -> {
            if(firstCreateAndNoLabel){
                return v.getReceiptThreshold();
            }

            // 原操作人离线随机分配每人的阈值
            return offLineThreshold;
        },(o1, o2)->o2));

        //查询在线用户处理中工单数量
        Map<Long,Integer> doingMap = getDoingCount(users,firstCreateAndNoLabel,firstCreate);

        //如果返回为空  直接分配给当前在线的人员
        if (MapUtils.isEmpty(doingMap)){
            log.info("no doing user online user={}",users);
            //查找分配时间早的用户
            Optional<StaffStatus> op = staffList.stream()
                    .filter(r->r.getReceiptThreshold() > 0).sorted(Comparator.comparing(StaffStatus::getAssignTime)).findFirst();
            if (op.isPresent()){
                return op.get().getUserId();
            }
            return 0L;
        }

        //取出最少工单数量
        Optional<Integer> minCount = doingMap.keySet().stream()
                .filter(r->doingMap.get(r) < countMap.get(r))
                .sorted(Comparator.comparing(r->doingMap.get(r)))
                .map(r->doingMap.get(r))
                .findFirst();

        //如果不存在不分配
        if (!minCount.isPresent()){
            return 0l;

        }
        //取出工单最少的人
        List<Long> minUsers = doingMap.keySet().stream()
                .filter(r->doingMap.get(r) < countMap.get(r))
                .filter(r->doingMap.get(r) == minCount.get())
                .collect(Collectors.toList());

        if (minUsers.size()==1){       //如果只有一个直接分配
            return minUsers.get(0);
        }

        //查找分配时间早的用户
        return staffStatusService.getStaffOrderByAssignTime(minUsers,orderType);

    }

    private long getlastOperatorId(WorkOrderBase base){

        log.info("get last caseId={}",base.getCaseId());

        WorkOrderBase last = workOrderDao.getWorkOrderBycaseIdAndTypes(base.getCaseId(),Lists.newArrayList(WorkOrderType.cailiao_fuwu.getType()),Lists.newArrayList(HandleResultEnum.manual_lock.getType(),
        HandleResultEnum.exception_done.getType(),
        HandleResultEnum.handle_manual_lock.getType(),
        HandleResultEnum.done.getType()));

        if (last == null || last.getOperatorId() == 0){
            log.info("caseId={} no OperatorId",base.getCaseId());
            return 0;
        }
        long operatorId = last.getOperatorId();

        List<Long> userId = Lists.newArrayList(operatorId);
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineAndStopToday(userId,base.getOrderType());

        if (CollectionUtils.isEmpty(staffList)){
            log.info("caseId={} no online",base.getCaseId());
            return 0;
        }

        log.info("getlastOperatorId operatorId={} orderId={}",operatorId,base.getId());
        return operatorId;
    }

    private Map<Long,Integer> getDoingCount(List<Long> users,Boolean firstCreateAndNoLabel,Boolean firstCreate){

        Map<Long,Integer> m = Maps.newHashMap();

        //查询在线用户处理中工单
        List<WorkOrderBaseVo> list = workOrderDao.getDoingOrders(users, WorkOrderType.cailiao_fuwu.getType(), HandleResultEnum.doing.getType());
        if (CollectionUtils.isEmpty(list)){
            return m;
        }
        List<Long> ids = list.stream().map(WorkOrderBaseVo::getId).collect(Collectors.toList());
        List<WorkOrderExt> exts = orderExtService.getWorkOrderExts(ids, OrderExtName.firstCreate.getName());

        //获取指定的工单
        Map<Long,String> labelMap = exts.stream().filter(r->firstCreate.equals(Boolean.valueOf(r.getExtValue()))).collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,WorkOrderExt::getExtValue,(o1,o2)->o2));

        if (MapUtils.isEmpty(labelMap)){
            return m;
        }
        //过滤掉 首次有标签的案例
        if (firstCreateAndNoLabel){
            exts = orderExtService.getWorkOrderExts(ids, OrderExtName.followLabel.getName());
            Set<Long> set = exts.stream().map(WorkOrderExt::getWorkOrderId).collect(Collectors.toSet());
            set.stream().forEach(r->{labelMap.remove(r);});
            if (MapUtils.isEmpty(labelMap)){
                return m;
            }
        }

        // 过滤掉多次有标签的案例
        if (!firstCreate){
            exts = orderExtService.getWorkOrderExts(ids, OrderExtName.followLabel.getName());
            Set<Long> set = exts.stream().map(WorkOrderExt::getWorkOrderId).collect(Collectors.toSet());
            set.stream().forEach(r->{labelMap.remove(r);});
            if (MapUtils.isEmpty(labelMap)){
                return m;
            }
        }

        list = list.stream().filter(r->labelMap.containsKey(r.getId())).collect(Collectors.toList());

        Map<Long,Integer> map = list.stream().distinct().collect(Collectors.groupingBy(WorkOrderBaseVo::getOperatorId,Collectors.summingInt(r->1)));

        //把没有的默认封装成0
        users.stream().forEach(r->{
            Integer count = map.get(r);
            m.put(r,Optional.ofNullable(count).orElse(0));
        });

        return m;
    }
}
