package com.shuidihuzhu.workorder.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderReportStatisticsDao;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: lixiaoshuang
 * @create: 2020-09-02 16:33
 **/
@Service
@RocketMQListener(id = MQTag.GET_CALL_TOTAL_DURATION,
        tags = MQTag.GET_CALL_TOTAL_DURATION,
        topic = MQTag.CF)
@Slf4j
public class GetCallTotalDurationConsumer implements MessageListener<Map<String, Object>> {


    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;
    @Autowired
    private WorkOrderReportStatisticsDao workOrderReportStatisticsDao;
    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Map<String, Object>> mqMessage) {
        Map<String, Object> payload = mqMessage.getPayload();
        long workOrderId = Long.parseLong(payload.get("workOrderId").toString());
        String unique = payload.get("unique").toString();
        int statDay = Integer.parseInt(payload.get("statDay").toString());
        int hour = Integer.parseInt(payload.get("hour").toString());
        log.info("GetCallTotalDurationConsumer workOrderId:{}", workOrderId);

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workOrderId);
        if (Objects.isNull(workOrderBase)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Response<List<ClewCallRecordModel>> response =  cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(List.of(unique));
        log.info("cfClewtrackFeignClient response:{}", JSONObject.toJSONString(response));
        if (response.notOk()){
            return ConsumeStatus.RECONSUME_LATER;
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        ClewCallRecordModel clewCallRecordModel = response.getData().get(0);

        int limit = 3;
        if (clewCallRecordModel.getTotalDuration() == 0 && mqMessage.getReconsumeTimes() < limit) {
            return ConsumeStatus.RECONSUME_LATER;
        }
        log.info(" get clewCallRecordModel:{} ", JSON.toJSONString(clewCallRecordModel));
        workOrderReportStatisticsDao.updateCallDuration(statDay, hour, workOrderBase.getOrderType(),
                workOrderBase.getOperatorId(), clewCallRecordModel.getTotalDuration());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
