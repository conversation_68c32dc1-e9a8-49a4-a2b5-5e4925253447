package com.shuidihuzhu.workorder.service.mq.kafka;

import brave.Span;
import brave.Tracer;
import brave.Tracing;
import brave.kafka.clients.KafkaTracing;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.data.consumer.internals.MessageListener;
import com.shuidihuzhu.data.support.listener.Acknowledgment;
import com.shuidihuzhu.workorder.service.WorkOrderDataStatisticsService;
import com.shuidihuzhu.workorder.service.mq.kafka.model.BinLog;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */

@Slf4j
@Component
@Profile("!canary & !canary2")
public class WorkOrderForDataStatisticsConsumer implements MessageListener<String, String> {

    @Autowired
    private Tracing tracing;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private WorkOrderDataStatisticsService workOrderDataStatisticsService;

    private static final List<String> DB_OPERATE_TYPE = Lists.newArrayList("insert", "update");
    private static final List<String> DB_TABLE = Lists.newArrayList("work_order");


//    @KafkaListener(
//            topics = {
//                    "shuidi_cf_admin_work_order"
//            },
//            clientIdPrefix = "cf_work_order_data_statistics_consumer",
//            groupId = "cf_work_order_data_statistics_consumer",
//            containerFactory = "batchContainerFactory")
    @Override
    public void onMessage(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        if (CollectionUtils.isEmpty(records)) {
            ack.acknowledge();
            return;
        }

        handleRecords(records);

        ack.acknowledge();
    }

    private void handleRecords(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            Span span = KafkaTracing.newBuilder(tracing).build().nextSpan(record).name("cf_work_order_data_statistics_consumer").start();
            try (Tracer.SpanInScope ws = tracing.tracer().withSpanInScope(span)) {
                Optional<BinLog> binLogOptional = convertBinLog(record.value());
                if (binLogOptional.isEmpty()) {
                    continue;
                }

                BinLog binLog = binLogOptional.get();
                log.info("binLog:{}", binLog);
                workOrderDataStatisticsService.handleBinLog(binLog);
            } catch (Exception e) {
                span.error(e);
                log.error("handle record error. record:{}", record, e);
            }finally {
                span.finish();
            }
        }
    }

    private Optional<List<BinLog>> convertBinLogs(List<String> messages) {
        if (CollectionUtils.isEmpty(messages)) {
            return Optional.empty();
        }
        List<BinLog> binLogs = messages.stream().map(x -> convertBinLog(x)).filter(y -> y.isPresent()).map(x -> x.get()).collect(Collectors.toList());

        return Optional.of(binLogs);
    }

    private Optional<BinLog> convertBinLog(String message) {
        if (StringUtils.isBlank(message)) {
            return Optional.empty();
        }

        BinLog binLog = JSON.parseObject(message, BinLog.class);

        if (binLog == null || binLog.getHead() == null) {
            return Optional.empty();
        }

        if (StringUtils.isBlank(binLog.getHead().getType()) || !DB_OPERATE_TYPE.contains(binLog.getHead().getType().toLowerCase())) {
            return Optional.empty();
        }

        if (StringUtils.isBlank(binLog.getHead().getTable()) || !DB_TABLE.contains(binLog.getHead().getTable().toLowerCase())) {
            return Optional.empty();
        }

        if (binLog.getBefore() == null && binLog.getAfter() == null) {
            return Optional.empty();
        }

        return Optional.of(binLog);
    }
}
