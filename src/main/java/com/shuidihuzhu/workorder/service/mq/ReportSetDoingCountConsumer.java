package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * @author: lixiaoshuang
 * @create: 2021-01-04 11:16
 **/
@Service
@RocketMQListener(id = CfClientMQTagCons.REPORT_SET_DOING_COUNT,
        tags = CfClientMQTagCons.REPORT_SET_DOING_COUNT,
        topic = MQTopicCons.CF
)
@Slf4j
public class ReportSetDoingCountConsumer implements MessageListener<Integer> {

    private static final String REPORT_SET_DOING_COUNT_KEY = "REPORT_SET_DOING_COUNT_KEY";

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {
        var payload = mqMessage.getPayload();
        log.info("ReportSetDoingCountConsumer payload:{}", payload);
        if (null == payload) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        LocalDateTime now = LocalDateTime.now();
        var nowMills = now.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        LocalDateTime withTime = now.withHour(23).withMinute(59).withSecond(59);
        long withMills = withTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        cf2RedissonHandler.setEX(REPORT_SET_DOING_COUNT_KEY, payload, withMills - nowMills);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
