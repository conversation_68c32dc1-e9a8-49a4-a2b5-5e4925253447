package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.pr.common.model.enums.project.ProjectConst;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.custom.recruit.PrCommonAssigner;
import com.shuidihuzhu.workorder.service.custom.recruit.WorkOrderStaffConfService;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/04/01 下午3:00
 */
@Slf4j
@Service("prSupplementWorkOrderAssignImpl")
public class PrSupplementWorkOrderAssignImpl extends PrCommonAssigner {

    private static final int maxLoad = 200;

    @Resource
    private WorkOrderDaoExt daoExt;
    @Resource
    private WorkOrderStaffConfService workOrderStaffConfService;
    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        return OpResult.createSucResult(fetchPendingOnAssociated(orderType, resultEnum,
                OrderExtName.treatmentLines.getName(), ProjectConst.TreatmentLines.IINITIAL_TREATMENT.getCode()+"", maxLoad, null));
    }

    @Override
    protected PrResponsibleVo selectResponsible(int orderType, WorkOrderBase workOrderBase) {
        WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.supplementMaterialId.getName());
        long supplementMaterialId = Optional.ofNullable(workOrderExt).map(WorkOrderExt::getExtValue).map(Long::parseLong).orElse(0L);
        return workOrderStaffConfService.assignForSupplement(workOrderBase.getId(), workOrderBase.getCaseId(), supplementMaterialId);
    }

}
