package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.admin.client.CfHospitalAuditClient;
import com.shuidihuzhu.client.cf.admin.model.HospitalAudit;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderCreate;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.vo.GenjinModel;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/8/14
 */
@Service
@RocketMQListener(id = "work_order_genjin",
        group = "work_order_genjin_group",
        tags = MQTag.genjin_order_create_mq,
        topic = MQTag.CF)
@Slf4j
public class DianhuaGenjinConsumer implements MessageListener<GenjinModel> {

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Resource(name = "dianhuaWorkOrderService")
    private WorkOrderFacade dianhuaWorkOrderService;

    @Autowired
    private CfHospitalAuditClient auditClient;

    @Autowired
    private CrowdfundingFeignClient client;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<GenjinModel> mqMessage) {

        log.info("DianhuaGenjinConsumer mqMessage={}",mqMessage);

        GenjinModel gm = mqMessage.getPayload();

        if (gm == null || gm.getCaseId() == 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        FeignResponse<CrowdfundingInfo> feignResponse =  client.getCaseInfoById(gm.getCaseId());

        if (feignResponse == null || feignResponse.getData()== null){
            log.info("CrowdfundingInfo == null caseid={}",gm.getCaseId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Response<HospitalAudit> response =  auditClient.getHospitalAudit(feignResponse.getData().getInfoId());

        if (response == null || response.getData() == null){
            log.info("HospitalAudit == null caseid={}",gm.getCaseId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        HospitalAudit hospitalAudit = response.getData();

        /*
         * 医院核实状态
         *     UN_SAVE(0),
         *     UN_SUBMITTED(1),
         *     PASSED(2),
         *     REJECTED(3),
         *     SUBMITTED(4);
         */
        //如果取消 通过 提交  不再生成电话跟进工单
        if (hospitalAudit.getAuditStatus() == 0 || hospitalAudit.getAuditStatus() == 2 || hospitalAudit.getAuditStatus()== 4){
            log.info("AuditStatus = {} caseid={}",hospitalAudit.getAuditStatus(),gm.getCaseId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if (hospitalAudit.getAuditStatus() == 3 && gm.getOperation().equals(GenjinModel.xiafa)){
            log.info("驳回后不再走下发的生成跟进逻辑 AuditStatus = {} caseid={}",hospitalAudit.getAuditStatus(),gm.getCaseId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        DianhuaWorkOrder workOrderCreate = new DianhuaWorkOrder();
        workOrderCreate.setCaseId(gm.getCaseId());
        workOrderCreate.setOrderType(WorkOrderType.genjin.getType());
        workOrderCreate.setOrderlevel(OrderLevel.edium.getType());
        if (GenjinModel.xiafa.equals(gm.getOperation())){
            workOrderCreate.setOrderlevel(OrderLevel.high.getType());
        }
        workOrderCreate.setYiyuanHeshiId(gm.getYiyuanHeshiId());
        workOrderCreate.setYiyuanHeshiTime(gm.getYiyuanHeshiTime());
        workOrderCreate.setCreateNow(true);
        workOrderCreate.setOperComment("医院核实"+(gm.getOperation().equals(GenjinModel.xiafa)?"下发":"驳回"));
        OpResult opResult = dianhuaWorkOrderService.doCreate(workOrderCreate);
        log.info("DianhuaGenjinConsumer workOrderCreate={},opResult={}",workOrderCreate,opResult);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
