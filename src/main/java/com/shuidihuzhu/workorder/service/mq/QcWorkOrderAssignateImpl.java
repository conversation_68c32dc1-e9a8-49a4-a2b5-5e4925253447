package com.shuidihuzhu.workorder.service.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.client.admin.RiskQcBaseClient;
import com.shuidihuzhu.cf.risk.model.risk.RiskQcSearchIndexVO;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackTaskFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClueInfoModel;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.cf.workorder.model.vo.RiskQualitySpotRuleDetailVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.dao.WorkOrderOrgRelDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.CfWorkOrderQualitySpotService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2020/06/16 下午5:00
 * @desc
 */
@Slf4j
@Service("qcWorkOrderAssignateImpl")
public class QcWorkOrderAssignateImpl extends AssignateWorkOrderFacade {
    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired
    private StaffStatusService staffStatusService;


    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Resource
    private CfWorkOrderQualitySpotService cfWorkOrderQualitySpotService;

    @Autowired
    private WorkOrderOrgRelDao workOrderOrgRelDao;

    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private CfClewtrackTaskFeignClient cfClewtrackTaskFeignClient;

    @Autowired
    private RiskQcBaseClient riskQcBaseClient;

    @Resource(name = "generalWorkOrderAssignateImpl")
    private AssignateWorkOrderFacade generalWorkOrderAssignateImpl;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult doAssignate(int orderType){
        if (orderType == WorkOrderType.qc_zhu_dong.getType()) {
            return super.doAssignate(orderType);
        }

        OpResult opResult = validate(orderType);
        if(null == opResult || !opResult.isSuccess()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String lockName = getLockKey(orderType);
        RLock rLock = null;

        try {
            meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,OperationStat.OPERATION,OperationStat.assignate,OperationStat.ORDERTYPE,orderType+"").increment();

            rLock = cf2RedissonHandler.getLock(lockName);
            if (!rLock.tryLock()){
                return OpResult.createFailResult(ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
            }

            OpResult<List<WorkOrderBase>> workOrderOpResult = getAssignateWorkOrder(orderType, HandleResultEnum.undoing);
            if(null == workOrderOpResult || !workOrderOpResult.isSuccess() || CollectionUtils.isEmpty(workOrderOpResult.getData())){
                return OpResult.createFailResult(ErrorCode.NOT_MORE_WORK_ORDER);
            }
            List<WorkOrderBase> workOrderBases = workOrderOpResult.getData();
            assignateWorkOrder(orderType, workOrderBases);
            return OpResult.createSucResult();
        } catch (Exception e){
            log.error("assignate workorder exeception.", e);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ERROR);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if (Objects.isNull(workOrderTypeService.getFromOrderTypeCode(orderType)) || Objects.isNull(resultEnum)) {
            return OpResult.createSucResult(Lists.newArrayList());
        }
        //查询工单
        List<WorkOrderBase> workOrderBases = queryOrderList(orderType, resultEnum);
        //分组处理
        List<List<WorkOrderBase>> partition = Lists.partition(workOrderBases, 500);
        List<WorkOrderBase> resultList = Lists.newArrayList();
        for (List<WorkOrderBase> workOrderBaseList : partition) {
            var workOrderIds = workOrderBaseList.parallelStream().map(WorkOrderBase::getId).collect(Collectors.toList());
            List<WorkOrderExt> workOrderExts = daoExt.getWorkOrderExts(workOrderIds, OrderExtName.qcAssignType.getName());
            Map<Long,WorkOrderExt> workOrderExtMap = workOrderExts.parallelStream()
                    .collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity(), (k1, k2) -> k2));
            for (WorkOrderBase workOrderBase : workOrderBaseList) {
                WorkOrderExt workOrderExt = workOrderExtMap.get(workOrderBase.getId());
                if (Objects.nonNull(workOrderExt) && Integer.toString(AssignTypeEnum.MUST_ASSIGN.getCode()).equals(workOrderExt.getExtValue())){
                    resultList.add(workOrderBase);
                }
            }
        }

        return OpResult.createSucResult(resultList);
    }

    private List<WorkOrderBase> queryOrderList(int orderType, HandleResultEnum resultEnum) {
        List<WorkOrderBase> workOrderBases = Lists.newArrayList();
        if (orderType == WorkOrderType.qc_complaint.getType()
                || orderType == WorkOrderType.qc_second_complaint.getType()
                || orderType == WorkOrderType.qc_serious_complaint.getType()) {
            workOrderBases.addAll(workOrderDao.queryUndoOrderOrderByLevel(orderType, resultEnum.getType()));
        } else {
            LocalDateTime now = LocalDateTime.now().minusDays(3).withHour(0).withMinute(0).withSecond(0);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String createTime = now.format(dateTimeFormatter);
            workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTimeFromTidb(orderType, resultEnum.getType(), createTime);
        }
        return workOrderBases;
    }


    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBases) {
        if (orderType == WorkOrderType.qc_zhu_dong.getType()) {
            return generalWorkOrderAssignateImpl.getAssignateStaff(orderType, workOrderBases);
        }

      /*  List<StaffStatus> staffStatuses = this.queryQcOnlineStaff(workOrderType, workOrderBases);

        Long availableUser = this.qcAssignateStaff(staffStatuses, workOrderType);*/

        return OpResult.createSucResult();
    }

    /**
     * 微信1v1改造
     * @param orderType 工单类型code
     * @param workOrderBases    工单基础信息
     * @return
     */
    public Long getAssignateStaffV2(int orderType, List<WorkOrderBase> workOrderBases) {
        //获取在线的 符合领取条件的用户
        List<StaffStatus> staffStatuses = queryQcOnlineStaffV2(orderType, workOrderBases.get(0));
        return qcAssignateStaffV2(staffStatuses, orderType);
    }

    public List<StaffStatus> queryQcOnlineStaff(int orderType) {

        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if(workOrderTypeDO == null){
            log.info("queryQcOnlineStaff workOrderType={} workOrderTypeDO=null", orderType);
            return null;
        }
        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(workOrderTypeDO.getPermission());

        if (Objects.isNull(response) || response.notOk()) {
            log.info("queryQcOnlineStaff workOrderTypeDO={} rpcRespons={}", workOrderTypeDO, response);
            return null;
        }
        if (CollectionUtils.isEmpty(response.getData())) {
            log.info("queryQcOnlineStaff workOrderTypeDO={} userAccountModelList=null", workOrderTypeDO);
            return null;
        }
        List<Long> userIds = response.getData().stream().map(r -> Long.valueOf(r.getUserId())).collect(Collectors.toList());
        //查询在线用户
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineToday(userIds, orderType);
        //autoAllocation==1表示接受自动分配工单
        staffList = staffList.stream().filter(s -> 1 == s.getAutoAllocation()).collect(Collectors.toList());

        return staffList;
    }

    public List<StaffStatus> queryQcOnlineStaffV2(int orderType, WorkOrderBase workOrderBase) {
        return filterRule(queryQcOnlineStaff(orderType), workOrderBase, orderType);
    }

    private List<StaffStatus> filterRule(List<StaffStatus> staffList, WorkOrderBase workOrderBase, int orderType) {
        if (CollectionUtils.isEmpty(staffList)){
            return Lists.newArrayList();
        }
        List<StaffStatus> resultUsers = Lists.newArrayList();
        long scene = getSceneByWorkOrderType(orderType);
        if (scene == 0){
            //如果不是规定的质检类型则不分配工单
            return Lists.newArrayList();
        }
        //查询工单信息
        List<WorkOrderExt> workOrderExtList = daoExt.getWorkOrderExtByName(workOrderBase.getId(),
                Lists.newArrayList(OrderExtName.ruleId.getName(), OrderExtName.qcUserName.getName()));
        if (CollectionUtils.isEmpty(workOrderExtList)){
            return Lists.newArrayList();
        }
        //查询层级信息
        List<WorkOrderOrgRel> workOrderOrgRels = workOrderOrgRelDao.getByWorkOrderId(workOrderBase.getId());
        List<Long> workOrderOrgIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(workOrderOrgRels)){
            workOrderOrgIds = workOrderOrgRels.stream().map(WorkOrderOrgRel::getOrgId).map(Long::new).collect(Collectors.toList());
        }

        List<RiskQualitySpotWorkOrderUserConfig> configs= cfWorkOrderQualitySpotService.listByUserIds(
                staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList()), scene);
        Map<Long, List<RiskQualitySpotWorkOrderUserConfig>> configMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(configs)){
            configMap = configs.parallelStream()
                    .collect(Collectors.groupingBy(RiskQualitySpotWorkOrderUserConfig::getUserId));
        }
        //对符合条件的用户进行过滤 符合规则和随机分配的直接返回
        for (StaffStatus staffStatus : staffList) {
            //查询用户的规则
            List<RiskQualitySpotWorkOrderUserConfig> configList = configMap.get(staffStatus.getUserId());
            RiskQualitySpotWorkOrderUserConfig config = CollectionUtils.isEmpty(configList) ? null : configList.get(0);
            if (config == null || config.getDistributionType() == QualitySpotWorkOrderDistributionEnum.RANDOM.getCode()) {
                resultUsers.add(staffStatus);
                continue;
            }
            List<RiskQualitySpotRuleDetailVo> riskQualitySpotRuleDetailVos = JSON.parseArray(config.getRuleInfo(),
                    RiskQualitySpotRuleDetailVo.class);
            if (CollectionUtils.isEmpty(riskQualitySpotRuleDetailVos)
                    && config.getRangeLimit() == QualitySpotWorkOrderRangeLimitEnum.PRIORITY.getCode()) {
                resultUsers.add(staffStatus);
                continue;
            }
            if (filterSpotRule(riskQualitySpotRuleDetailVos, workOrderExtList, workOrderOrgIds, 0, orderType, workOrderBase.getId())) {
                resultUsers.add(staffStatus);
                continue;
            }
            //若果是优先的直接分配
            if (config.getRangeLimit() == QualitySpotWorkOrderRangeLimitEnum.PRIORITY.getCode()) {
                resultUsers.add(staffStatus);
            }
        }
        return resultUsers;
    }

    private boolean filterSpotRule(List<RiskQualitySpotRuleDetailVo> riskQualitySpotRuleDetailVos,
                                   List<WorkOrderExt> workOrderExts, List<Long> workOrderOrgIds, long taskId,
                                   int orderType, long workOrderId) {
        //验证规则 3种情况
        /**
         * 1. 只有规则id
         * 2. 只有组织架构
         * 3. 外呼服务人员名称
         * 4. 1v1工作内容
         */
        Map<String, String> workOrderExtMap = workOrderExts.stream()
                .collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue));
        long ruleId = Long.parseLong(workOrderExtMap.getOrDefault(OrderExtName.ruleId.getName(), "-1"));
        String checkName = workOrderExtMap.get(OrderExtName.qcUserName.getName());
        for (RiskQualitySpotRuleDetailVo ruleDetailVo : riskQualitySpotRuleDetailVos) {
            if (ruleDetailVo.getRuleId() > 0 && ruleId != ruleDetailVo.getRuleId()) {
                continue;
            }
            if (ruleDetailVo.getOrgId() > 0 && !workOrderOrgIds.contains(ruleDetailVo.getOrgId())){
                continue;
            }
            if (StringUtils.isNotBlank(ruleDetailVo.getCheckedName()) && (orderType == WorkOrderType.qc_call.getType() ||
                    orderType == WorkOrderType.qc_material_audit.getType())) {
                List<String> nameList = Arrays.asList(StringUtils.split(ruleDetailVo.getCheckedName(),",，"));
                if (!nameList.contains(checkName)) {
                    continue;
                }
            }
            if (CollectionUtils.isNotEmpty(ruleDetailVo.getWorkContentType()) && orderType == WorkOrderType.qc_wx_1v1.getType()){
                Response<List<CfClueInfoModel>> response = cfClewtrackTaskFeignClient.listCfClueInfo(List.of(taskId));
                if (response == null || response.notOk() || CollectionUtils.isEmpty(response.getData())){
                    continue;
                }
                CfClueInfoModel cfClueInfoModel = response.getData().get(0);
                int serviceStage = Objects.isNull(cfClueInfoModel) ? -1 :
                        Optional.ofNullable(cfClueInfoModel.getServicePhase()).orElse(-1);
                List<Integer> workContents = ruleDetailVo.getWorkContentType();
                if (!workContents.contains(serviceStage)){
                    continue;
                }
            }
            if (orderType == WorkOrderType.qc_material_audit.getType() && (ruleDetailVo.getCallResult() > 0 || ruleDetailVo.getWorkOrderDealResult() > 0)){
                Response<RiskQcSearchIndexVO> riskQcSearchIndexVOResponse = riskQcBaseClient.getRiskQcSearchIndex(workOrderId);
                log.info("riskQcBaseClient getRiskQcSearchIndex:{}", JSON.toJSONString(riskQcSearchIndexVOResponse));
                if (riskQcSearchIndexVOResponse == null || riskQcSearchIndexVOResponse.notOk()
                        || riskQcSearchIndexVOResponse.getData() == null){
                    continue;
                }
                RiskQcSearchIndexVO riskQcSearchIndexVO = riskQcSearchIndexVOResponse.getData();
                if (ruleDetailVo.getCallResult() > 0 && riskQcSearchIndexVO.getCallStatus() != ruleDetailVo.getCallResult()){
                    continue;
                }
                if (ruleDetailVo.getWorkOrderDealResult() > 0 &&
                        riskQcSearchIndexVO.getHandleResult() != ruleDetailVo.getWorkOrderDealResult()){
                    continue;
                }
            }
            return true;
        }
        return false;
    }

    private long getSceneByWorkOrderType(int orderType) {
        switch (orderType){
            case WorkOrderTypeConstants.QC_COMMON:
                return 3;
            case WorkOrderTypeConstants.QC_WX_1V1:
                return 4;
            case WorkOrderTypeConstants.QC_CALL:
                return 6;
            case WorkOrderTypeConstants.QC_MATERIAL_AUDIT:
                return 8;
            case WorkOrderTypeConstants.QC_COMPLAINT:
                return 9;
            case WorkOrderTypeConstants.QC_SECOND_COMPLAINT:
                return 10;
            case WorkOrderTypeConstants.QC_SERIOUS_COMPLAINT:
                return 11;
            case WorkOrderTypeConstants.QC_WX_1V1_REPEAT:
                return 12;
            case WorkOrderTypeConstants.QC_COMMON_REPEAT:
                return 13;
            default:
                return 0;
        }
    }

    private Long qcAssignateStaff(List<StaffStatus> staffList, WorkOrderType workOrderType) {
        if (CollectionUtils.isEmpty(staffList)) {
            log.info("qcAssignateStaff workOrderType={} StaffOnline=null", workOrderType);
            return null;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());

        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users, workOrderType.getType(), HandleResultEnum.doing.getType());

        Map<Long, WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));

        //确定在线人员处理中的工单数量
        list = users.stream().map(r -> {
            if (Objects.nonNull(doingMap.get(r))) {
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r, 0);
        }).filter(workOrderDoingCount -> workOrderDoingCount.getNum() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return 0L;
        }
        WorkOrderDoingCount workOrderDoingCount = list.get(0);
        return workOrderDoingCount.getOperatorId();
    }

    private Long qcAssignateStaffV2(List<StaffStatus> staffList, int orderType) {
        if (CollectionUtils.isEmpty(staffList)) {
            log.info("qcAssignateStaff orderType={} StaffOnline=null", orderType);
            return 0L;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());

        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users, orderType, HandleResultEnum.doing.getType());

        Map<Long, WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity()));

        //确定在线人员处理中的工单数量
        list = users.stream().map(r -> {
            if (Objects.nonNull(doingMap.get(r))) {
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r, 0);
        }).filter(workOrderDoingCount -> workOrderDoingCount.getNum() == 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return 0L;
        }
        return staffStatusService.getStaffOrderByAssignTime(list.stream()
                .map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList()), orderType);
/*        WorkOrderDoingCount workOrderDoingCount = list.get(0);
        return workOrderDoingCount.getOperatorId();*/
    }


    /**
     * 质检分配v3
     * @param orderType
     * @param workOrderBases
     */
    public void assignateWorkOrder(int orderType, List<WorkOrderBase> workOrderBases){
        if (CollectionUtils.isEmpty(workOrderBases)){
            log.info("assignateWorkOrder workOrderBases empty");
            return;
        }
        workOrderBases = workOrderBases.stream().sorted(Comparator.comparing(WorkOrderBase::getCreateTime)).collect(Collectors.toList());
        List<Long> userIds = queryQcOnlineStaffV3(orderType);
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        long scene = getSceneByWorkOrderType(orderType);
        if (scene == 0){
            //如果不是规定的质检类型则不分配工单
            return;
        }
        List<RiskQualitySpotWorkOrderUserConfig> configs= cfWorkOrderQualitySpotService.listByUserIds(userIds, scene);
        Map<Long, List<RiskQualitySpotWorkOrderUserConfig>> configMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(configs)){
            configMap = configs.parallelStream()
                    .collect(Collectors.groupingBy(RiskQualitySpotWorkOrderUserConfig::getUserId));
        }
        for (Long userId : userIds) {
            int j = 1;
            if (orderType == WorkOrderType.qc_material_audit.getType()) {
                j = 5;
            }
            for (int i = 0; i < j; i++) {
                long workOrderId = filterRuleV1(userId, workOrderBases, configMap, orderType);
                if (workOrderId == 0) {
                    continue;
                }
                Response<Long> response = assignWorkOrderService.assignWorkOrder(workOrderId, userId);
                if (response != null && response.ok()) {
                    workOrderBases = workOrderBases.stream().filter(t -> t.getId() != workOrderId).collect(Collectors.toList());
                }
                WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
                if(workOrderTypeDO == null){
                    log.info("doAssignate workOrderTypeDO is null.userId:{}, workOrderId:{}", userId, workOrderId);
                }else {
                    log.info("doAssignate workorder success.type:{}, userId:{}, workOrderId:{}", workOrderTypeDO.getMsg(), userId, workOrderId);
                }
            }
        }
    }


    private long filterRuleV1(Long userId, List<WorkOrderBase> workOrderBases,
                              Map<Long, List<RiskQualitySpotWorkOrderUserConfig>> configMap, int orderType){
        if (CollectionUtils.isEmpty(workOrderBases)){
            return 0;
        }
        List<RiskQualitySpotWorkOrderUserConfig> configList = configMap.get(userId);
        RiskQualitySpotWorkOrderUserConfig config = CollectionUtils.isEmpty(configList) ? null : configList.get(0);

        //随机分单
        if (config == null || config.getDistributionType() == QualitySpotWorkOrderDistributionEnum.RANDOM.getCode()) {
            WorkOrderBase workOrderBase = Optional.ofNullable(workOrderBases.get(0)).orElseGet(WorkOrderBaseVo::new);
            return workOrderBase.getId();
        }

        List<RiskQualitySpotRuleDetailVo> riskQualitySpotRuleDetailVos = JSON.parseArray(config.getRuleInfo(),
                RiskQualitySpotRuleDetailVo.class);
        if (CollectionUtils.isEmpty(riskQualitySpotRuleDetailVos)
                && config.getRangeLimit() == QualitySpotWorkOrderRangeLimitEnum.PRIORITY.getCode()) {
            WorkOrderBase workOrderBase = Optional.ofNullable(workOrderBases.get(0)).orElseGet(WorkOrderBaseVo::new);
            return workOrderBase.getId();
        }

        //通过优先级排序，数字越大，优先级越高
        riskQualitySpotRuleDetailVos = riskQualitySpotRuleDetailVos.stream().sorted(
                Comparator.comparing(RiskQualitySpotRuleDetailVo::getSort, Comparator.reverseOrder())).collect(Collectors.toList());

        //每个规则跑所有工单
        for (RiskQualitySpotRuleDetailVo ruleDetailVo : riskQualitySpotRuleDetailVos) {
            for (WorkOrderBase workOrderBase : workOrderBases) {
                WorkOrderExt workOrderExt = daoExt.getWorkOrderExtDesc(workOrderBase.getId(), OrderExtName.ruleId.getName());
                if (workOrderExt == null) {
                    continue;
                }
                List<WorkOrderExt> workOrderExts = Lists.newArrayList(workOrderExt);
                if (orderType == WorkOrderType.qc_call.getType() || orderType == WorkOrderType.qc_material_audit.getType()){
                    WorkOrderExt userNameWorkOrderExt =
                            daoExt.getWorkOrderExtDesc(workOrderBase.getId(), OrderExtName.qcUserName.getName());
                    if (userNameWorkOrderExt != null){
                        workOrderExts.add(userNameWorkOrderExt);
                    }
                }
                //查询层级信息
                List<WorkOrderOrgRel> workOrderOrgRels = workOrderOrgRelDao.getByWorkOrderId(workOrderBase.getId());
                List<Long> workOrderOrgIds = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(workOrderOrgRels)) {
                    workOrderOrgIds =
                            workOrderOrgRels.stream().map(WorkOrderOrgRel::getOrgId).map(Long::valueOf).collect(Collectors.toList());
                }
                if (filterSpotRule(List.of(ruleDetailVo), workOrderExts,
                        workOrderOrgIds, workOrderBase.getCaseId(), orderType, workOrderBase.getId())){
                    log.info("filterUserSpotRule info:{}, userId:{}, workOrderId:{}", JSON.toJSONString(ruleDetailVo),
                            userId, workOrderBase.getId());
                    return workOrderBase.getId();
                }
            }
        }
        //如果所有工单都不符合规则并且是优先领取配置工单
        if (config.getRangeLimit() == QualitySpotWorkOrderRangeLimitEnum.PRIORITY.getCode()){
            WorkOrderBase workOrderBase = Optional.ofNullable(workOrderBases.get(0)).orElseGet(WorkOrderBaseVo::new);
            log.info("no target rule, auto assignate userId:{}, workOrderId:{}", userId, workOrderBase.getId());
            return workOrderBase.getId();
        }
        return 0;
    }


    public List<Long> queryQcOnlineStaffV3(int orderType){
        List<StaffStatus> staffStatuses = queryQcOnlineStaff(orderType);
        if (CollectionUtils.isEmpty(staffStatuses)){
            return Lists.newArrayList();
        }
        List<Long> userIds = staffStatuses.stream().map(StaffStatus::getOperationId).collect(Collectors.toList());
        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(userIds, orderType, HandleResultEnum.doing.getType());

        Map<Long, WorkOrderDoingCount> doingMap = CollectionUtils.isEmpty(list) ? Maps.newHashMap() :
                list.stream().collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity(), (k1, k2) -> k2));

        //获取在线且没有处理工单的人员
        userIds = MapUtils.isEmpty(doingMap) ? userIds : userIds.stream().filter(t ->  doingMap.get(t) == null).collect(Collectors.toList());
        log.info("queryQcOnlineStaffV3 userIds:{}", userIds);
        return userIds;
    }
}
