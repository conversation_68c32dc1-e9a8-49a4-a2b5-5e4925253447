package com.shuidihuzhu.workorder.service.mq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.JuanzhuanWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.StaffRealTimeWorkData;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.impl.JuanzhuanWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/5/9
 */
@Service("juanzhuanWorkOrderAssignate")
@Slf4j
public class JuanzhuanWorkOrderAssignateImpl extends AssignateWorkOrderFacade {

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private BaseWorkOrderService workOrderService;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Resource(name="juanzhuanWorkOrderService")
    private JuanzhuanWorkOrderServiceImpl juanzhuanWorkOrderService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult doAssignate(int orderType) {

        OpResult<List<WorkOrderBaseVo>> unHandlerWorksOptResult = getAssignateWorkOrders(orderType, HandleResultEnum.undoing);

        if(null == unHandlerWorksOptResult || !unHandlerWorksOptResult.isSuccess() || CollectionUtils.isEmpty(unHandlerWorksOptResult.getData())){
            return OpResult.createFailResult(ErrorCode.NOT_MORE_WORK_ORDER);
        }

        int limitNum = 1;
        List<WorkOrderBaseVo> unHandlerWorks = unHandlerWorksOptResult.getData();

        if (orderType == WorkOrderType.d0_1v1.getType() || orderType == WorkOrderType.d0_1v1_tzb.getType() ) {
            log.info("分配1v1d0的任务");
            return super.doAssignate(orderType);
        }

        //不是d0工单
        String lockName = getLockKey(orderType);
        RLock rLock = cf2RedissonHandler.getLock(lockName);
        if (!rLock.tryLock()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
        }
        try {
            return assignJuanzhuanDetail(orderType, limitNum, unHandlerWorks);
        } catch (Exception e) {
            log.error("juanzhuan assignate workorder exeception.", e);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ERROR);
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
    }

    @NotNull
    private OpResult assignJuanzhuanDetail(int orderType, int limitNum, List<WorkOrderBaseVo> unHandlerWorks) {
        //特种兵用户
        Set<Long> tzbSet  = getTzbUsers();

        List<WorkOrderBaseVo> copyUnHandlerWorks = Lists.newArrayList();
        for (WorkOrderBaseVo unHandlerWork : unHandlerWorks) {
            //优先分配d0处理的人员
            long assignStaff = getlastOperatorId(unHandlerWork, limitNum, tzbSet);
            if (assignStaff > 0) {
                log.info("捐转工单code:{},对应的案例:{}直接分配到:{}", orderType, unHandlerWork.getCaseId(), assignStaff);
                assignWorkOrderService.assignWorkOrder(unHandlerWork, assignStaff);
            } else {
                copyUnHandlerWorks.add(unHandlerWork);
            }
        }
        //防止生成工单的时候就直接分配给空闲人员，但是当前空闲人员可能不是真正的空闲，因此需要判断是不是真正的空闲
        //找到仍然空闲员工
        //先粗暴点直接根据时间判断,如果在09:10之前或者16:40之前,有空闲人员也不分配
        DateTime now = DateTime.now();
        DateTime firstStartTime = now.withHourOfDay(9).withMinuteOfHour(0).withSecondOfMinute(0);
        DateTime firstEndTime = now.withHourOfDay(9).withMinuteOfHour(10).withSecondOfMinute(0);
        DateTime secondStartTime = now.withHourOfDay(16).withMinuteOfHour(30).withSecondOfMinute(0);
        DateTime secondEndTime = now.withHourOfDay(16).withMinuteOfHour(40).withSecondOfMinute(0);

        if ((now.isAfter(firstStartTime) && now.isBefore(firstEndTime)) || (now.isAfter(secondStartTime) && now.isBefore(secondEndTime))) {
            log.info("时间为:{},防止伪空闲人员分配任务", now);
            return OpResult.createSucResult();
        }
        List<StaffStatus> staffStatuses = queryOnlineStaff(orderType);

        final String tzb = "tzb";
        final String common = "common";

        Map<String,List<StaffStatus>> staffs = staffStatuses.stream().collect(Collectors.groupingBy(r->{
            if (tzbSet.contains(r.getUserId())){
                return tzb;
            }else {
                return common;
            }
        }));

        Map<String,List<WorkOrderBaseVo>> works = copyUnHandlerWorks.stream().collect(Collectors.groupingBy(r->{
            if (JuanzhuanWorkOrder.ext_type_tzb.equals(r.getOrderExtType())){
                return tzb;
            }else {
                return common;
            }
        }));

        //特种兵单独分配
        assignWorkOrder(orderType,limitNum,staffs.get(tzb),works.get(tzb));
        //普通工单单独分配
        assignWorkOrder(orderType,limitNum,staffs.get(common),works.get(common));

        return OpResult.createSucResult();
    }

    private OpResult assignWorkOrder(int orderType, int limitNum,List<StaffStatus> staffStatuses,List<WorkOrderBaseVo> copyUnHandlerWorks){

        Long availableUser = calCanAssignateStaff(staffStatuses, orderType, limitNum);
        if (availableUser <= 0) {
            log.info("orderType:{}找不到对应分配的人员", orderType);
            return OpResult.createSucResult();
        }
        if (CollectionUtils.isEmpty(copyUnHandlerWorks)){
            log.info("orderType:{} 待分配数量为0", orderType);
            return OpResult.createSucResult();
        }
        //判断下这个人是不是真正的空闲
        Integer personUnHandlerWork = workOrderService.listUnHandleWork(availableUser, Lists.newArrayList(orderType))
                .stream().filter(item -> item.getOrderType() == orderType)
                .map(StaffRealTimeWorkData::getPersonalUnHandle)
                .findFirst().orElse(0);
        if (personUnHandlerWork > 0) {
            log.info("员工:{}orderType:{}工单个数:{}不能处理其他人工单", availableUser, orderType, personUnHandlerWork);
            return OpResult.createSucResult();
        }
        log.info("已经处理完自己任务的员工:{}", availableUser);
        log.debug("copyUnHandlerWorks={}",copyUnHandlerWorks);
        //找到还没处理任务中对应d0中最多的
        Map<Long, List<WorkOrderBase>> operatorTNeedHandle = copyUnHandlerWorks
                .stream()
                .collect(Collectors.groupingBy(WorkOrderBase::getOperatorId));

        int maxUnHandleCount = 0;
        WorkOrderBase needHandleBase = null;
        for (Long operatorId : operatorTNeedHandle.keySet()) {

            //查询可以分配工单的人员是否设置了可以分配给他人
            StaffStatus ss = staffStatusService.getStaffStatus(operatorId, orderType);
            log.info("checkAssignOrdertype ss={},ordertype={}",ss,orderType);
            if (ss != null && !ss.checkAssignOrdertype(orderType)){
                continue;
            }
            List<WorkOrderBase> needHandleBaseList = operatorTNeedHandle.get(operatorId);
            if (maxUnHandleCount < needHandleBaseList.size()) {
                maxUnHandleCount = needHandleBaseList.size();
                needHandleBase = needHandleBaseList.stream()
                        .min(Ordering.natural().onResultOf(WorkOrderBase::getId))
                        .orElse(null);
            }
        }
        log.info("遗留任务个数最多的为:{},需要处理的任务:{},处理人:{}", maxUnHandleCount, JSON.toJSONString(needHandleBase), availableUser);
        if (needHandleBase != null) {
            //分配之前再确认一下人员是否设置可以共享
            if (needHandleBase.getOperatorId() > 0 ){
                StaffStatus ss = staffStatusService.getStaffStatus(needHandleBase.getOperatorId(), orderType);
                if (ss != null && !ss.checkAssignOrdertype(orderType)){
                    log.info("checkAssignOrdertype 人员未共享不能分配 caseId={} id={} oper={}",needHandleBase.getCaseId(),needHandleBase.getId(),needHandleBase.getOperatorId());
                    return OpResult.createSucResult();
                }
            }
            assignWorkOrderService.assignWorkOrder(needHandleBase, availableUser);
        }

        return OpResult.createSucResult();

    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if(null == workOrderTypeService.getFromOrderTypeCode(orderType) || null == resultEnum){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),null, null);

        return OpResult.createSucResult(workOrderBases);
    }

    public OpResult<List<WorkOrderBaseVo>> getAssignateWorkOrders(int orderType, HandleResultEnum resultEnum) {
        if(null == workOrderTypeService.getFromOrderTypeCode(orderType) || null == resultEnum){
            return OpResult.createSucResult(Lists.newArrayList());
        }
        return OpResult.createSucResult(juanzhuanWorkOrderService.getJuanzhuanOrders(orderType,resultEnum.getType()));
    }


    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBases) {

        int limitNum = 1;

        List<StaffStatus> staffStatuses = queryOnlineStaff(orderType);

        long availableUser = calCanAssignateStaff(staffStatuses, orderType, limitNum);

        return OpResult.createSucResult(availableUser);
    }




    private long getlastOperatorId(WorkOrderBaseVo base, int limitNum,Set<Long> tzbSet){

        log.info("get last caseId={}",base.getCaseId());

        List<Integer> types = Lists.newArrayList(WorkOrderType.d0_1v1.getType(),WorkOrderType.d0_1v1_tzb.getType());

        //查看d0天第一次任务是谁处理的
        WorkOrderBase last = workOrderDao.getLastByCaseIdAndOrderTypes(base.getCaseId(),types);

        if (last == null || last.getOperatorId() == 0){
            return 0;
        }
        long operatorId = last.getOperatorId();

        //直接保存一下d0天第一次任务是谁处理的
        //后面外层会根据处理进行分组
        base.setOperatorId(operatorId);

        if (JuanzhuanWorkOrder.ext_type_tzb.equals(base.getOrderExtType()) && !tzbSet.contains(operatorId)){
            log.info("orderid={} 是特种兵  上次处理人{}不是特种兵",base.getId(),operatorId);
            return 0;
        }

        if (JuanzhuanWorkOrder.ext_type_common.equals(base.getOrderExtType()) && tzbSet.contains(operatorId)){
            log.info("orderid={} 不是特种兵  上次处理人{}是特种兵",base.getId(),operatorId);
            return 0;
        }

        List<Long> userId = Lists.newArrayList(operatorId);
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineToday(userId,base.getOrderType());

        if (CollectionUtils.isEmpty(staffList)){
            return 0;
        }

        StaffStatus staffStatus = staffList.get(0);

        int count = getTodayDoneByUserId(operatorId,getTodayOrder(Lists.newArrayList(operatorId),base.getOrderType()));

        if (staffStatus.getReceiptThreshold()<=count){
            return 0;
        }

        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(Lists.newArrayList(operatorId), base.getOrderType(), HandleResultEnum.doing.getType());

        if (CollectionUtils.isEmpty(list)){
            log.info("当前operatorId:{}无类型为:{}处理中的任务", operatorId, base.getOrderType());
            return operatorId;
        }
        //判断处理中是否超限
        WorkOrderDoingCount doingCount = list.get(0);
        if (doingCount.getNum() >= limitNum){
            return 0;
        }
        log.info("getlastOperatorId operatorId={} orderId={}",operatorId,base.getId());
        return operatorId;
    }


    private Long calCanAssignateStaff(List<StaffStatus> staffList, int orderType, int limitNum){

        if (CollectionUtils.isEmpty(staffList)){
            log.info("calCanAssignateStaff orderType={} StaffOnline=null",orderType);
            return 0L;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        //限制数量
        Map<Long,Integer> countMap = staffList.stream().collect(Collectors.toMap(StaffStatus::getUserId,StaffStatus::getReceiptThreshold,(o1,o2)->o2));
        //今天的总工单
        Map<Long,Long> todayOrders = getTodayOrder(users,orderType);

        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users,orderType, HandleResultEnum.doing.getType());

        //如果返回为空  直接分配给当前在线的人员
        if (CollectionUtils.isEmpty(list)){
            log.info("no doing user online user={}",users);
            //查找分配时间早的用户
            Optional<StaffStatus> op = staffList.stream()
                    .filter(r->getTodayDoneByUserId(r.getUserId(),todayOrders) < countMap.get(r.getUserId()))
                    .filter(r->r.getReceiptThreshold()>0).sorted(Comparator.comparing(StaffStatus::getAssignTime)).findFirst();
            if (op.isPresent()){
                return op.get().getUserId();
            }
            return 0L;
        }

        Map<Long,WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount :: getOperatorId, Function.identity()));

        //确定在线人员工单数量
        list = users.stream().map(r->{
            if (doingMap.get(r) != null){
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r,0);
        }).collect(Collectors.toList());

        //取出最少工单数量
        Optional<WorkOrderDoingCount> minCount =  list.stream()
                .filter(r->r.getNum() < limitNum)
                .filter(r->getTodayDoneByUserId(r.getOperatorId(),todayOrders) < countMap.get(r.getOperatorId()))
                .sorted(Comparator.comparing(WorkOrderDoingCount::getNum))
                .findFirst();

        //如果不存在不分配
        if (!minCount.isPresent()){
            return 0L;
        }
        //取出工单最少的人
        Map<Integer,List<WorkOrderDoingCount>> map = list.stream()
                .filter(r->r.getNum() < limitNum)
                .filter(r-> getTodayDoneByUserId(r.getOperatorId(),todayOrders) < countMap.get(r.getOperatorId()))
                .collect(Collectors.groupingBy(WorkOrderDoingCount :: getNum));

        List<WorkOrderDoingCount> minUsers = map.get(minCount.get().getNum());

        if (minUsers.size()==1){        //如果只有一个直接分配
            return minUsers.get(0).getOperatorId();
        }

        List<Long> userList = minUsers.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
        //查找分配时间早的用户
        return staffStatusService.getStaffOrderByAssignTime(userList,orderType);

    }


    private Map<Long,Long> getTodayOrder(List<Long> operatorIds,int orderType){

        List<Integer> results = HandleResultEnum.unDoResult();
        results.add(HandleResultEnum.done.getType());
        results.add(HandleResultEnum.exception_done.getType());
        String startTime = LocalDate.now()+" 00:00:00";
        String endTime = LocalDate.now().plusDays(1)+" 00:00:00";

        List<WorkOrderBase> list =  workOrderDao.getWorkordesByUser(operatorIds,orderType,results,startTime,endTime);

        return list.stream().collect(Collectors.groupingBy(WorkOrderBase::getOperatorId, Collectors.counting()));
    }

    private int getTodayDoneByUserId(long operatorId,Map<Long,Long> userMap){

       return Optional.ofNullable(userMap).filter(r->userMap.get(operatorId) != null)
               .map(r->userMap.get(operatorId)).map(Long::intValue).orElse(0);
    }

    private Set<Long> getTzbUsers(){
        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(WorkOrderType.d0_1v1_tzb.getPermission());
        //特种兵用
        Set<Long> tzbSet  = Optional.ofNullable(response).map(Response::getData).orElse(Lists.newArrayList()).stream().map(AuthUserDto::getUserId).collect(Collectors.toSet());

        return tzbSet;
    }
}
