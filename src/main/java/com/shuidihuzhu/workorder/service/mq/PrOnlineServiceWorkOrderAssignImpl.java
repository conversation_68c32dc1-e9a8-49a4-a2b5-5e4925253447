package com.shuidihuzhu.workorder.service.mq;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.custom.recruit.PrCommonAssigner;
import com.shuidihuzhu.workorder.service.custom.recruit.WorkOrderStaffConfService;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrResponsibleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/04/01 下午3:00
 */
@RefreshScope
@Slf4j
@Service("prOnlineServiceWorkOrderAssignImpl")
public class PrOnlineServiceWorkOrderAssignImpl extends PrCommonAssigner {

    private static final int maxLoad = 500;

    @Value("${pr.work.order.online.allow.assign:true}")
    private boolean allowAssign;

    @Resource
    private WorkOrderStaffConfService workOrderStaffConfService;
    @Resource
    protected AssignWorkOrderService assignWorkOrderService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    protected boolean isCycleFetch() {
        return true;
    }

    @Override
    public OpResult<List<? extends WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum,
                                                               WorkOrderBase lastOrder, int limit) {
        if (!allowAssign) {
            log.warn("Online work order stop assign!");
            return OpResult.createSucResult(List.of());
        }
        return OpResult.createSucResult(fetchPendingOnAssociated(orderType, resultEnum,
                OrderExtName.disease.getName(), null, limit, lastOrder));
    }

    @Override
    protected PrResponsibleVo selectResponsible(int orderType, WorkOrderBase workOrderBase) {
        return workOrderStaffConfService.assignForOnlineService(workOrderBase.getId(), workOrderBase.getCaseId(), orderType);
    }

}
