package com.shuidihuzhu.workorder.service.mq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeConstants;
import com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.pr.client.UserRoleClient;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/3/14 下午2:41
 * @desc
 */
@Slf4j
@Service("generalWorkOrderAssignateImpl")
public class GeneralWorkOrderAssignateImpl extends AssignateWorkOrderFacade {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private StaffStatusService staffStatusService;
    @Autowired
    private WorkTypePropertyService orderTypePropertyService;
    @Autowired
    private UserRoleClient userRoleClient;
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if(null == workOrderTypeService.getFromOrderTypeCode(orderType) || null == resultEnum){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),null, null);

        return OpResult.createSucResult(workOrderBases);
    }

    @Override
    public OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBases) {

        List<StaffStatus> staffStatuses = queryOnlineStaff(orderType);

        Long availableUser = calCanAssignateStaff(staffStatuses, orderType, obtainLimitCount(orderType));

        return OpResult.createSucResult(availableUser);
    }

    private int obtainLimitCount(int orderType) {
        int count = WorkOrderConfig.assignation_count;

        switch (orderType){
            case WorkOrderTypeConstants.PR_FIRST_SCREEN:
            case WorkOrderTypeConstants.PR_2LINE_FIRST_SCREEN_SERVICE:
            case WorkOrderTypeConstants.PR_SECOND_SCREEN:
            case WorkOrderTypeConstants.PR_SUPPLEMENT_MATERIAL:
            case WorkOrderTypeConstants.PR_ONLINE_SERVICE:
            case WorkOrderTypeConstants.PR_FIRST_SCREEN_REJECT_SERVICE:
            case WorkOrderTypeConstants.PR_SUPPLEMENT_MATERIAL_SERVICE:
            case WorkOrderTypeConstants.PR_RETURN_VISIT_SERVICE:
                count = orderTypePropertyService.getSecondMaxDelayCount(orderType, WorkTypeProperty.PropertyType.HANDLING_COUNT);
                break;
            default:
                break;
        }

        return count;
    }

    private Long calCanAssignateStaff(List<StaffStatus> staffList,int orderType,final int count){

        if (CollectionUtils.isEmpty(staffList)){
            log.info("calCanAssignateStaff orderType={} StaffOnline=null",orderType);
            return null;
        }

        //在线用户
        List<Long> users = staffList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(users)){
            return null;
        }
        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(users, orderType, HandleResultEnum.doing.getType());

        //如果返回为空  直接分配给当前在线的人员
        if (CollectionUtils.isEmpty(list)){
            log.info("no doing user online user={}",users);
            //查找分配时间早的用户
            return staffStatusService.getStaffOrderByAssignTime(users,orderType);
        }

        Map<Long,WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount :: getOperatorId, Function.identity()));

        //确定在线人员工单数量
        list = users.stream().map(r->{
            if (doingMap.get(r) != null){
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r,0);
        }).collect(Collectors.toList());

        //取出最少工单数量
        Optional<WorkOrderDoingCount> minCount =  list.stream().filter(r->r.getNum() < count).sorted(Comparator.comparing(WorkOrderDoingCount::getNum)).findFirst();

        //如果不存在不分配
        if (!minCount.isPresent()){
            return 0L;
        }
        //取出工单最少的人
        Map<Integer,List<WorkOrderDoingCount>> map = list.stream().filter(r->r.getNum() < count).collect(Collectors.groupingBy(WorkOrderDoingCount :: getNum));
        List<WorkOrderDoingCount> minUsers = map.get(minCount.get().getNum());


        if (minUsers.size()==1){        //如果只有一个直接分配
            return minUsers.get(0).getOperatorId();
        }

        List<Long> userList = minUsers.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
        //查找分配时间早的用户
        return staffStatusService.getStaffOrderByAssignTime(userList,orderType);

    }

    /**
     * 从候选员工中按照[处理中工单数->最早分配工单]的规则找到指定的一个
     * @param candidateUserIds 候选员工ids
     * @param orderType 工单类型
     * @param orderLimitCount 传入的最大处理中的工单数，< 0:表示无限制；如果为空，则使用工单系统的最大处理中工单限制数
     * @param staffHandlingLimitFun 员工-工单类型维度的处理中上限限制fun，当orderLimitCount有效时，该配置生效
     * @return null表示找不到符合条件的
     */
    public Long calCanAssignStaff(List<Long> candidateUserIds, int orderType, Integer orderLimitCount,
                                  BiFunction<Long, Integer, Integer> staffHandlingLimitFun) {
        if (CollectionUtils.isEmpty(candidateUserIds)){
            log.info("calCanAssignStaff workOrderType={} candidateUserIds=null",orderType);
            return null;
        }
        int count = orderLimitCount == null ? obtainLimitCount(orderType) : orderLimitCount;

        //查询在线用户处理中工单数量
        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(candidateUserIds, orderType, HandleResultEnum.doing.getType());
        //如果返回为空  直接分配给当前在线的人员
        if (CollectionUtils.isEmpty(list)){
            log.info("no doing user online user={}",candidateUserIds);
            //查找分配时间早的用户
            return staffStatusService.getStaffOrderByAssignTime(candidateUserIds, orderType);
        }

        Map<Long,WorkOrderDoingCount> doingMap = list.stream().collect(Collectors.toMap(WorkOrderDoingCount :: getOperatorId, Function.identity()));
        //确定在线人员工单数量
        list = candidateUserIds.stream().map(r->{
            if (doingMap.get(r) != null){
                return doingMap.get(r);
            }
            return new WorkOrderDoingCount(r,0);
        }).collect(Collectors.toList());

        LinkedHashMap<Integer, List<WorkOrderDoingCount>> numListMap = list.stream()
                .filter(r -> count < 0 || r.getNum() < Optional.ofNullable(staffHandlingLimitFun.apply(r.getOperatorId(), orderType)).orElse(count))
                .sorted(Comparator.comparing(WorkOrderDoingCount::getNum))
                .collect(Collectors.groupingBy(WorkOrderDoingCount::getNum, LinkedHashMap::new, Collectors.toList()));
        Optional<List<WorkOrderDoingCount>> minNumOption = numListMap.values().stream().findFirst();
        //如果不存在不分配
        if (minNumOption.isEmpty()){
            return null;
        }

        List<WorkOrderDoingCount> minUsers = minNumOption.get();
        if (minUsers.size() == 1){
            return minUsers.get(0).getOperatorId();
        }

        List<Long> userList = minUsers.stream().map(WorkOrderDoingCount::getOperatorId).collect(Collectors.toList());
        //查找分配时间早的用户
        return staffStatusService.getStaffOrderByAssignTime(userList,orderType);
    }

}
