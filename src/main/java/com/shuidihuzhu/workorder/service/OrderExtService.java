package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/4/18
 */
public interface OrderExtService {


    /**
     * value不为空才会保存
     * @param workOrderId
     * @param extName
     * @param extValue
     */
    void saveIfNotEmpty(long workOrderId, OrderExtName extName, String extValue);

    /**
     * 保存/更新ext信息
     */
    int replaceExt(long workOrderId, String extName, String extValue);

    int createWorkOrderExt(List<WorkOrderExt> workOrderExts);


    List<WorkOrderExt> getWorkOrderExts(List<Long> workOrderId, String extName);

    Map<Long, WorkOrderExt> getExtMap(List<Long> workOrderId, OrderExtName extName);

    List<WorkOrderExt> getWorkOrderExtList(List<Long> workOrderId, String extName, String extVaule);

    List<WorkOrderExt> getAllOrderExtIgnoreDelete(long workOrderId, List<String> extNames);

    List<WorkOrderExt> listOrderExtByIdsAndExtNames(List<Long> workOrderIds, List<String> extNames);

    WorkOrderExt getByOrderId(long workOrderId,OrderExtName extName);
}
