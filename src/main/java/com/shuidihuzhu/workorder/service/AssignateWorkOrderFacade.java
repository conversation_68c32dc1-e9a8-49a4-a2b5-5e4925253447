package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.assignate.DefaultAssignService;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/3/14 下午2:35
 * @desc
 */
@Slf4j
public abstract class AssignateWorkOrderFacade implements IAssignService {
    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    protected AssignWorkOrderService assignWorkOrderService;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private StaffStatusService staffStatusService;
    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private DefaultAssignService defaultAssignService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    public OpResult validate(int orderType) {
        return OpResult.createSucResult();
    }

    /**
     * 是否循环load取待分配数据，true时需要实现getAssignateWorkOrder含limit参数的方法
     */
    protected boolean isCycleFetch(){return false;}

    /**
     * 按照创建时间循环fetch待处理工单
     * <br/>** 注意 仅适用工单level相同的工单，否则无法正常fetch结果
     */
    public OpResult<List<? extends WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum,
                                                                         WorkOrderBase lastOrder, int limit) {
        throw new UnsupportedOperationException();
    }

    public OpResult<List<WorkOrderBase>> getAssignateWorkOrder(int orderType, HandleResultEnum resultEnum) {
        if(null == resultEnum){
            return OpResult.createSucResult(Lists.newArrayList());
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, resultEnum.getType(),null, null);

        return OpResult.createSucResult(workOrderBases);
    }

//    public OpResult<Long> getAssignateStaff(WorkOrderType workOrderType, WorkOrderBase workOrderBase) {
//        return OpResult.createSucResult(defaultAssignService.getAssignOperator(workOrderType, workOrderBase));
//    }

    public abstract OpResult<Long> getAssignateStaff(int orderType, WorkOrderBase workOrderBase);

    @Override
    public Response<Void> doAssign(int orderType) {
        OpResult opResult = doAssignate(orderType);
        return opResult.isSuccess() ?
                NewResponseUtil.makeSuccess(null) :
                NewResponseUtil.makeFail(opResult.getErrorCode().getCode(), opResult.getMessage(), null);
    }

    public OpResult doAssignate(int orderType){

        OpResult opResult = validate(orderType);
        if(null == opResult || !opResult.isSuccess()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String lockName = getLockKey(orderType);
        RLock rLock = null;

        try {
            meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,
                    OperationStat.OPERATION,OperationStat.assignate,
                    OperationStat.ORDERTYPE,orderType+"",
                    OperationStat.ONE_TYPE, workOrderTypeService.getOneFromTwo(orderType)+"").increment();

            rLock = cf2RedissonHandler.getLock(lockName);
            if (!rLock.tryLock()){
                return OpResult.createFailResult(ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
            }
            StopWatch stopWatch = StopWatch.createStarted();
            log.info("AssignateWorkOrderFacade assign start");
            long count = 0;
            int fetchSize = 500;
            if (isCycleFetch()) {
                WorkOrderBase lastOrder = null;
                List<? extends WorkOrderBase> workOrderBases;
                do {
                    OpResult<List<? extends WorkOrderBase>> workOrderOpResult = getAssignateWorkOrder(orderType, HandleResultEnum.undoing,
                            lastOrder, fetchSize);
                    if(null == workOrderOpResult || !workOrderOpResult.isSuccess() || CollectionUtils.isEmpty(workOrderOpResult.getData())){
                        return OpResult.createFailResult(ErrorCode.NOT_MORE_WORK_ORDER);
                    }
                    workOrderBases = workOrderOpResult.getData();
                    count += workOrderBases.size();
                    if (count > 10000) {
                        log.error("疑似死循环:"+count);
                    }
                    OpResult<?> tmpResult = actualAssign(orderType, workOrderOpResult.getData());
                    if (tmpResult != null) {
                        return tmpResult;
                    }

                    lastOrder = workOrderBases.get(workOrderBases.size() - 1);
                } while (workOrderBases.size() >= fetchSize);
            } else {
                OpResult<List<WorkOrderBase>> workOrderOpResult = getAssignateWorkOrder(orderType, HandleResultEnum.undoing);
                if(null == workOrderOpResult || !workOrderOpResult.isSuccess() || CollectionUtils.isEmpty(workOrderOpResult.getData())){
                    return OpResult.createFailResult(ErrorCode.NOT_MORE_WORK_ORDER);
                }
                OpResult tmpResult = actualAssign(orderType, workOrderOpResult.getData());
                if (tmpResult != null) {
                    return tmpResult;
                }
            }
            stopWatch.stop();
            log.info("AssignateWorkOrderFacade assign end time type {}, time {}", orderType, stopWatch.getTime());
            return OpResult.createSucResult();
        } catch (Exception e){
            log.error("assignate workorder exeception.", e);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ERROR);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()){
                rLock.unlock();
            }
        }
    }

    private OpResult actualAssign(int orderType, List<? extends WorkOrderBase> workOrderBases) {
        for (WorkOrderBase workOrder : workOrderBases){
            OpResult<Long> staffsOpResult = getAssignateStaff(orderType, workOrder);
            Long operatorId = null != staffsOpResult ? staffsOpResult.getData() : null;

            log.info("doAssignate workorder type={} getAssignateStaff:{}.", orderType, operatorId);
            // 质检工单中 科室质检需要自动分单 临时修改 需要重构
            if (WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType)
                    && orderType != WorkOrderType.qc_hospital_dept.getType()
                    && orderType != WorkOrderType.qc_zhu_dong.getType()){
                continue;
            }

            if (WorkOrderType.cailiao_fuwu.getType() == orderType && operatorId != null &&operatorId == 0) {
                log.info("cailiao_fuwu  continue  orderid={}",workOrder.getId());
                continue;
            }

            if (Optional.ofNullable(operatorId).filter(o -> o > 0).isEmpty() && WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType)) {
                log.info("pr work order continue order id:{}",workOrder.getId());
                continue;
            }

            if(null == staffsOpResult || !staffsOpResult.isSuccess() || null == operatorId || 0 == operatorId){
                return OpResult.createFailResult(ErrorCode.NOT_MORE_STAFF_ASSIGNATE);
            }

            assignWorkOrderService.assignWorkOrder(workOrder, operatorId);
            log.info("doAssignate workorder id={} success.type={},operatorId={}",workOrder.getId(), orderType,operatorId);
        }

        return null;
    }


    protected List<StaffStatus> queryOnlineStaff(int orderType) {

        List<Long> userIds = getHasPermissionUserIds(orderType);
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }
        //查询在线用户
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineToday(userIds, orderType);

        return staffList;
    }

//    public List<StaffStatus> queryOnlineAndStopStaff(WorkOrderType workOrderType) {
//
//        List<Long> userIds = getHasPermissionUserIds(0, workOrderType);
//        if (userIds == null) {
//            return null;
//        }
//        //查询在线用户
//        List<StaffStatus> staffList = staffStatusService.getStaffOnlineAndStopToday(userIds,workOrderType.getType());
//
//        return staffList;
//    }


    private List<Long> getHasPermissionUserIds(int orderType) {
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if (workOrderTypeDO == null){
            log.info("queryOnlineStaff orderType={} workOrderTypeDO=null", orderType);
            return null;
        }
        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(workOrderTypeDO.getPermission());
        if (response == null || response.notOk()) {
            log.info("queryOnlineStaff workOrderTypeDO={} rpcRespons={}", workOrderTypeDO, response);
            return null;
        }
        List<AuthUserDto> userAccountModelList = response.getData();

        if (CollectionUtils.isEmpty(userAccountModelList)) {
            log.info("queryOnlineStaff workOrderTypeDO={} userAccountModelList=null", workOrderTypeDO);
            return null;
        }

        List<Long> userIds = userAccountModelList.stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
        return userIds;
    }


    protected final String getLockKey(int orderType){
        return "Assignate_Work_Order_" + orderType;
    }

}
