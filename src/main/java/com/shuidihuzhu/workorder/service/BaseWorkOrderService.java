package com.shuidihuzhu.workorder.service;


import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.order.OrderUserHandleResultCount;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
public interface BaseWorkOrderService {

    int getCountByHandleResult(long userId, int orderType, String handleResult);


    int getAllCountByHandleResult(int orderType, int handleResult,long userId);

    /**
     * 释放领取的全部工单后尝试分配工单
     *
     * @param userId
     */
    void freeWorkOrder(long userId, int orderType);

    void freeWorkOrderWithTime(long userId, int orderType, Date startTime, Date endTime);

    int freeWorkOrder(List<Long> workOrderIds);

    int freeWorkOrderWithNoCheckOldStatus(List<Long> workOrderIds);


    int callbackOrder(List<Long> workOrderIds);

    /**
     * @param orderType
     * @param results
     * @param time
     * @return
     */
    int getOrderCount(List<Integer> orderType, List<Integer> results, String time);

    /**
     * 查询工单
     *
     * @param workId
     * @return
     */
    WorkOrderVO getWorkOrderById(long workId);


    /**
     * 查询时间之后的指定工单
     *
     * @param caseId
     * @param orderType
     * @param time
     * @return
     */
    WorkOrderVO getOrderByTypeAndTime(int caseId, int orderType, String time);


    /**
     * 查询工单列表
     *
     * @param queryListParam
     * @return
     */
    OpResult<PageResult<QueryListResult>> getWorkOrderList(QueryListParam queryListParam);

    /**
     * 查询工单列表
     *
     * @param queryListParam
     * @return
     */
    Integer getWorkOrderListCount(QueryListParam queryListParam);

    /**
     * 获取渠道
     * @param caseId
     * @return
     */
    Map<Integer, ChannelRefine.ChannelRefineResuleEnum> getChannel(int caseId);

    boolean checkCanReprocess(WorkOrderBase workOrder);

    boolean checkCanReprocess(long workOrderId);

    /**
     * 获取渠道
     *
     * @param caseList
     * @return
     */
    Map<Integer, ChannelRefine.ChannelRefineResuleEnum> getChannel(List<CrowdfundingInfo> caseList);


    /**
     * 修改优先级
     *
     * @param workOrderId
     * @param orderLevel
     * @param operatorId
     * @param userComment
     * @return
     */
    OpResult changeOrderLevel(long workOrderId, int orderLevel, long operatorId, String userComment);


    /**
     * 根据案例id和类型关闭最新工单
     * @param caseId
     * @param orderType
     * @param HandleResult
     * @param operatorId
     * @param comment
     * @return
     */
    int closeOrderBycaseIdAndType(int caseId,int orderType,int HandleResult,long operatorId,String comment);

    void closeOrderBycaseIdAndTypeWithoutHandleTime(int caseId,int orderType,int HandleResult,long operatorId,String comment);

    List<WorkOrderBase> listByCaseId(int caseId, int orderType);

    WorkOrderVO getLastByCaseIdAndOrderType(int caseId, int orderType);

    WorkOrderVO getLastByCaseIdAndOrderTypes(int caseId, List<Integer> orderTypes);

    List<WorkOrderVO> queryByCaseAndTypes(int caseId, List<Integer> orderTypes);

    List<WorkOrderVO> listByCaseIdsAndTypes(List<Integer> caseIds, List<Integer> orderTypes);

    List<WorkOrderVO> ListByCaseIdAndTypeAndResult(int caseId, List<Integer> orderTypes, List<Integer> results);

    List<Integer> listCaseIdsByTypeAndCount(List<Integer> caseIds, Integer orderTypes, Integer count);

    boolean closeOrderByWorkOrderId(long workOrderId, int handleResult, long operatorId, String comment);

    boolean closeOrderByWorkOrderIds(List<Long> workOrderIds, int handleResult, long operatorId, String comment);

    /**
     * 后门生成工单
     * @param workOrderBase
     * @return
     */
    boolean createWorkOrderEnforce(WorkOrderBase workOrderBase, int operatorId);


    int reportTransfer(List<Long> ids, int orderType, int operatorId, int recipientId);

    /**
     * 个人工单数据信息,比如待分配数量、稍后处理数量
     */
    List<StaffRealTimeWorkData> listStaffRealTimeWorkData(long userId);

    List<StaffRealTimeWorkData> listUnHandleWork(long userId, List<Integer> orderTypes);


    int qcTransfer(List<Long> ids, int orderType, long operatorId, long targetUserId);

    /**
     *  查询符合条件的质检工单
     */
    List<WorkOrderBase> queryListByBatch(long id, int handleResult, String endTime, int orderType, int limit);


    /**
     * 关闭质检工单
     */
    int closeQcOrderList(List<Long> workIds, int handleResult);


    /**
     * 重新打开关闭的质检工单
     */
    int openAgainQcWorkOrder(long workOrderId, long operatorId, String comment);


    int deleteWorkOrderByIdAndOrderType(long id);

    /**
     *  质检工单重新质检
     */
    int changeQcWorkOrderStatus(long workOrderId, long operatorId);

    /**
     * 批量分配
     *
     * @param ids
     * @param orderType
     * @param operatorId
     * @param recipientId
     * @return
     */
    int batchAllocation(List<Long> ids, int orderType, int operatorId, int recipientId);

    /**
     * 根据id查询工单原始信息(不过滤is_delete=1的情况)
     * @param workOrderIds work order id
     * @return RawWorkOrder
     */
    List<RawWorkOrder> getRawWorkOrder(List<Long> workOrderIds);

    /**
     * 查询指定时间到现在，是否存在指定扩展字段的工单
     * @param caseId
     * @param orderType
     * @param startTime
     * @param extName
     * @param extVal
     * @return true exist, else false
     */
    List<WorkOrderBase> existExtValue(int caseId, int orderType, Date startTime, String extName, String extVal);

    Response<List<QueryListResult>> getListByOrderIdListOld(List<Long> workOrderIdList);

    List<Long> getIdsByTypeResultTime(List<Integer> orderTypes, List<Integer> results, Date createTime);

    List<Long> getIdsByTypeResultBetweenTime(List<Integer> orderTypes, List<Integer> results, Date createTime, Date endTime);

    List<OrderUserHandleResultCount> getCountByUserIdAndHandleResult(int orderType, long userId, List<Integer> handleResultList);
}
