package com.shuidihuzhu.workorder.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.impl.WorkOrderTypeServiceImpl;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: fengxuan
 * @create 2020-01-07 11:52
 **/
@RefreshScope
@Service
@Slf4j
public class StaffAssignPlugins {

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Resource
    private WorkOrderTypeServiceImpl workOrderTypeRecordService;

    public OpResult<Long> getStaffByPlugin(int orderType, WorkOrderBase workOrderBase) {
        Map<Long, Long> cfUserTAdminUserId = parseUserId();
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeRecordService.getFromOrderTypeCode(orderType);
        if (workOrderTypeDO == null) {
            log.warn("orderType 未找到对应工单类型,orderType:{}", orderType);
            return OpResult.createFailResult(ErrorCode.ASSIGN_PLUGIN_NO_MATCH);
        }
        log.info("特殊分配规则");
        //find case creator
        FeignResponse<CrowdfundingInfo> caseInfoResponse = client.getCaseInfoById(workOrderBase.getCaseId());
        //if creator in customizeAssign return creator
        if (caseInfoResponse.ok()) {
            long userId = caseInfoResponse.getData().getUserId();
            Long adminUserId = cfUserTAdminUserId.getOrDefault(userId, -1L);
            //校验是否有权限
            Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(workOrderTypeDO.getPermission());
            boolean match = Optional.ofNullable(response.getData()).orElse(Lists.newArrayList()).stream().anyMatch(item -> Objects.equals(Long.valueOf(item.getUserId()), adminUserId));
            log.debug("case match special assign to cf userId:{}, admin userId:{}, match result:{}", userId, adminUserId, match);
            if (adminUserId > 0 && match) {
                return OpResult.createSucResult((long) adminUserId);
            }
        }
        return OpResult.createFailResult(ErrorCode.ASSIGN_PLUGIN_NO_MATCH);
    }


    /**
     * cfUserId:adminUserId
     */
    private Map<Long, Long> parseUserId() {
        final String customizeAssign = ConfigService.getAppConfig().getProperty("work.order.customize.assign", "");
        Map<Long, Long> userIdMap = Maps.newHashMap();
        try {
            userIdMap = JSON.parseObject(customizeAssign, new TypeReference<Map<Long, Long>>() {
            });
            log.debug("userIdMap:{}", userIdMap);
        } catch (Exception e) {
            log.error("apollo配置信息格式错误,customizeAssign:{}", customizeAssign, e);
        }
        return userIdMap;
    }




}
