package com.shuidihuzhu.workorder.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderReportStatisticsVo;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDataStatisticsDao;
import com.shuidihuzhu.workorder.dao.WorkOrderReportStatisticsDao;
import com.shuidihuzhu.workorder.elastic.service.impl.SearchDelegate;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.WorkOrderDataStatistics;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.model.order.WorkOrderReportStatistics;
import com.shuidihuzhu.workorder.model.order.WorkOrderStatisticalCount;
import com.shuidihuzhu.workorder.service.mq.kafka.model.BinLog;
import com.shuidihuzhu.workorder.service.mq.kafka.model.BinLogOperator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/3
 */
@Slf4j
@Service
public class WorkOrderDataStatisticsService {

    private static final DecimalFormat df = new DecimalFormat("0.00");

    @Resource
    private WorkOrderDataStatisticsDao workOrderDataStatisticsDao;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private SearchDelegate searchDelegate;
    @Autowired
    private WorkOrderReportStatisticsDao workOrderReportStatisticsDao;
    @Autowired(required = false)
    private Producer producer;
    @Resource
    WorkOrderTypeService workOrderTypeService;

    private static final List<Integer> HANDLE_FINISH_STATUS = Lists.newArrayList();

    List<Integer> CAILIAO_FUWU_FINISH_STATUS = Lists.newArrayList(HandleResultEnum.manual_lock.getType(),
            HandleResultEnum.exception_done.getType(), HandleResultEnum.handle_manual_lock.getType());

    private static final String HANDLE_RESULT = "handle_result";
    private static final String OPERATOR_ID = "operator_id";
    private static final String ORDER_TYPE = "order_type";
    private static final String CREATE_TIME = "create_time";
    private static final String UPDATE_TIME = "update_time";
    private static final String HANDLE_TIME = "handle_time";

    static {
        HANDLE_FINISH_STATUS.add(HandleResultEnum.audit_pass.getType());
        HANDLE_FINISH_STATUS.add(HandleResultEnum.audit_reject.getType());
        HANDLE_FINISH_STATUS.add(HandleResultEnum.stop_case.getType());
        HANDLE_FINISH_STATUS.add(HandleResultEnum.done.getType());
        HANDLE_FINISH_STATUS.add(HandleResultEnum.manual_lock.getType());
        HANDLE_FINISH_STATUS.add(HandleResultEnum.exception_done.getType());
    }

    public void handleExtInfo(List<WorkOrderExt> workOrderExts) {
        if (CollectionUtils.isEmpty(workOrderExts)) {
            return;
        }

        workOrderExts.forEach(x -> handleExtInfo(x.getWorkOrderId(), x.getExtName(), x.getExtValue()));
    }

    public void handleExtInfo(long workOrderId, String extName, String extValue) {
        if (workOrderId <= 0 || !StringUtils.equalsAnyIgnoreCase(extName, OrderExtName.callUnicode.getName(), OrderExtName.CallStatus.getName())) {
            return;
        }

        try {
            WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workOrderId);
            if (workOrderBase == null || workOrderBase.getOperatorId() <= 0) {
                return;
            }
            Date now = new Date();
            int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(now));
            int hour = now.getHours();

            if (StringUtils.equalsIgnoreCase(OrderExtName.callUnicode.getName(), extName)) {
                workOrderDataStatisticsDao.updateCallCount(statDay, hour, workOrderBase.getOrderType(), workOrderBase.getOperatorId());
                //处理举报工单通话次数与时长
                if (WorkOrderType.REPORT_TYPES.contains(workOrderBase.getOrderType())) {
                    this.updateReportCallCount(workOrderId, extValue, workOrderBase, statDay, hour);
                }
            } else if (StringUtils.equalsIgnoreCase(OrderExtName.CallStatus.getName(), extName)) {
                if (StringUtils.equals(extValue, "1")) {
                    workOrderDataStatisticsDao.updateEffectiveCallCount(statDay, hour, workOrderBase.getOrderType(), workOrderBase.getOperatorId());
                }
            }
        } catch (Exception e) {
            log.error("handleCallStatus error. workOrderId:{}, extName:{}", workOrderId, extName);
        }
    }

    private void updateReportCallCount(long workOrderId, String extValue, WorkOrderBase workOrderBase, int statDay, int hour) {
        workOrderReportStatisticsDao.updateCallCount(statDay, hour, workOrderBase.getOrderType(), workOrderBase.getOperatorId());
        //发送查询通话时间mq
        Map<String, Object> payload = Maps.newHashMap();
        payload.put("workOrderId", workOrderId);
        payload.put("unique", extValue);
        payload.put("statDay", statDay);
        payload.put("hour", hour);
        MessageResult messageResult = producer.send(new Message<>(MQTopicCons.CF, MQTag.GET_CALL_TOTAL_DURATION,
                MQTag.GET_CALL_TOTAL_DURATION + "_" + workOrderId, payload, DelayLevel.M7));
        log.info("updateReportCallCount messageResult:{}", JSON.toJSONString(messageResult));
    }


    public void handleBinLog(BinLog binLog) {

        Date updateTime = getColumnValueDate(UPDATE_TIME, binLog.getAfter());
        Date handleTime = getColumnValueDate(HANDLE_TIME, binLog.getAfter());

        int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(updateTime));
        int hour = updateTime.getHours();
        int workOrderType = getColumnValueInt(ORDER_TYPE, binLog.getAfter());

        handleOperator(binLog, statDay, hour, workOrderType);

        handleStatus(binLog, statDay, hour, workOrderType, updateTime, handleTime);

        handleWorkOrderReportStatistics(binLog, statDay, hour, workOrderType);
    }

    private void handleWorkOrderReportStatistics(BinLog binLog, int statDay, int statHour, int orderType) {
        Optional<String> optional = isColumnUpdate(HANDLE_RESULT, binLog.getAfter());
        if (optional.isEmpty() || !StringUtils.isNumeric(optional.get())) {
            return;
        }

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(Integer.parseInt(optional.get()));
        if (handleResultEnum == null) {
            log.error("handleWorkOrderReportStatistics handleResultEnum is null. handleResult:{}", optional.get());
            return;
        }
        long operatorId = getColumnValueLong(OPERATOR_ID, binLog.getAfter());

        switch (handleResultEnum) {
            case noneed_deal:
                workOrderReportStatisticsDao.updateNoneedDealCount(statDay, statHour, orderType, operatorId);
                workOrderReportStatisticsDao.updateDoneCount(statDay, statHour, orderType, operatorId);
                break;
            case end_deal:
                workOrderReportStatisticsDao.updateEndDealCount(statDay, statHour, orderType, operatorId);
                workOrderReportStatisticsDao.updateDoneCount(statDay, statHour, orderType, operatorId);
                break;
            case end_deal_upgrade:
                workOrderReportStatisticsDao.updateUpgradeCount(statDay, statHour, orderType, operatorId);
                workOrderReportStatisticsDao.updateDoneCount(statDay, statHour, orderType, operatorId);
                break;
            case end_deal_lost:
                workOrderReportStatisticsDao.updateLostCount(statDay, statHour, orderType, operatorId);
                workOrderReportStatisticsDao.updateDoneCount(statDay, statHour, orderType, operatorId);
                break;
            default:
        }
    }

    private void handleStatus(BinLog binLog, int statDay, int statHour, int orderType, Date updateTime, Date handleTime) {
        Optional<String> optional = isColumnUpdate(HANDLE_RESULT, binLog.getAfter());
        if (optional.isEmpty() || !StringUtils.isNumeric(optional.get())) {
            return;
        }

        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(Integer.parseInt(optional.get()));
        if (handleResultEnum == null) {
            log.error("handleResultEnum is null. handleResult:{}", optional.get());
            return;
        }

        long operatorId = getColumnValueLong(OPERATOR_ID, binLog.getAfter());

        switch (handleResultEnum) {
            case manual_lock:
                workOrderDataStatisticsDao.updateManualLockCount(statDay, statHour, orderType, operatorId);
                break;
            case exception_done:
                operatorId = getColumnValueLong(OPERATOR_ID, binLog.getBefore());
                if (operatorId > 0) {
                    workOrderDataStatisticsDao.updateExceptionDoneCount(statDay, statHour, orderType, operatorId);
                }
                break;
            case stop_case:
                workOrderDataStatisticsDao.updateStopCaseCount(statDay, statHour, orderType, operatorId);
                break;
            case audit_pass:
                workOrderDataStatisticsDao.updateAuditPassCount(statDay, statHour, orderType, operatorId);
                break;
            case audit_reject:
                workOrderDataStatisticsDao.updateAuditRejectCount(statDay, statHour, orderType, operatorId);
                break;
            case done:
                workOrderDataStatisticsDao.updateDoneCount(statDay, statHour, orderType, operatorId);
                break;
            case handle_manual_lock:
                workOrderDataStatisticsDao.updateHandleManualLockCount(statDay, statHour, orderType, operatorId);
                break;
            default:
                break;
        }

        // 单独处理结束
        handleFinish(statDay, statHour, orderType, operatorId, handleResultEnum, updateTime, handleTime);
    }

    private void handleFinish(int statDay, int statHour, int orderType, long operatorId, HandleResultEnum handleResultEnum, Date updateTime, Date handleTime) {

        if (orderType == WorkOrderType.cailiao_fuwu.getType()) {

            if (!CAILIAO_FUWU_FINISH_STATUS.contains(handleResultEnum.getType())) {
                return;
            }

        } else if (!HANDLE_FINISH_STATUS.contains(handleResultEnum.getType())) {
            return;
        }

        if (operatorId <= 0) {
            return;
        }

        long timeSpan = updateTime.getTime() - handleTime.getTime();

        boolean outsideThreeMinutes = timeSpan > TimeUnit.MINUTES.toMillis(3);
        if (outsideThreeMinutes) {
            workOrderDataStatisticsDao.updateOutsideThreeMinutesCount(statDay, statHour, orderType, operatorId);
        }else {
            workOrderDataStatisticsDao.updateWithinThreeMinutesCount(statDay, statHour, orderType, operatorId);
        }

        boolean outsideTenMinutes = timeSpan > TimeUnit.MINUTES.toMillis(10);
        if (outsideTenMinutes) {
            workOrderDataStatisticsDao.updateOutsideTenMinutesCount(statDay, statHour, orderType, operatorId);
        } else {
            workOrderDataStatisticsDao.updateWithinTenMinutesCount(statDay, statHour, orderType, operatorId);
        }
    }

    private void handleOperator(BinLog binLog, int statDay, int statHour, int orderType) {
        Optional<String> optional = isColumnUpdate(OPERATOR_ID, binLog.getAfter());
        if (optional.isEmpty() || !StringUtils.isNumeric(optional.get())) {
            return;
        }
        long operatorId = Long.parseLong(optional.get());
        if (operatorId <= 0) {
            return;
        }

        workOrderDataStatisticsDao.updateAssignCount(statDay, statHour, orderType, operatorId);

        if (WorkOrderType.REPORT_TYPES.contains(orderType)){
            workOrderReportStatisticsDao.updateAssignCount(statDay, statHour, orderType, operatorId);
        }
    }

    private Optional<String> isColumnUpdate(String columnName, List<BinLogOperator> operators) {
        if (StringUtils.isBlank(columnName) || CollectionUtils.isEmpty(operators)) {
            return Optional.empty();
        }

        Optional<BinLogOperator> binLogOperator = operators.stream().filter(x -> StringUtils.equalsAnyIgnoreCase(columnName, x.getName()) && x.isUpdate()).findFirst();
        return binLogOperator.map(BinLogOperator::getValue);
    }

    private Date getColumnValueDate(String columnName, List<BinLogOperator> operatorList) {
        String value = getColumnValue(columnName, operatorList);
        return DateUtil.getStr2LDate(value);
    }

    private long getColumnValueLong(String columnName, List<BinLogOperator> operatorList) {
        String value = getColumnValue(columnName, operatorList);
        return Long.parseLong(value);
    }

    private int getColumnValueInt(String columnName, List<BinLogOperator> operatorList) {
        String value = getColumnValue(columnName, operatorList);
        return Integer.parseInt(value);
    }

    private String getColumnValue(String columnName, List<BinLogOperator> operatorList) {
        return operatorList.stream().filter(x -> StringUtils.equalsAnyIgnoreCase(columnName, x.getName())).findFirst().get().getValue();
    }

    public List<WorkOrderDataStatisticsView> getOrgDataStat(List<Integer> orgIds,  List<Integer> orderTypes) {

        //组维度记录，一个组一条记录
        List<WorkOrderDataStatisticsView> viewsStatByOrg = Lists.newArrayList();

        orgIds.parallelStream().forEach(orgId-> {
            List<WorkOrderDataStatisticsView> viewsStatByOrderType = getDataDetailStatByOrderType(orgId, 0, orderTypes);
            WorkOrderDataStatisticsView view = viewsToView(viewsStatByOrderType);
            view.setOrgId(orgId);
            view.setOrgName(organizationService.getOrgName(orgId));
            view.setOnlineUserCount(staffStatusService.getDistinctUserOnlineCount(orderTypes, organizationService.getAllUsersByOrgId(orgId)));
            viewsStatByOrg.add(view);
        });

        fillDataStatRate(viewsStatByOrg);
        return viewsStatByOrg;
    }

    public List<WorkOrderDataStatisticsView> getUserDataStat(List<Long> userIds, List<Integer> orderTypes) {

        //人维度记录，一个人一条记录
        List<WorkOrderDataStatisticsView> viewsStatByUser = Collections.synchronizedList(Lists.newArrayList());
        userIds.parallelStream().forEach(searchUserId -> {
            List<WorkOrderDataStatisticsView> viewsStatByOrderType = getDataDetailStatByOrderType(0, searchUserId, orderTypes);
            WorkOrderDataStatisticsView view = viewsToView(viewsStatByOrderType);
            view.setOperatorId(searchUserId);
            view.setOperatorName(organizationService.getUserName(searchUserId));
//            view.setOrgId(organizationService.getUserOrgId(searchUserId));
            view.setOrgName(organizationService.getSimpleOrganization(Math.toIntExact(searchUserId)));
            viewsStatByUser.add(view);
        });

        fillDataStatRate(viewsStatByUser);
        return viewsStatByUser;
    }

    /**
     * 查询单个组织或者个人的按照工单类型维度聚合的明细信息
     * @param orgId
     * @param userId
     * @param orderTypes
     * @return
     */
    public List<WorkOrderDataStatisticsView> getDataDetailStatByOrderType(int orgId, long userId, List<Integer> orderTypes){
        Date now = new Date();
        int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(now));

        final List<Long> userIds = Lists.newArrayList();
        if (orgId > 0) {
            userIds.addAll(organizationService.getAllUsersByOrgId(orgId));
        } else if (userId > 0) {
            userIds.add(userId);
        }

        List<WorkOrderDataStatisticsView> views =  getDataDetailStatByOrderType(statDay, userIds, orderTypes);

        fillDataStatRate(views);

        if (orgId > 0) {
//            String orgName = organizationService.getOrgName(orgId);
            views.forEach(view -> {
                view.setOrgId(orgId);
//                view.setOrgName(orgName);
                view.setOnlineUserCount(staffStatusService.getDistinctUserOnlineCount(Lists.newArrayList(view.getOrderType()), userIds));
            });
        }

        return views;
    }

    private List<WorkOrderDataStatisticsView> getDataDetailStatByOrderType(int statDay, List<Long> userIds, List<Integer> orderTypes) {
        if (CollectionUtils.isEmpty(orderTypes)) {
            return Lists.newArrayList();
        }

        List<WorkOrderDataStatisticsView> views = Lists.newArrayList();
        orderTypes.parallelStream().forEach(x -> views.add(getDataDetailStatByOrderType(statDay, userIds, x)));
        return views;
    }

    private WorkOrderDataStatisticsView getDataDetailStatByOrderType(int statDay, List<Long> userIds, int orderType) {

        WorkOrderDataStatistics statistics = null;
        if (CollectionUtils.isNotEmpty(userIds)) {
            statistics = workOrderDataStatisticsDao.getSumByUserIdsAndOrderType(statDay, orderType, userIds);
        }

        WorkOrderDataStatisticsView view = convert(statistics);
        view.setOrderType(orderType);
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        view.setOrderTypeName(workOrderTypeDO != null ? workOrderTypeDO.getMsg() : "");
        view.setLaterDoingCount(Math.toIntExact(searchDelegate.searchWorkOrderCount(userIds, orderType, HandleResultEnum.later_doing.getType())));
        view.setDoingCount(Math.toIntExact(searchDelegate.searchWorkOrderCount(userIds, orderType, HandleResultEnum.doing.getType())));

        if (orderType == WorkOrderType.cailiao_fuwu.getType() && CollectionUtils.isNotEmpty(userIds)) {
            WorkOrderTableParam woTableParam = new WorkOrderTableParam();
            woTableParam.setOperatorIds(userIds);
            woTableParam.setOrderTypes(Lists.newArrayList(orderType));
            woTableParam.setHandleResult(Lists.newArrayList(HandleResultEnum.doing.getType()));
            woTableParam.setCreateStartTime(LocalDate.of(2019, 5, 1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            woTableParam.setCreateEndTime(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH).getTime());

            view.setHistoricalDoingCount(Math.toIntExact(searchDelegate.searchWorkOrderCount(woTableParam)));

            woTableParam.setHandleResult(CAILIAO_FUWU_FINISH_STATUS);
            woTableParam.setUpdateStartTime(DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH).getTime());
            woTableParam.setUpdateEndTime(System.currentTimeMillis());
            view.setHistoricalFinishCount(Math.toIntExact(searchDelegate.searchWorkOrderCount(woTableParam)));
        }

        //只有一个人的时候下面的赋值才有意义
        if (userIds.size() == 1) {
            long userId = userIds.get(0);
            StaffStatus staffStatus = staffStatusService.getStaffStatus(userId, orderType);
            view.setOperatorStatus(staffStatus != null ? staffStatus.getStaffStatus() : 0);
            view.setOperatorId(userId);
//            view.setOrgId(organizationService.getUserOrgId(userId));
            if (staffStatus != null && staffStatus.getOnJob() != 1) {
                view.setPauseDuration(staffStatusService.calcTodayPauseDuration(staffStatus, userId, orderType));
            }
        }
        view.setFinishCount(countFinishCount(view));
        return view;
    }

    private void fillDataStatRate(List<WorkOrderDataStatisticsView> views) {
        if (CollectionUtils.isEmpty(views)) {
            return;
        }
        views.forEach(view -> {
            int finishCount = view.getFinishCount();
//            if (view.getOrderType() == WorkOrderType.cailiao_fuwu.getType()) {
//                finishCount = view.getManualLockCount() +
//                        view.getExceptionDoneCount() +
//                        view.getHandleManualLockCount();
//            } else{
//                finishCount = view.getManualLockCount() +
//                        view.getExceptionDoneCount() +
//                        view.getDoneCount() +
//                        view.getStopCaseCount() +
//                        view.getAuditRejectCount() +
//                        view.getAuditPassCount();
//            }
//
//            view.setFinishCount(finishCount);
            if (finishCount <= 0) {
                return;
            }

            if (view.getWithinThreeMinutesCount() > 0) {
                view.setWithinThreeMinutesRate(String.format("%.2f%%", view.getWithinThreeMinutesCount() * 100.0 / finishCount));
            }

            if (view.getWithinTenMinutesCount() > 0) {
                view.setWithinTenMinutesRate(String.format("%.2f%%", view.getWithinTenMinutesCount() * 100.0 / finishCount));
            }

            if (view.getAuditPassCount() > 0) {
                view.setAuditPassRate(String.format("%.2f%%", view.getAuditPassCount() * 100.0 / finishCount));
            }

            if (view.getAuditRejectCount() > 0) {
                view.setAuditRejectRate(String.format("%.2f%%", view.getAuditRejectCount() * 100.0 / finishCount));
            }

            if (view.getStopCaseCount() > 0) {
                view.setStopCaseRate(String.format("%.2f%%", view.getStopCaseCount() * 100.0 / finishCount));
            }
        });
    }


    private WorkOrderDataStatisticsView convert(WorkOrderDataStatistics x) {
        WorkOrderDataStatisticsView view = new WorkOrderDataStatisticsView();
        if (x == null) {
            return view;
        }

        view.setCallCount(x.getCallCount());
        view.setDoneCount(x.getDoneCount());
        view.setAssignCount(x.getAssignCount());
        view.setManualLockCount(x.getManualLockCount());
        view.setExceptionDoneCount(x.getExceptionDoneCount());
        view.setAuditPassCount(x.getAuditPassCount());
        view.setAuditRejectCount(x.getAuditRejectCount());
        view.setStopCaseCount(x.getStopCaseCount());
        view.setWithinThreeMinutesCount(x.getWithinThreeMinutesCount());
        view.setOutsideThreeMinutesCount(x.getOutsideThreeMinutesCount());
        view.setWithinTenMinutesCount(x.getWithinTenMinutesCount());
        view.setOutsideTenMinutesCount(x.getOutsideTenMinutesCount());
        view.setHandleManualLockCount(x.getHandleManualLockCount());
        view.setEffectiveCallCount(x.getEffectiveCallCount());
        return view;
    }

    private WorkOrderDataStatisticsView viewsToView(List<WorkOrderDataStatisticsView> list) {
        WorkOrderDataStatisticsView view = new WorkOrderDataStatisticsView();
        if (CollectionUtils.isEmpty(list)) {
            return view;
        }

        list.forEach(x -> {
            view.setCallCount(view.getCallCount() + x.getCallCount());
            view.setDoneCount(view.getDoneCount() + x.getDoneCount());
            view.setAssignCount(view.getAssignCount() + x.getAssignCount());
            view.setManualLockCount(view.getManualLockCount() + x.getManualLockCount());
            view.setExceptionDoneCount(view.getExceptionDoneCount() + x.getExceptionDoneCount());
            view.setAuditPassCount(view.getAuditPassCount() + x.getAuditPassCount());
            view.setAuditRejectCount(view.getAuditRejectCount() + x.getAuditRejectCount());
            view.setStopCaseCount(view.getStopCaseCount() + x.getStopCaseCount());
            view.setWithinThreeMinutesCount(view.getWithinThreeMinutesCount() + x.getWithinThreeMinutesCount());
            view.setOutsideThreeMinutesCount(view.getOutsideThreeMinutesCount() + x.getOutsideThreeMinutesCount());
            view.setWithinTenMinutesCount(view.getWithinTenMinutesCount() + x.getWithinTenMinutesCount());
            view.setOutsideTenMinutesCount(view.getOutsideTenMinutesCount() + x.getOutsideTenMinutesCount());
            view.setPauseDuration(view.getPauseDuration() + x.getPauseDuration());
            view.setLaterDoingCount(view.getLaterDoingCount() + x.getLaterDoingCount());
            view.setDoingCount(view.getDoingCount() + x.getDoingCount());
            view.setHandleManualLockCount(view.getHandleManualLockCount() + x.getHandleManualLockCount());
            view.setEffectiveCallCount(view.getEffectiveCallCount() + x.getEffectiveCallCount());
            view.setFinishCount(view.getFinishCount() + x.getFinishCount());
            view.setHistoricalDoingCount(view.getHistoricalDoingCount() + x.getHistoricalDoingCount());
            view.setHistoricalFinishCount(view.getHistoricalFinishCount() + x.getHistoricalFinishCount());

        });

        if (list.stream().anyMatch(x -> x.getOperatorStatus() == StaffStatusEnum.online.getType())) {
            view.setOperatorStatus(StaffStatusEnum.online.getType());
        } else if (list.stream().anyMatch(x -> x.getOperatorStatus() == StaffStatusEnum.stop.getType())) {
            view.setOperatorStatus(StaffStatusEnum.stop.getType());
        } else {
            view.setOperatorStatus(StaffStatusEnum.offline.getType());
        }

        if (list.size() > 1) {
            view.setPauseDuration(0);
        }

        return view;
    }

    private int countFinishCount(WorkOrderDataStatisticsView view) {
        if (Objects.isNull(view)) {
            return 0;
        }
        int finishCount = 0;
        if (view.getOrderType() == WorkOrderType.cailiao_fuwu.getType()) {
            finishCount = view.getManualLockCount() +
                    view.getExceptionDoneCount() +
                    view.getHandleManualLockCount();
        } else{
            finishCount = view.getManualLockCount() +
                    view.getExceptionDoneCount() +
                    view.getDoneCount() +
                    view.getStopCaseCount() +
                    view.getAuditRejectCount() +
                    view.getAuditPassCount();
        }

       return finishCount;
    }


    public List<WorkOrderReportStatisticsVo> getReportUserDataStat(List<Long> userIds, List<Integer> orderTypes) {
        int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(new Date()));
        List<WorkOrderReportStatistics> workOrderReportStatistics = workOrderReportStatisticsDao.getSumByUserIdsAndOrderType(statDay, orderTypes, userIds);
        Map<Long, WorkOrderReportStatistics> reportStatisticsMap = workOrderReportStatistics.parallelStream()
                .collect(Collectors.toMap(WorkOrderReportStatistics::getOperatorId, Function.identity(), (k1, k2) -> k2));
        //处理中数量
        List<WorkOrderDoingCount> workOrderDoingCounts = workOrderDao.queryCountByHandleResult(userIds, orderTypes, HandleResultEnum.doing.getType());
        Map<Long, WorkOrderDoingCount> doingCountMap = workOrderDoingCounts.parallelStream()
                .collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity(), (k1, k2) -> k2));
        //达成一致数量
        List<WorkOrderDoingCount> orderDoingCounts = workOrderDao.queryCountByHandleResult(userIds, orderTypes, HandleResultEnum.reach_agree.getType());
        Map<Long, WorkOrderDoingCount> reachAgreeCountMap = orderDoingCounts.parallelStream()
                .collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity(), (k1, k2) -> k2));
        //延后处理数量
        List<WorkOrderDoingCount> orderDelayCounts = workOrderDao.queryCountByHandleResult(userIds, orderTypes, HandleResultEnum.later_doing.getType());
        Map<Long, WorkOrderDoingCount> orderDelayCountsMap = orderDelayCounts.parallelStream()
                .collect(Collectors.toMap(WorkOrderDoingCount::getOperatorId, Function.identity(), (k1, k2) -> k2));

        List<Integer> assignHandleResults = List.of(HandleResultEnum.doing.getType(), HandleResultEnum.reach_agree.getType(), HandleResultEnum.noneed_deal.getType(),
                HandleResultEnum.end_deal.getType(), HandleResultEnum.end_deal_upgrade.getType(), HandleResultEnum.end_deal_lost.getType());
        Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());
        List<WorkOrderBase> workOrderBaseList = workOrderDao.queryToDay(userIds, orderTypes, assignHandleResults, dateOfZero, null);
        Map<Long, List<WorkOrderBase>> assignListMap = workOrderBaseList.parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOperatorId));

        List<Integer> handleResults = List.of(HandleResultEnum.end_deal.getType(), HandleResultEnum.end_deal_upgrade.getType(), HandleResultEnum.end_deal_lost.getType());
        Map<Long, List<WorkOrderBase>> handleListMap = workOrderDao.queryToDay(userIds, orderTypes, handleResults, null, dateOfZero)
                .parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOperatorId));

        Map<Long, List<WorkOrderBase>> noNeedListMap = workOrderDao.queryToDay(userIds, orderTypes, List.of(HandleResultEnum.noneed_deal.getType()), null, dateOfZero)
                .parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOperatorId));


        return userIds.parallelStream().map(userId -> {
            //组织 名称
            WorkOrderReportStatisticsVo workOrderReportStatisticsVo = new WorkOrderReportStatisticsVo();
            workOrderReportStatisticsVo.setOperatorId(userId);
            WorkOrderReportStatistics reportStatistics = reportStatisticsMap.get(userId);
            if (Objects.nonNull(reportStatistics)) {
                BeanUtils.copyProperties(reportStatistics, workOrderReportStatisticsVo);
            }

            String orgName = organizationService.getOrgName(userId);
            String userName = organizationService.getUserName(userId);
            workOrderReportStatisticsVo.setOperatorName(userName);
            workOrderReportStatisticsVo.setOrgName(orgName);

            // 达成一致、处理中、延后处理、库存数量
            int inventoryCount = 0;
            WorkOrderDoingCount workOrderDoingCount = doingCountMap.get(userId);
            if (Objects.nonNull(workOrderDoingCount)) {
                workOrderReportStatisticsVo.setDoingCount(workOrderDoingCount.getNum());
                inventoryCount = inventoryCount + workOrderDoingCount.getNum();
            }
            WorkOrderDoingCount orderDoingCount = reachAgreeCountMap.get(userId);
            if (Objects.nonNull(orderDoingCount)) {
                workOrderReportStatisticsVo.setReachAgreeCount(orderDoingCount.getNum());
                inventoryCount = inventoryCount + orderDoingCount.getNum();
            }
            WorkOrderDoingCount orderDelayCount = orderDelayCountsMap.get(userId);
            if (Objects.nonNull(orderDelayCount)) {
                workOrderReportStatisticsVo.setDelayCount(orderDelayCount.getNum());
                inventoryCount = inventoryCount + orderDelayCount.getNum();
            }
            workOrderReportStatisticsVo.setInventoryCount(inventoryCount);
            //新增完结比
            if (workOrderReportStatisticsVo.getDoneCount() > 0) {
                workOrderReportStatisticsVo.setAddedEndRatio(String.format("%.2f%%",
                        workOrderReportStatisticsVo.getAssignCount() * 100.0 / workOrderReportStatisticsVo.getDoneCount()));
            }
            //升级率
            int count = workOrderReportStatisticsVo.getEndDealCount() + workOrderReportStatisticsVo.getUpgradeCount() + workOrderReportStatisticsVo.getLostCount();
            if (count > 0) {
                workOrderReportStatisticsVo.setUpgradeRate(String.format("%.2f%%",
                        workOrderReportStatisticsVo.getUpgradeCount() * 100.0 / count));
            }
            //平均分配时长
            List<WorkOrderBase> assignOrderBases = assignListMap.get(userId);
            if (CollectionUtils.isNotEmpty(assignOrderBases)) {
                String averageAssignDuration = getAverageAssignDuration(assignOrderBases);
                workOrderReportStatisticsVo.setAverageAssignDuration(averageAssignDuration);
            }
            //有效关单时长
            List<WorkOrderBase> handleOrderBases = handleListMap.get(userId);
            if (CollectionUtils.isNotEmpty(handleOrderBases)) {
                String averageEffectiveEndDuration = getAverageEndDuration(handleOrderBases);
                workOrderReportStatisticsVo.setAverageEffectiveEndDuration(averageEffectiveEndDuration);
            }

            //无效关单时长
            List<WorkOrderBase> noNeedOrderBases = noNeedListMap.get(userId);
            if (CollectionUtils.isNotEmpty(noNeedOrderBases)) {
                String averageEndDuration = getAverageEndDuration(noNeedOrderBases);
                workOrderReportStatisticsVo.setAverageInvalidEndDuration(averageEndDuration);
            }

            return workOrderReportStatisticsVo;
        }).collect(Collectors.toList());
    }

    private String getAverageAssignDuration(List<WorkOrderBase> orderBases) {
        double sum = orderBases.parallelStream().mapToDouble(workOrder -> ((workOrder.getHandleTime().getTime() - workOrder.getCreateTime().getTime()) * 1.0 / 1000 / 60)
        ).sum();
        double result = sum / orderBases.size();
        return df.format(result);
    }

    private String getAverageEndDuration(List<WorkOrderBase> orderBases) {
        double sum = orderBases.parallelStream().mapToDouble(workOrder -> ((workOrder.getFinishTime().getTime() - workOrder.getHandleTime().getTime()) * 1.0 / 1000 / 60 / 60)
        ).sum();
        double result = sum / orderBases.size();
        return df.format(result);
    }

    public List<WorkOrderReportStatisticsVo> getReportUserDataByOrderType(long userId, List<Integer> orderTypes) {
        String orgName = organizationService.getOrgName(userId);

        String userName = organizationService.getUserName(userId);

        int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(new Date()));

        List<WorkOrderReportStatistics> workOrderReportStatistics = workOrderReportStatisticsDao.getByUserIdAndOrderType(statDay, orderTypes, userId);
        Map<Integer, WorkOrderReportStatistics> reportStatisticsMap = workOrderReportStatistics.stream()
                .collect(Collectors.toMap(WorkOrderReportStatistics::getOrderType, Function.identity(), (k1, k2) -> k2));

        //处理中数量
        List<WorkOrderStatisticalCount> workOrderDoingCounts = workOrderDao.queryCountGroupByOrderType(userId, orderTypes, HandleResultEnum.doing.getType());
        Map<Integer, WorkOrderStatisticalCount> workOrderDoingCountMap = workOrderDoingCounts.stream()
                .collect(Collectors.toMap(WorkOrderStatisticalCount::getOrderType, Function.identity(), (k1, k2) -> k2));

        //达成一致数量
        List<WorkOrderStatisticalCount> orderReachAgreeCounts = workOrderDao.queryCountGroupByOrderType(userId, orderTypes, HandleResultEnum.reach_agree.getType());
        Map<Integer, WorkOrderStatisticalCount> orderReachAgreeCountMap = orderReachAgreeCounts.stream()
                .collect(Collectors.toMap(WorkOrderStatisticalCount::getOrderType, Function.identity(), (k1, k2) -> k2));

        //延后处理数量
        List<WorkOrderStatisticalCount> orderDelayCounts = workOrderDao.queryCountGroupByOrderType(userId, orderTypes, HandleResultEnum.later_doing.getType());
        Map<Integer, WorkOrderStatisticalCount> orderDelayCountsMap = orderDelayCounts.parallelStream()
                .collect(Collectors.toMap(WorkOrderStatisticalCount::getOrderType, Function.identity(), (k1, k2) -> k2));

        List<Integer> assignHandleResults = List.of(HandleResultEnum.doing.getType(), HandleResultEnum.reach_agree.getType(), HandleResultEnum.noneed_deal.getType(),
                HandleResultEnum.end_deal.getType(), HandleResultEnum.end_deal_upgrade.getType(), HandleResultEnum.end_deal_lost.getType());
        Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());
        List<WorkOrderBase> workOrderBaseList = workOrderDao.queryToDay(List.of(userId), orderTypes, assignHandleResults, dateOfZero, null);
        Map<Integer, List<WorkOrderBase>> assignListMap = workOrderBaseList.parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOrderType));

        List<Integer> handleResults = List.of(HandleResultEnum.end_deal.getType(), HandleResultEnum.end_deal_upgrade.getType(), HandleResultEnum.end_deal_lost.getType());
        var handleListMap = workOrderDao.queryToDay(List.of(userId), orderTypes, handleResults, null, dateOfZero)
                .parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOrderType));

        var noNeedListMap = workOrderDao.queryToDay(List.of(userId), orderTypes, List.of(HandleResultEnum.noneed_deal.getType()), null, dateOfZero)
                .parallelStream().collect(Collectors.groupingBy(WorkOrderBase::getOrderType));

        List<WorkOrderReportStatisticsVo> workOrderReportStatisticsVos = Lists.newArrayList();
        orderTypes.parallelStream().forEach(orderType -> {
            WorkOrderReportStatisticsVo workOrderReportStatisticsVo = new WorkOrderReportStatisticsVo();
            workOrderReportStatisticsVo.setOperatorId(userId);
            WorkOrderReportStatistics reportStatistics = reportStatisticsMap.get(orderType);
            if (Objects.nonNull(reportStatistics)) {
                BeanUtils.copyProperties(reportStatistics, workOrderReportStatisticsVo);
            }

            WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
            workOrderReportStatisticsVo.setOrderType(orderType);
            if (Objects.nonNull(workOrderTypeDO)){
                workOrderReportStatisticsVo.setOrderTypeName(workOrderTypeDO.getMsg());
            }
            workOrderReportStatisticsVo.setOperatorName(userName);
            workOrderReportStatisticsVo.setOrgName(orgName);


            int inventoryCount = 0;
            WorkOrderStatisticalCount workOrderDoingCount = workOrderDoingCountMap.get(orderType);
            if (Objects.nonNull(workOrderDoingCount)) {
                workOrderReportStatisticsVo.setDoingCount(workOrderDoingCount.getNum());
                inventoryCount = inventoryCount + workOrderDoingCount.getNum();
            }

            WorkOrderStatisticalCount orderReachAgreeCount = orderReachAgreeCountMap.get(orderType);
            if (Objects.nonNull(orderReachAgreeCount)) {
                workOrderReportStatisticsVo.setReachAgreeCount(orderReachAgreeCount.getNum());
                inventoryCount = inventoryCount + orderReachAgreeCount.getNum();
            }

            WorkOrderStatisticalCount orderDelayCount = orderDelayCountsMap.get(orderType);
            if (Objects.nonNull(orderDelayCount)) {
                workOrderReportStatisticsVo.setDelayCount(orderDelayCount.getNum());
                inventoryCount = inventoryCount + orderDelayCount.getNum();
            }

            workOrderReportStatisticsVo.setInventoryCount(inventoryCount);
            //新增完结比
            if (workOrderReportStatisticsVo.getDoneCount() > 0) {
                workOrderReportStatisticsVo.setAddedEndRatio(String.format("%.2f%%",
                        workOrderReportStatisticsVo.getAssignCount() * 100.0 / workOrderReportStatisticsVo.getDoneCount()));
            }
            //升级率
            int count = workOrderReportStatisticsVo.getEndDealCount() + workOrderReportStatisticsVo.getUpgradeCount() + workOrderReportStatisticsVo.getLostCount();
            if (count > 0) {
                workOrderReportStatisticsVo.setUpgradeRate(String.format("%.2f%%",
                        workOrderReportStatisticsVo.getUpgradeCount() * 100.0 / count));
            }

            //平均分配时长
            List<WorkOrderBase> assignOrderBases = assignListMap.get(orderType);
            if (CollectionUtils.isNotEmpty(assignOrderBases)) {
                String averageAssignDuration = getAverageAssignDuration(assignOrderBases);
                workOrderReportStatisticsVo.setAverageAssignDuration(averageAssignDuration);
            }
            //有效关单时长
            List<WorkOrderBase> handleOrderBases = handleListMap.get(orderType);
            if (CollectionUtils.isNotEmpty(handleOrderBases)) {
                String averageEffectiveEndDuration = getAverageEndDuration(handleOrderBases);
                workOrderReportStatisticsVo.setAverageEffectiveEndDuration(averageEffectiveEndDuration);
            }

            //无效关单时长
            List<WorkOrderBase> noNeedOrderBases = noNeedListMap.get(orderType);
            if (CollectionUtils.isNotEmpty(noNeedOrderBases)) {
                String averageEndDuration = getAverageEndDuration(noNeedOrderBases);
                workOrderReportStatisticsVo.setAverageInvalidEndDuration(averageEndDuration);
            }

            workOrderReportStatisticsVos.add(workOrderReportStatisticsVo);
        });
        return workOrderReportStatisticsVos;
    }


}
