package com.shuidihuzhu.workorder.service.admin.impl;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.dao.AdminWorkOrderDao;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.service.admin.AdminWorkOrderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AdminWorkOrderServiceImpl implements AdminWorkOrderService {

    @Autowired
    private AdminWorkOrderDao adminWorkOrderDao;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserOperationRecordDao recordDao;

    @Override
    public Response<Void> initWorkOrderState(Long workOrderId, Integer operatorId) {
        Response<Void> response = new Response<>();
        if (workOrderId == null) {
            response.setCode(ErrorCode.SYSTEM_PARAM_ERROR.getCode());
            response.setMsg("workOrderId 不能非空");
            return response;
        }

        if (operatorId == null) {
            response.setCode(ErrorCode.SYSTEM_PARAM_ERROR.getCode());
            response.setMsg("operatorId 不能非空");
            return response;
        }

        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            response.setCode(ErrorCode.SYSTEM_PARAM_ERROR.getCode());
            response.setMsg("order 不存在");
            return response;
        }

        int count = adminWorkOrderDao.updateHandleResult(workOrderId, operatorId, HandleResultEnum.doing.getType());

        if (count <= 0) {
            response.setCode(ErrorCode.SYSTEM_ERROR.getCode());
            response.setMsg("初始工单失败");
        }

        WorkOrderRecord record = new WorkOrderRecord();

        record.setOperatorId(operatorId);
        record.setWorkOrderType(workOrder.getOrderType());
        record.setComment(operatorId+"操作初始化");
        record.setOperateMode(OperateMode.init.getType());
        record.setOperateDesc(OperateMode.init.getMsg());
        record.setWorkOrderId(workOrder.getId());

        recordDao.saveRecord(record);

        return response;
    }
}
