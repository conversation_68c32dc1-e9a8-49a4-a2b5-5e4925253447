package com.shuidihuzhu.workorder.service;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.impl.WorkOrderBaseStatService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/24
 */
public abstract class WorkOrderStatFacadeService<T extends WorkOrderStat> implements WorkOrderStatService<T> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    public OpResult<List<T>> getWorkOrderStatList(int one, String two, long userId){

        if (one<=0){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //如果二级没选
        if (StringUtils.isEmpty(two)){
            return getOneLevel(one,userId);
        }else {
            List<Integer> twoList = Arrays.stream(two.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            return getTwoLevel(one,twoList,userId);
        }
    }

    protected OpResult<List<T>> getOneLevel(int oneLevel,long userId) {
        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getOneAndUser(oneLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyOneLevel(oneLevel));
        }
    }

    private OpResult<List<T>> getTwoLevel(int oneLevel,List<Integer> twoLevel ,long userId) {
        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getTwoAndUser(oneLevel,twoLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyTwo(oneLevel,twoLevel));
        }
    }


    private List<T> getOneAndUser(int oneLevel, long userId){

        List<Integer> doneList = getDoneList();

        List<Long> users = Lists.newArrayList(userId);
        List<Integer>  twoList = workOrderTypeService.getByOneLevel(oneLevel);
        //0代表全部用户
        if(userId == 0){
            users = Lists.newArrayList(staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toSet()));
        }

        List<WorkOrderStat> list = baseStatService.getOneAndUser(oneLevel,twoList,users,doneList);

        return getUserStat(list,users,twoList);
    }



    protected List<T> getOnlyTwo(int one,List<Integer> twoList) {

        List<Integer> doneList = getDoneList();

        List<WorkOrderStat> stats = baseStatService.getTwoALL(one,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        return getTypeStat(stats,users,twoList);
    }


    private List<T> getTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        if (twoLevel.size()>1){
            return getAllTwoAndUser(oneLevel,twoLevel,userId);
        }

        List<Integer> doneList = getDoneList();

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getTwoAndUser(oneLevel,twoLevel.get(0),users,doneList);

        return getUserStat(list,users,twoList);
    }

    protected List<T> getAllTwoAndUser(int oneLevel, List<Integer> twoLevel, long userId){

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        List<Integer> doneList = getDoneList();

        List<WorkOrderStat> list = baseStatService.getAllTwoAndUser(oneLevel,twoLevel,userId,doneList);

        return getTypeStat(list,users,twoList);
    }

    public abstract List<Integer> getDoneList();

}
