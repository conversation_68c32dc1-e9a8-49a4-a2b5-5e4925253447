package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.OrderOperationFacade;
import com.shuidihuzhu.workorder.dao.ReportWorkOrderDAO;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @time 2019/12/26 下午3:31
 * @desc
 */
@Slf4j
@Service("reportWorkOrderServiceImpl")
public class ReportWorkOrderServiceImpl extends WorkOrderFacade<ReportWorkOrder, ReportHandleOrderParam,WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private ReportWorkOrderDAO reportWorkOrderDAO;

    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired
    private OrderOperationFacade orderOperationFacade;

    @Override
    public OpResult vlidate(ReportWorkOrder wordOrder) {
        if(!WorkOrderType.REPORT_TYPES.contains(wordOrder.getOrderType())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(ReportWorkOrder wordOrder) {

        wordOrder.setOrderlevel(OrderLevel.edium.getType());

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(ReportHandleOrderParam param) {

        final int handleResult = param.getHandleResult();
        if (handleResult != HandleResultEnum.reach_agree.getType()
                && handleResult != HandleResultEnum.noneed_deal.getType()
                && handleResult != HandleResultEnum.end_deal.getType()
                && handleResult != HandleResultEnum.end_deal_upgrade.getType()
                && handleResult != HandleResultEnum.end_deal_lost.getType()
                && handleResult != HandleResultEnum.later_doing.getType()) {
            return OpResult.createFailResult(SYSTEM_PARAM_ERROR);
        }

        if(!WorkOrderType.REPORT_TYPES.contains(param.getOrderType())){
            return OpResult.createFailResult(SYSTEM_PARAM_ERROR);
        }

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());

        if (handleResult == HandleResultEnum.reach_agree.getType() && HandleResultEnum.doing.getType() != workOrderBase.getHandleResult()
                && HandleResultEnum.later_doing.getType() != workOrderBase.getHandleResult()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }

        if (handleResult == HandleResultEnum.noneed_deal.getType() && HandleResultEnum.reach_agree.getType() != workOrderBase.getHandleResult()
                && HandleResultEnum.doing.getType() != workOrderBase.getHandleResult()
                && HandleResultEnum.later_doing.getType() != workOrderBase.getHandleResult()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }

        if (handleResult == HandleResultEnum.end_deal.getType() && HandleResultEnum.reach_agree.getType() != workOrderBase.getHandleResult()
                && HandleResultEnum.doing.getType() != workOrderBase.getHandleResult()
                && HandleResultEnum.later_doing.getType() != workOrderBase.getHandleResult()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }

        int result = workOrderDao.reportHandle(param.getWorkOrderId(), param.getHandleResult());
        if (result > 0){
            WorkOrderExt ext = new WorkOrderExt(param.getWorkOrderId(), param.getOperComment(), DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
            orderExtService.createWorkOrderExt(Lists.newArrayList(ext));
            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
    }

    @Override
    public OpResult getOrderList(WorkOrderListParam param) {

        if (param.getUserId() <= 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize()+1;

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).map(Integer::valueOf).collect(Collectors.toList());

        List<WorkOrderBase> list = reportWorkOrderDAO.queryWorkOrderList(Lists.newArrayList(param.getUserId()), param.getOrderType(),
                results, p, param.getWorkOrderId(), param.getPaging(), param.getStartTime(), param.getEndTime(),param.getCaseId());

        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }

        List<WorkOrderVO> voList = list.stream().map(r-> {

            List<WorkOrderExt> exts = daoExt.getWorkOrderExtByName(r.getId(), Arrays.asList(OrderExtName.reportId.getName(), OrderExtName.fundUseProgressId.getName()));
            List<String> reportIds = exts.stream()
                    .filter(f -> StringUtils.equals(f.getExtName(), OrderExtName.reportId.getName()))
                    .map(WorkOrderExt::getExtValue)
                    .collect(Collectors.toList());
            int fundUserProgressId = exts.stream()
                    .filter(f -> StringUtils.equals(f.getExtName(), OrderExtName.fundUseProgressId.getName()))
                    .map(WorkOrderExt::getExtValue)
                    .map(Integer::valueOf)
                    .findFirst()
                    .orElse(0);

            WorkOrderVO workOrderVO = new WorkOrderVO();

            WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(r.getId(), OrderExtName.workOrderHandleReason.getName());
            if (Objects.nonNull(workOrderExt)) {
                workOrderVO.setReportHandleReason(Integer.parseInt(workOrderExt.getExtValue()));
            }
            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setOperatorId(r.getOperatorId());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setReportIds(String.join(",", reportIds));
            workOrderVO.setFundUseProgressId(fundUserProgressId);
            workOrderVO.setOrderLevel(r.getOrderlevel());
            return workOrderVO;
        }).filter(workOrderVO -> {
            if (param.getReasonCode() <= 0){
                return true;
            }
            return workOrderVO.getReportHandleReason() == param.getReasonCode();
        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }
}
