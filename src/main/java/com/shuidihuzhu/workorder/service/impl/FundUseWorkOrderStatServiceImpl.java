package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.FundUseWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 */
@Service("fundUseWorkOrderStatService")
public class FundUseWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<FundUseWorkOrderStat> {

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType(),
            HandleResultEnum.report_intervene.getType()
    );


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<FundUseWorkOrderStat> getOnlyOneLevel(int oneLevel) {
        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel, twoList, doneList);

        List<Long> users = Lists.newArrayList();

        int passNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.audit_pass, today);

        int rejectNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.audit_reject, today);

        int interveneNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.report_intervene, today);

        FundUseWorkOrderStat s = new FundUseWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat, s);
        s.setPassAmount(passNum);
        s.setRejectAmount(rejectNum);
        s.setReportInterveneAmount(interveneNum);

        return Lists.newArrayList(s);
    }

    @Override
    public List<FundUseWorkOrderStat> getTypeStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {

        Map<Integer, Map<Integer, Integer>> result = baseStatService.getTypeStatBase(users, twoList, doneList);

        return list.stream()
                .map(r -> {

                    FundUseWorkOrderStat c = new FundUseWorkOrderStat();

                    BeanUtils.copyProperties(r, c);

                    c.setPassAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_pass.getType()), r.getTwoLevel()));
                    c.setRejectAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_reject.getType()), r.getTwoLevel()));
                    c.setReportInterveneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.report_intervene.getType()), r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<FundUseWorkOrderStat> getUserStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {

        Map<Integer, Map<Long, Integer>> result = baseStatService.getUserStatBase(users, twoList, doneList);

        return list.stream()
                .map(r -> {
                    FundUseWorkOrderStat c = new FundUseWorkOrderStat();

                    BeanUtils.copyProperties(r, c);

                    c.setPassAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_pass.getType()), r.getUserId()));
                    c.setRejectAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_reject.getType()), r.getUserId()));
                    c.setReportInterveneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.report_intervene.getType()), r.getUserId()));

                    return c;

                }).collect(Collectors.toList());
    }
}
