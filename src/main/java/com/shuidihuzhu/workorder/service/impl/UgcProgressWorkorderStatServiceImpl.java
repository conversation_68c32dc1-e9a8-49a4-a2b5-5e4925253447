package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.UgcProgressWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/24
 */
@Service("ugcProgressWorkorderStatService")
public class UgcProgressWorkorderStatServiceImpl extends WorkOrderStatFacadeService<UgcProgressWorkOrderStat> {

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.pass_show.getType(),
            HandleResultEnum.modify.getType(),
            HandleResultEnum.delete.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType()
            );


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<UgcProgressWorkOrderStat> getOnlyOneLevel(int oneLevel) {

        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        int showNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.pass_show,today);

        int modifyNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.modify,today);

        int deleteNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.delete,today);

        UgcProgressWorkOrderStat s = new UgcProgressWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setPassShowAmount(showNum);
        s.setModifyAmount(modifyNum);
        s.setDeleteAmount(deleteNum);

        return Lists.newArrayList(s);
    }

    @Override
    public List<UgcProgressWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Integer,Integer>> result = baseStatService.getTypeStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{

                    UgcProgressWorkOrderStat c = new UgcProgressWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setPassShowAmount(MapUtil.getFromMap(result.get(HandleResultEnum.pass_show.getType()),r.getTwoLevel()));
                    c.setModifyAmount(MapUtil.getFromMap(result.get(HandleResultEnum.modify.getType()),r.getTwoLevel()));
                    c.setDeleteAmount(MapUtil.getFromMap(result.get(HandleResultEnum.delete.getType()),r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<UgcProgressWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Long,Integer>> result = baseStatService.getUserStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{
                    UgcProgressWorkOrderStat c = new UgcProgressWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setPassShowAmount(MapUtil.getFromMap(result.get(HandleResultEnum.pass_show.getType()),r.getUserId()));
                    c.setModifyAmount(MapUtil.getFromMap(result.get(HandleResultEnum.modify.getType()),r.getUserId()));
                    c.setDeleteAmount(MapUtil.getFromMap(result.get(HandleResultEnum.delete.getType()),r.getUserId()));

                    return c;

                }).collect(Collectors.toList());
    }
}
