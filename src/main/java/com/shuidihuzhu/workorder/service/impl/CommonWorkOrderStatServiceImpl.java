package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.v2.model.view.WorkOrderStatVO;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("CommonWorkOrderStatServiceImpl")
public class CommonWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<WorkOrderStatVO> implements WorkOrderStatService<WorkOrderStatVO> {

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    private static List<Integer> doneList = Lists.newArrayList(
            HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType(),
            HandleResultEnum.stop_case.getType(),
            HandleResultEnum.return_call.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.manual_lock.getType(),
            HandleResultEnum.audit_reject.getType()
    );

    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }


    @Override
    public List<WorkOrderStatVO> getOnlyOneLevel(int oneLevel) {
        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel, twoList, doneList);
        return Lists.newArrayList(map2VO(workOrderStat));
    }


    @Override
    public List<WorkOrderStatVO> getTypeStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(this::map2VO).collect(Collectors.toList());
    }

    @Override
    public List<WorkOrderStatVO> getUserStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(this::map2VO).collect(Collectors.toList());
    }


    private WorkOrderStatVO map2VO(WorkOrderStat workOrderStat) {
        if (workOrderStat == null) {
            return null;
        }
        WorkOrderStatVO view = new WorkOrderStatVO();
        BeanUtils.copyProperties(workOrderStat, view);
        return view;
    }
}
