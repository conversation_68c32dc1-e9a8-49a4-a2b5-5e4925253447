package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.constant.CfClientMQTopicCons;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
@Service("cailiaoWorkOrderService")
@Slf4j
public class CailiaoWorkOrderServiceImpl extends WorkOrderFacade<CailiaoWorkOrder, CailiaoHandleOrderParam,WorkOrderListParam> {


    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private WorkTypePropertyService orderTypePropertyService;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    final static String ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER = "CLEW_NOTICE_ADMIN_CLOSE_WORK_ORDER";


    @Override
    public OpResult vlidate(CailiaoWorkOrder wordOrder) {

        List<Integer> types = workOrderTypeService.getByOneLevel(OneTypeEnum.cailiao.getType());

        Set<Integer> integers = types.stream().collect(Collectors.toSet());

        if (!integers.contains(wordOrder.getOrderType())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        //如果创建的是服务单
        if (wordOrder.getOrderType() == WorkOrderType.cailiao_fuwu.getType()){

            WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                    Lists.newArrayList(WorkOrderType.cailiao_fuwu.getType()),
                    HandleResultEnum.unDoResult());
            if (workOrderBase != null){
                //如果存在的是材审服务单 提升优先级
                if (workOrderBase.getOrderType() == WorkOrderType.cailiao_fuwu.getType() && workOrderBase.getOrderlevel() != OrderLevel.S.getType()){
                    int newLevel = OrderLevel.A.getType();
                    if(workOrderBase.getOrderlevel() == newLevel){
                        newLevel = OrderLevel.S.getType();
                    }
                    workOrderDao.changeOrderLevel(workOrderBase.getId(),newLevel);
                }
                if (StringUtils.isNotEmpty(wordOrder.getFlowOrderId())){
                    //关闭信息传递工单
                    noticeFlowOrder(Long.valueOf(wordOrder.getFlowOrderId()),workOrderBase.getOperatorId(),0);
                }

                log.info("new order vlidate caseId={} repeat",wordOrder.getCaseId());
                return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
            }
        }else {
            //创建的是审核单把服务单关闭
            baseWorkOrderService.closeOrderBycaseIdAndTypeWithoutHandleTime(wordOrder.getCaseId(),WorkOrderType.cailiao_fuwu.getType(),HandleResultEnum.done.getType(),0,"用户提交材审");
        }

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                Lists.newArrayList(WorkOrderType.cailiao_4.getType(),WorkOrderType.cailiao_5.getType()),
                HandleResultEnum.unDoResult());
        if (workOrderBase != null){
            log.info("new order vlidate caseId={} repeat",wordOrder.getCaseId());
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }

        return OpResult.createSucResult();

    }


    @Override
    public OpResult<Long> create(CailiaoWorkOrder wordOrder) {

        if (wordOrder.getOrderlevel() == 0) {
            wordOrder.setOrderlevel(OrderLevel.E.getType());
        }

        // 主动服务工单写入首次字段
        int caseOrderCountByType = workOrderDao.getCaseOrderCountByType(wordOrder.getCaseId(), wordOrder.getOrderType());
        wordOrder.setFirstCreate(caseOrderCountByType <= 0);

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(CailiaoHandleOrderParam param) {


        final int handleResult = param.getHandleResult();

        Set<Integer> set = workOrderTypeService.getByOneLevel(OneTypeEnum.cailiao.getType()).stream().collect(Collectors.toSet());

        if (!set.contains(param.getOrderType())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        //如果手动提交   判断一下数量
        if (handleResult == HandleResultEnum.later_doing.getType()){

            int count =  baseWorkOrderService.getCountByHandleResult(param.getUserId(),param.getOrderType(), handleResult + "");

            if (count >= orderTypePropertyService.getMaxDelayCount(param.getOrderType())){
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }
        int result = 0;
        //材料服务单  提交暂不关单
        if (HandleResultEnum.doing.getType() == handleResult && param.getOrderType() == WorkOrderType.cailiao_fuwu.getType()){
//            result = 1;
            result = workOrderDao.updateFinishTime(param.getWorkOrderId());
        }else {
            result = workOrderDao.handle(param.getWorkOrderId(),param.getHandleResult());
        }

        if (result > 0){
            orderExtService.createWorkOrderExt(param.getWorkOrderExt());
            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }


    @Override
    public OpResult<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param) {


        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize+1;

        int orderType = param.getOrderType();
        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), orderType,results,p,param.getWorkOrderId(),
                paging,param.getCaseId(),param.getStartTime(),param.getEndTime(),param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }


        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }


        List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());

        FeignResponse<List<CrowdfundingInfo>> infoListResponse = client.getCrowdfundingListById(caseIds);
        if (infoListResponse == null || infoListResponse.notOk()) {
            return OpResult.createFailResult(ErrorCode.RPC_FALL_BACK);
        }
        List<CrowdfundingInfo> feignResponse = infoListResponse.getData();

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(),(o1,o2)->o2));

        Map<Integer,ChannelRefine.ChannelRefineResuleEnum> recordMap =baseWorkOrderService.getChannel(feignResponse);

        List<Long> workOrderIdList = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        Map<Long,WorkOrderExt> followLabelMap = orderExtService.getExtMap(workOrderIdList,OrderExtName.followLabel);

        Map<Long, WorkOrderExt> firstCreateMap = orderExtService.getExtMap(workOrderIdList, OrderExtName.firstCreate);


        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{

            WorkOrderVO workOrderVO = new WorkOrderVO();

            long workOrderId = r.getId();
            workOrderVO.setWorkOrderId(workOrderId);
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            workOrderVO.setTitle(c.getTitle());

            ChannelRefine.ChannelRefineResuleEnum resuleEnum = recordMap.get(r.getCaseId());

            if (resuleEnum != null){
                workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
                workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
            }

            workOrderVO.setCaseChannel(formatChannel(c.getChannel()));

            workOrderVO.setCaseUuid(c.getInfoId());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setFollowTime(0l);

            WorkOrderExt followExt = followLabelMap.get(workOrderId);
            if (followExt != null){
                workOrderVO.setFollowLabel(followExt.getExtValue());
                workOrderVO.setFollowTime(followExt.getCreateTime().getTime());
            }
            Boolean firstCreate = Optional.ofNullable(firstCreateMap.get(workOrderId))
                    .map(WorkOrderExt::getExtValue).map(Boolean::valueOf).orElse(false);
            workOrderVO.setFirstCreate(firstCreate);

            return workOrderVO;

        }).collect(Collectors.toList());


        //如果处理中或稍后处理   按照优先级排序一下
        if (results.contains(HandleResultEnum.later_doing.getType()) || results.contains(HandleResultEnum.doing.getType())){
            Comparator<WorkOrderVO> comparator = Comparator.comparing(WorkOrderVO::getOrderLevel, Comparator.reverseOrder());
            if (orderType == WorkOrderType.cailiao_fuwu.getType()){
                comparator = Comparator.comparing(WorkOrderVO::getFirstCreate)
                        .thenComparing(WorkOrderVO::getOrderLevel,Comparator.reverseOrder())
                        .thenComparing(WorkOrderVO::getCreateTime,Comparator.reverseOrder());
            }
            voList = voList.stream().sorted(comparator).collect(Collectors.toList());
        }

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }


    public String formatChannel(String channel){

        if (StringUtils.isEmpty(channel)){
            return "其他";
        }

        if ("cf_volunteer".equals(channel)){
            return "线下渠道";
        }

        if (channel.contains("app")){
            return "APP";
        }

        if (channel.contains("toutiao") || "ad_search_feed".equals(channel)){
            return "头条信息流";
        }

        if (channel.contains("gzh")){
            return "外围号";
        }

        if ("wx_enter_nofollow".equals(channel)){
            return "我也要筹款";
        }

        if (channel.contains("wx_menu_choukuanren")){
            return "菜单发起";
        }

        return "其他";
    }

    public void noticeFlowOrder(long flowOrderId,long userId,int type){
        String mis = "";
        if (userId > 0){
            Response<AuthUserDto> rpcResponse =  userFeignClient.getAuthUserById(userId);
            if (rpcResponse.ok() && rpcResponse.getData() != null){
                mis = rpcResponse.getData().getLoginName();
            }
        }
        NoticeCloseWorkOrderModel n = new NoticeCloseWorkOrderModel();
        n.setWorkOrderId(flowOrderId);
        n.setEventType(type);
        n.setMis(mis);
        n.setSource("cailiaoWorkOrder");

        Message message = Message.of(CfClientMQTopicCons.CF,ADMIN_CLEW_NOTICE_TO_END_FLOW_ORDER,flowOrderId+"_cailiao",n);
        producer.send(message);
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class NoticeCloseWorkOrderModel {

        private long workOrderId;
        private String mis;
        // 0 关闭 1 处理中
        private int eventType;

        private String source;

    }
}
