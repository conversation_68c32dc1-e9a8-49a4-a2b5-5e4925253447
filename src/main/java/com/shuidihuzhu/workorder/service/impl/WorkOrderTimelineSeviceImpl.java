package com.shuidihuzhu.workorder.service.impl;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTimeline;
import com.shuidihuzhu.workorder.dao.WorkOrderTimelineDao;
import com.shuidihuzhu.workorder.service.WorkOrderTimelineSevice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @DATE 2019/7/5
 */
@Service
public class WorkOrderTimelineSeviceImpl implements WorkOrderTimelineSevice {


    @Autowired
    private WorkOrderTimelineDao workOrderTimelineDao;

    @Override
    public int insertTimeLine(WorkOrderTimeline workOrderTimeline) {

        WorkOrderTimeline w = workOrderTimelineDao.getWorkOrderTimeline(workOrderTimeline.getWorkOrderId(),
               workOrderTimeline.getWorkOrderType(),workOrderTimeline.getWorkResult());

        if (w == null){
          return workOrderTimelineDao.insertTimeLine(workOrderTimeline);
        }

        return 0;
    }
}
