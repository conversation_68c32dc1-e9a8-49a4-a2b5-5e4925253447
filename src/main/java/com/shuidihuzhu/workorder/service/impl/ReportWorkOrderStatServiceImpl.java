package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.ReportWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("reportWorkOrderStatService")
public class ReportWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<ReportWorkOrderStat> implements WorkOrderStatService<ReportWorkOrderStat> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.noneed_deal.getType(),
            HandleResultEnum.reach_agree.getType(),
            HandleResultEnum.end_deal.getType());


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<ReportWorkOrderStat> getOnlyOneLevel(int oneLevel) {
        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel, twoList, doneList);

        List<Long> users = Lists.newArrayList();

        int doneNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.done, today);

        int noNeedDealNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.noneed_deal, today);

        int reachAgreeNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.reach_agree, today);

        int endDealNum = baseStatService.getStatusInt(twoList, users, HandleResultEnum.end_deal, today);

        ReportWorkOrderStat s = new ReportWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat, s);
        s.setDoneAmount(doneNum);
        s.setNoNeedDealAmount(noNeedDealNum);
        s.setReachAgreeAmount(reachAgreeNum);
        s.setEndDealAmount(endDealNum);

        return Lists.newArrayList(s);
    }

    @Override
    public List<ReportWorkOrderStat> getTypeStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {

        Map<Integer, Map<Integer, Integer>> result = baseStatService.getTypeStatBase(users, twoList, doneList);

        return list.stream()
                .map(r -> {

                    ReportWorkOrderStat c = new ReportWorkOrderStat();

                    BeanUtils.copyProperties(r, c);
                    c.setDoneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()), r.getTwoLevel()));
                    c.setNoNeedDealAmount(MapUtil.getFromMap(result.get(HandleResultEnum.noneed_deal.getType()), r.getTwoLevel()));
                    c.setReachAgreeAmount(MapUtil.getFromMap(result.get(HandleResultEnum.reach_agree.getType()), r.getTwoLevel()));
                    c.setEndDealAmount(MapUtil.getFromMap(result.get(HandleResultEnum.end_deal.getType()), r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<ReportWorkOrderStat> getUserStat(List<WorkOrderStat> list, List<Long> users, List<Integer> twoList) {

        Map<Integer, Map<Long, Integer>> result = baseStatService.getUserStatBase(users, twoList, doneList);

        return list.stream()
                .map(r -> {
                    ReportWorkOrderStat c = new ReportWorkOrderStat();

                    BeanUtils.copyProperties(r, c);

                    c.setDoneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()), r.getUserId()));
                    c.setNoNeedDealAmount(MapUtil.getFromMap(result.get(HandleResultEnum.noneed_deal.getType()), r.getTwoLevel()));
                    c.setReachAgreeAmount(MapUtil.getFromMap(result.get(HandleResultEnum.reach_agree.getType()), r.getTwoLevel()));
                    c.setEndDealAmount(MapUtil.getFromMap(result.get(HandleResultEnum.end_deal.getType()), r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());
    }

}
