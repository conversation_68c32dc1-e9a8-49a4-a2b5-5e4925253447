package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.ShouciWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderStatDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/5/6
 */
@Service("shouciWorkOrderStatService")
public class ShouciWorkOrderStatServiceImpl implements WorkOrderStatService<ShouciWorkOrderStat> {

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderStatDao workOrderStatDao;

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType(),
            HandleResultEnum.stop_case.getType());

    @Override
    public OpResult<List<ShouciWorkOrderStat>> getWorkOrderStatList(int one, String two, long userId) {

        if (one<=0){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        //如果二级没选
        if (StringUtils.isEmpty(two)){
            return getOne(one,userId);
        }else {
            List<Integer> twoList = Arrays.stream(two.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            return getTwo(one,twoList,userId);
        }
    }


    public OpResult<List<ShouciWorkOrderStat>> getOne(int oneLevel , long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getOneAndUser(oneLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyOne(oneLevel));
        }
    }


    public OpResult<List<ShouciWorkOrderStat>> getTwo(int oneLevel ,List<Integer> twoLevel ,long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getTwoAndUser(oneLevel,twoLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyTwo(oneLevel,twoLevel));
        }
    }


    public List<ShouciWorkOrderStat> getOneAndUser(int oneLevel, long userId){

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        List<Integer>  twoList = workOrderTypeService.getByOneLevel(oneLevel);
        //0代表全部用户
        if(userId == 0){
            users = Lists.newArrayList(staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toSet()));
        }

        List<WorkOrderStat> list = baseStatService.getOneAndUser(oneLevel,twoList,users,doneList);

        //停止筹款
        Map<Long,Integer> stopCaseMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.stop_case,today);

        //自动关闭
        Map<Long,Integer> doneCloseMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.done,today);

        //自动异常关闭
        Map<Long,Integer> doneExceptionMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //呼通
        List<WorkOrderStat> yesList = workOrderStatDao.getExtUserStat(twoList,users,OrderExtName.CallStatus.getName(),"1",today);
        Map<Long,Integer> yesMap = yesList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //未呼通
        List<WorkOrderStat> noList = workOrderStatDao.getExtUserStat(twoList,users,OrderExtName.CallStatus.getName(),"2",today);
        Map<Long,Integer> noMap = noList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return list.stream()
                .map(r->{
                    ShouciWorkOrderStat cws = new ShouciWorkOrderStat();

                    BeanUtils.copyProperties(r,cws);
                    cws.setStopAmount(MapUtil.getFromMap(stopCaseMap,r.getUserId()));
                    cws.setAutoAmount(MapUtil.getFromMap(doneCloseMap,r.getUserId()));
                    cws.setAutoExceptionAmount(MapUtil.getFromMap(doneExceptionMap,r.getUserId()));
                    cws.setCallYes(MapUtil.getFromMap(yesMap,r.getUserId()));
                    cws.setCallNo(MapUtil.getFromMap(noMap,r.getUserId()));

                    return cws;

                }).collect(Collectors.toList());
    }



    public List<ShouciWorkOrderStat> getOnlyOne(int oneLevel) {

        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        //停止筹款
        int stopNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.stop_case,today);

        //自动关闭
        int doneCloseNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.done,today);

        //自动异常关闭
        int doneExceptionNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.exception_done,today);

        //呼通
        List<WorkOrderStat> yesList = workOrderStatDao.getExtStat(twoList,OrderExtName.CallStatus.getName(),"1",today);
        int yesNum =yesList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();

        //未呼通
        List<WorkOrderStat> noList = workOrderStatDao.getExtStat(twoList,OrderExtName.CallStatus.getName(),"2",today);
        int noNum =noList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();


        ShouciWorkOrderStat s = new ShouciWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setStopAmount(stopNum);
        s.setAutoAmount(doneCloseNum);
        s.setAutoExceptionAmount(doneExceptionNum);
        s.setCallYes(yesNum);
        s.setCallNo(noNum);

        return Lists.newArrayList(s);
    }


    public List<ShouciWorkOrderStat> getTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        if (twoLevel.size()>1){
            return getAllTwoAndUser(oneLevel,twoLevel,userId);
        }

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getTwoAndUser(oneLevel,twoLevel.get(0),users,doneList);

        //停止筹款
        Map<Long,Integer> stopCaseMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.stop_case,today);

        //自动关闭
        Map<Long,Integer> doneCloseMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.done,today);

        //自动异常关闭
        Map<Long,Integer> doneExceptionMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //呼通
        List<WorkOrderStat> yesList = workOrderStatDao.getExtUserStat(twoList,users,OrderExtName.CallStatus.getName(),"1",today);
        Map<Long,Integer> yesMap = yesList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //未呼通
        List<WorkOrderStat> noList = workOrderStatDao.getExtUserStat(twoList,users,OrderExtName.CallStatus.getName(),"2",today);
        Map<Long,Integer> noMap = noList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return list.stream()
                .map(r->{
                    ShouciWorkOrderStat s = new ShouciWorkOrderStat();

                    BeanUtils.copyProperties(r,s);

                    s.setStopAmount(MapUtil.getFromMap(stopCaseMap,r.getUserId()));
                    s.setAutoAmount(MapUtil.getFromMap(doneCloseMap,r.getUserId()));
                    s.setAutoExceptionAmount(MapUtil.getFromMap(doneExceptionMap,r.getUserId()));
                    s.setCallYes(MapUtil.getFromMap(yesMap,r.getUserId()));
                    s.setCallNo(MapUtil.getFromMap(noMap,r.getUserId()));

                    return s;

                }).collect(Collectors.toList());
    }



    public List<ShouciWorkOrderStat> getOnlyTwo(int one ,List<Integer> twoList) {

        String today = LocalDate.now() + " 00:00:00";

        List<WorkOrderStat> stats = baseStatService.getTwoALL(one,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        //停止筹款
        Map<Integer,Integer> stopMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.stop_case,today);

        //自动关闭
        Map<Integer,Integer> doneCloseMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.done,today);

        //自动异常关闭
        Map<Integer,Integer> doneExceptionMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //呼通
        List<WorkOrderStat> yesList = workOrderStatDao.getExtStat(twoList,OrderExtName.CallStatus.getName(),"1",today);
        Map<Integer,Integer> yesMap = yesList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), WorkOrderStat::getQueryNum));

        //未呼通
        List<WorkOrderStat> noList = workOrderStatDao.getExtStat(twoList,OrderExtName.CallStatus.getName(),"2",today);
        Map<Integer,Integer> noMap = noList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), WorkOrderStat::getQueryNum));

        return stats.stream()
                .map(r->{
                    ShouciWorkOrderStat s = new ShouciWorkOrderStat();

                    BeanUtils.copyProperties(r,s);

                    s.setStopAmount(stopMap.get(s.getTwoLevel())== null?0:stopMap.get(s.getTwoLevel()));
                    s.setAutoAmount(doneCloseMap.get(s.getTwoLevel())==null?0:doneCloseMap.get(s.getTwoLevel()));
                    s.setAutoExceptionAmount(doneExceptionMap.get(s.getTwoLevel())==null?0:doneExceptionMap.get(s.getTwoLevel()));
                    s.setCallYes(yesMap.get(s.getTwoLevel())==null?0:yesMap.get(s.getTwoLevel()));
                    s.setCallNo(noMap.get(s.getTwoLevel())==null?0:noMap.get(s.getTwoLevel()));

                    return s;

                }).collect(Collectors.toList());
    }



    public List<ShouciWorkOrderStat> getAllTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        List<WorkOrderStat> list = baseStatService.getAllTwoAndUser(oneLevel,twoLevel,userId,doneList);

        //停止筹款
        Map<Integer,Integer> stopCaseMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.stop_case,today);

        //自动关闭
        Map<Integer,Integer> doneCloseMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.done,today);

        //自动异常关闭
        Map<Integer,Integer> doneExceptionMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //呼通
        List<WorkOrderStat> yesList = workOrderStatDao.getExtTypeStat(twoList,userId,OrderExtName.CallStatus.getName(),"1",today);
        Map<Integer,Integer> yesMap = yesList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //未呼通
        List<WorkOrderStat> noList = workOrderStatDao.getExtTypeStat(twoList,userId,OrderExtName.CallStatus.getName(),"2",today);
        Map<Integer,Integer> noMap = noList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return list.stream()
                .map(r->{
                    ShouciWorkOrderStat s = new ShouciWorkOrderStat();

                    BeanUtils.copyProperties(r,s);

                    s.setStopAmount(MapUtil.getFromMap(stopCaseMap,r.getTwoLevel()));
                    s.setAutoAmount(MapUtil.getFromMap(doneCloseMap,r.getTwoLevel()));
                    s.setAutoExceptionAmount(MapUtil.getFromMap(doneExceptionMap,r.getTwoLevel()));
                    s.setCallYes(MapUtil.getFromMap(yesMap,r.getTwoLevel()));
                    s.setCallNo(MapUtil.getFromMap(noMap,r.getTwoLevel()));

                    return s;

                }).collect(Collectors.toList());
    }

}
