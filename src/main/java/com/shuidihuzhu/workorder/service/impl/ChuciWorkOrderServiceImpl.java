package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.ChuciWorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/5/17
 */
@Service("chuciWorkOrderService")
@Slf4j
public class ChuciWorkOrderServiceImpl extends WorkOrderFacade<ChuciWorkOrder, ChuciHandleOrderParam, WorkOrderListParam> {


    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private ChuciWorkOrderDao chuciWorkOrderDao;

    @Autowired
    private WorkTypePropertyService orderTypePropertyService;

    @Autowired
    private AssignGroupService assignGroupService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    private static List<Integer> chuShenWorkList = Lists.newArrayList(
            WorkOrderType.shenhe.getType(),
            WorkOrderType.huifang.getType(),
            WorkOrderType.dianhuashenhe.getType(),
            WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(),
            WorkOrderType.ai_photo.getType(),
            WorkOrderType.ai_content.getType(),
            WorkOrderType.ai_erci.getType()
    );

    private static List<Integer> undoChushenWorkList = Lists.newArrayList(
            WorkOrderType.shenhe.getType(),
            WorkOrderType.dianhuashenhe.getType(),
            WorkOrderType.yiliaoshenhe.getType(),
            WorkOrderType.highriskshenhe.getType(),
            WorkOrderType.ai_erci.getType()
    );

    @Override
    public OpResult vlidate(ChuciWorkOrder wordOrder) {

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                undoChushenWorkList, HandleResultEnum.unDoResult());

        if (workOrderBase != null ){
            log.info("chuci vlidate caseId={} repeat workOrderBase:{}", wordOrder.getCaseId(), workOrderBase);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }

        int orderType = wordOrder.getOrderType();
        if (orderType == WorkOrderType.ai_photo.getType() || orderType == WorkOrderType.ai_content.getType()){
            workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(), Lists.newArrayList(orderType), HandleResultEnum.unDoResult());
            if (workOrderBase != null){
                log.info("chuci vlidate caseId={} repeat workOrderBase:{}", wordOrder.getCaseId(), workOrderBase);
                return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
            }
        }
        // 高风险工单继承上次工单的分配组
        if (orderType == WorkOrderType.highriskshenhe.getType()) {
            WorkOrderBase lastOrder = workOrderDao.getLastByCaseIdAndOrderType(wordOrder.getCaseId(), orderType);
            if (lastOrder != null) {
                wordOrder.setAssignGroupId(lastOrder.getAssignGroupId());
            }
            if (wordOrder.getAssignGroupId() <= 0) {
                long groupId = assignGroupService.getGroupIdByOrderTypeAndPermission(orderType, "von-assign-group:high-risk-normal");
                wordOrder.setAssignGroupId(groupId);
            }
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(ChuciWorkOrder wordOrder) {

        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(wordOrder.getCaseId()));

        wordOrder.setOrderlevel(OrderLevel.edium.getType());

        if (feignResponse != null && feignResponse.ok()) {

            List<CrowdfundingInfo> list = feignResponse.getData();
            if (CollectionUtils.isNotEmpty(list)){
                CrowdfundingInfo cf = list.get(0);
                //医院信息工单创建  不检查案例结束状态
                if (cf.getEndTime().before(new Date()) && WorkOrderType.bu_chong_yi_yuan_xin_xi.getType() != wordOrder.getOrderType()){
                    log.info("chuciWorkOrder caseId={} end ",cf.getId());
                    return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_END_ERROR);
                }

                ChannelRefineDTO refineDTO = new ChannelRefineDTO();
                refineDTO.setUserId(cf.getUserId());
                refineDTO.setInfoId(Long.valueOf(cf.getId()));
                refineDTO.setChannel(cf.getChannel());

                Map<Integer,ChannelRefine.ChannelRefineResuleEnum> m = baseWorkOrderService.getChannel(Lists.newArrayList(cf));

                if (ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.equals(m.get(cf.getId()))){
                    wordOrder.setOrderlevel(OrderLevel.high.getType());
                }

                if (WorkOrderType.ai_erci.getType() == wordOrder.getOrderType()) {
                    if (ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.equals(m.get(cf.getId()))){
                        wordOrder.setOrderlevel(OrderLevel.B.getType());
                    }
                    if (ChannelRefine.ChannelRefineResuleEnum.WEIXIN_1V1.equals(m.get(cf.getId()))){
                        wordOrder.setOrderlevel(OrderLevel.C.getType());
                    }
                    if (ChannelRefine.ChannelRefineResuleEnum.YONGHU_ZIZHU.equals(m.get(cf.getId()))){
                        wordOrder.setOrderlevel(OrderLevel.D.getType());
                    }
                }
            }

        }
        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(ChuciHandleOrderParam param) {

        //如果审核和回访工单和电话审核稍后处理判断一下数量
        if (chuShenWorkList.contains(param.getOrderType())
                && param.getHandleResult() == HandleResultEnum.later_doing.getType()){

            int count = baseWorkOrderService.getCountByHandleResult(param.getUserId(),param.getOrderType(), param.getHandleResult() +"");

            if (count >= orderTypePropertyService.getMaxDelayCount(param.getOrderType())){
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }

        int result = workOrderDao.handle(param.getWorkOrderId(), param.getHandleResult());

        Map<Integer,Long> map = Maps.newHashMap();

        //更新处理结果
        if (result > 0){
            long workOrderId = param.getWorkOrderId();
            orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.OperComment, param.getOperComment());

            orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.CallStatus, param.getCallStatus());

            orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.userCallStatus, param.getUserCallStatus());

            return OpResult.createSucResult(map);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    @Override
    public OpResult<PageResult> getOrderList(WorkOrderListParam param) {

        if (param.getUserId() == 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize()+1;

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).map(Integer::valueOf).collect(Collectors.toList());

        String forceIndex = "";
        if (StringUtils.isNotBlank(param.getStartTime())
                && StringUtils.isNotBlank(param.getEndTime())
                && param.getWorkOrderId() > 0 && param.getCaseId() == 0){
            forceIndex = "  force index(idx_finish_time) ";
        }
        List<WorkOrderBase> list = chuciWorkOrderDao.getWorkorderList(Lists.newArrayList(param.getUserId()),param.getOrderType(),results,
               p,param.getWorkOrderId(),param.getPaging(),param.getCaseId(),param.getStartTime(),
               param.getEndTime(),param.getOrderLevel(),forceIndex);

        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(r->r.getCaseId()).distinct().collect(Collectors.toList());
        //查询案例
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(caseIds);

        if (feignResponse == null || feignResponse.notOk()){
           return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        List<CrowdfundingInfo> caseList = feignResponse.getData();

        Map<Integer,CrowdfundingInfo> map = caseList.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));


        Map<Integer,ChannelRefine.ChannelRefineResuleEnum> recordMap =baseWorkOrderService.getChannel(caseList);

        List<Long> workOrderId = list.stream().map(r->r.getId()).collect(Collectors.toList());
        List<WorkOrderExt> extList = orderExtService.getWorkOrderExts(workOrderId,OrderExtName.CallStatus.getName());
        Map<Long,WorkOrderExt> extMap = extList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,Function.identity()));

        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{

            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUserId(c.getUserId());
            workOrderVO.setCaseUuid(c.getInfoId());

            ChannelRefine.ChannelRefineResuleEnum resuleEnum = recordMap.get(r.getCaseId());

            if (resuleEnum != null){
                workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
                workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
            }

            WorkOrderExt ext = extMap.get(r.getId());
            if(ext != null){
                workOrderVO.setCallStatus(ext.getExtValue());
            }

            return workOrderVO;

        }).collect(Collectors.toList());

        //如果是处理中和稍后处理   按照优先级排序一下
        if (results.contains(HandleResultEnum.doing.getType()) ||
                results.contains(HandleResultEnum.later_doing.getType())){
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId)).collect(Collectors.toList());
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getOrderLevel).reversed()).collect(Collectors.toList());
        }

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }


}

