package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.client.feign.CfUserStatFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/5/7
 */
@Service("juanzhuanWorkOrderService")
@Slf4j
public class JuanzhuanWorkOrderServiceImpl extends WorkOrderFacade<JuanzhuanWorkOrder, JuanzhuanHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserOperationRecordDao recordDao;


    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private WorkTypePropertyService orderTypePropertyService;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private CfUserStatFeignClient cfUserStatFeignClient;

    @Autowired
    private WorkOrderDaoExt workOrderDaoExt;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult vlidate(JuanzhuanWorkOrder wordOrder) {

        List<Integer> types = workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType());

        Set<Integer> integers = types.stream().collect(Collectors.toSet());

        if (!integers.contains(wordOrder.getOrderType())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        //d0和特种兵d0是一个工单
        List<Integer> orderTypes = Lists.newArrayList(wordOrder.getOrderType());
        if (wordOrder.getOrderType() == WorkOrderType.d0_1v1_tzb.getType()
                || wordOrder.getOrderType() == WorkOrderType.d0_1v1.getType() ){
            orderTypes = Lists.newArrayList(WorkOrderType.d0_1v1_tzb.getType(),WorkOrderType.d0_1v1.getType());
        }

        //如果工单已经生成了  不再重复生成
        List<WorkOrderVO> orderList = baseWorkOrderService.queryByCaseAndTypes(wordOrder.getCaseId(),orderTypes);
        Optional optional = orderList.stream().filter(r -> r.getShowName().equals(wordOrder.getShowName())).findAny();
        if (optional.isPresent()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }



        WorkOrderVO last = baseWorkOrderService.getLastByCaseIdAndOrderTypes(wordOrder.getCaseId(),workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType()));
        if (Optional.ofNullable(last).map(WorkOrderVO::getLabel).filter(StringUtils::isNotEmpty).filter(r->r.contains("放弃跟进")).isPresent()){
            log.info("last={}",last);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }
        //d1第一次工单  需要判断  d0第一次工单是否处理完成
        if (wordOrder.getOrderType() == WorkOrderType.d1_1v1.getType() && JuanzhuanWorkOrder.d1_show_name_1.equals(wordOrder.getShowName())){

            List<WorkOrderVO> list = baseWorkOrderService.queryByCaseAndTypes(wordOrder.getCaseId(),Lists.newArrayList(WorkOrderType.d0_1v1.getType(),WorkOrderType.d0_1v1_tzb.getType()));

            Optional o = list.stream().filter(r -> JuanzhuanWorkOrder.d0_show_name_1.equals(r.getShowName()))
                                .filter(r -> r.getHandleResult() == HandleResultEnum.done.getType() || r.getHandleResult() == HandleResultEnum.exception_done.getType()).findAny();

            if (!o.isPresent()){
                return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
            }
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(JuanzhuanWorkOrder wordOrder) {

        //创建之前关闭之前未处理的订单
        closeWorkOrder(wordOrder.getCaseId(),"未拨打关闭");

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }


    public void closeWorkOrder(int caseId,String comment){

        List<Integer> results = HandleResultEnum.unDoResult();

        List<WorkOrderBase> list = workOrderDao.listByCaseIdAndTypeAndResult(caseId,workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType()),results);

        if (CollectionUtils.isEmpty(list)){
            return ;
        }
        FeignResponse<CfInfoStat> response = cfUserStatFeignClient.getCfInfoStatById(caseId);
        CfInfoStat stat = Optional.ofNullable(response).filter(FeignResponse::ok)
                .map(FeignResponse::getData).orElse(null);
        list.stream().forEach(r->{
            JuanzhuanHandleOrderParam param = new JuanzhuanHandleOrderParam();
            param.setWorkOrderId(r.getId());
            param.setOrderType(r.getOrderType());
            param.setHandleResult(HandleResultEnum.exception_done.getType());
            param.setOperComment(comment);
            if (stat!=null){
                param.setAmount(stat.getAmount()+"");
                param.setDonationCount(stat.getDonationCount()+"");
                param.setShareCount(stat.getShareCount()+"");
            }
            int close = workOrderDao.closeOrderById(Lists.newArrayList(r.getId()),param.getHandleResult());
            if (close > 0){

                orderExtService.createWorkOrderExt(param.getWorkOrderExt());

                WorkOrderRecord workOrderRecord = new WorkOrderRecord();
                workOrderRecord.setWorkOrderType(r.getOrderType());
                workOrderRecord.setOperatorId(0);
                workOrderRecord.setComment(param.getOperComment());
                workOrderRecord.setOperateMode(OperateMode.handle.getType());
                workOrderRecord.setOperateDesc(OperateMode.handle.getMsg());
                workOrderRecord.setWorkOrderId(r.getId());

                recordDao.saveRecord(workOrderRecord);
            }
        });
    }

    @Override
    public OpResult handle(JuanzhuanHandleOrderParam param) {

        //如果稍后处理判断一下数量
        if (param.getHandleResult() == HandleResultEnum.later_doing.getType()){

            int count = baseWorkOrderService.getCountByHandleResult(param.getUserId(),param.getOrderType(), param.getHandleResult() +"");

            if (count >= orderTypePropertyService.getMaxDelayCount(param.getOrderType())){
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }

        int result = workOrderDao.handle(param.getWorkOrderId(), param.getHandleResult());

        //仅处理完成才保存记录
        if (result > 0 && (param.getHandleResult() == HandleResultEnum.done.getType())){
            orderExtService.createWorkOrderExt(param.getWorkOrderExt());
        }

        if (param.getHandleResult() == HandleResultEnum.later_doing.getType()) {
            List<WorkOrderExt> workOrderExtList = Optional.ofNullable(param.getWorkOrderExt()).orElse(Lists.newArrayList());
            Set<String> extName = workOrderExtList.stream().map(WorkOrderExt::getExtName).collect(Collectors.toSet());
            Set<String> needContainsExtName = Sets.newHashSet(OrderExtName.CallStatus.getName(), OrderExtName.label.getName(), OrderExtName.OperComment.getName());
            Sets.difference(needContainsExtName, extName).forEach(item -> {
                WorkOrderExt workOrderExt = new WorkOrderExt();
                workOrderExt.setWorkOrderId(param.getWorkOrderId());
                workOrderExt.setExtName(item);
                workOrderExt.setExtValue("");
                log.info("填充ext:{}信息", item);
                workOrderExtList.add(workOrderExt);});
            orderExtService.createWorkOrderExt(workOrderExtList);
        }

        return OpResult.createSucResult(result);
    }

    @Override
    public OpResult getOrderList(WorkOrderListParam param) {

        if (param.getUserId() == 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = param.getPageSize()+1;


        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(param.getUserId()), param.getOrderType(),results,p,param.getWorkOrderId(),
                param.getPaging(),param.getCaseId(),param.getStartTime(),param.getEndTime(),param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(r->r.getCaseId()).collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        List<Long> workOrderId = list.stream().map(r->r.getId()).collect(Collectors.toList());
        List<WorkOrderExt> extList = orderExtService.getWorkOrderExts(workOrderId, OrderExtName.showName.getName());
        Map<Long,WorkOrderExt> extMap = extList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,Function.identity()));

        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setOrderType(r.getOrderType());

            CrowdfundingInfo c = map.get(r.getCaseId());
            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUuid(c.getInfoId());

            WorkOrderExt ext = extMap.get(workOrderVO.getWorkOrderId());
            if (ext != null){
                workOrderVO.setShowName(ext.getExtValue());
            }

            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }



    public Map<Integer,WorkOrderVO> getD0ShareAndDonation(List<Integer> caseIds){

        Map<Integer,WorkOrderVO> result = Maps.newHashMap();

        if (CollectionUtils.isEmpty(caseIds)){
            return result;
        }
        List<WorkOrderBase> orders = workOrderDao.listByCaseIdsAndTypes(caseIds,Lists.newArrayList(WorkOrderType.d0_1v1.getType(),WorkOrderType.d0_1v1_tzb.getType()));

        if (CollectionUtils.isEmpty(orders)){
            return result;
        }

        List<Long> workOrderIds = orders.stream().map(WorkOrderBase::getId).distinct().collect(Collectors.toList());

        Map<Integer,Long> map = orders.stream().collect(Collectors.toMap(WorkOrderBase::getCaseId,WorkOrderBase::getId,(o1,o2)->o2));

        Map<Long,WorkOrderExt> shareMap =  getExtMap(workOrderIds,OrderExtName.shareCount.getName());

        Map<Long,WorkOrderExt> donationMap =  getExtMap(workOrderIds,OrderExtName.donationCount.getName());

        caseIds.stream().forEach(r->{

            WorkOrderVO vo = new WorkOrderVO();
            Long workOrderId = map.get(r);
            if (workOrderId != null){
                vo.setShareCount(Optional.ofNullable(shareMap.get(workOrderId)).map(WorkOrderExt::getExtValue).map(Integer::valueOf).orElse(0));
                vo.setDonationCount(Optional.ofNullable(donationMap.get(workOrderId)).map(WorkOrderExt::getExtValue).map(Integer::valueOf).orElse(0));
            }
            result.put(r,vo);
        });

        return result;
    }

    public List<WorkOrderBaseVo> getJuanzhuanOrders(int orderType, int handleResult){

        List<WorkOrderBaseVo> workOrderBases = workOrderDao.queryUndoOrderOrderByLevel(orderType, handleResult);

        if (CollectionUtils.isEmpty(workOrderBases)){
            return Lists.newArrayList();
        }

        List<Long> orderIds =  workOrderBases.stream().map(WorkOrderBaseVo::getId).distinct().collect(Collectors.toList());

        List<WorkOrderExt> exts = workOrderDaoExt.getWorkOrderExts(orderIds,OrderExtName.orderExtType.getName());
        if (CollectionUtils.isNotEmpty(exts)){
            Map<Long,String> maps = exts.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,WorkOrderExt::getExtValue));
            workOrderBases.stream().forEach(r->{
                r.setOrderExtType(Optional.ofNullable(maps.get(r.getId())).orElse(JuanzhuanWorkOrder.ext_type_common));
            });
        }
        return workOrderBases;
    }

    private Map<Long,WorkOrderExt> getExtMap(List<Long> workOrderIds,String name){

        List<WorkOrderExt> shareList =  orderExtService.getWorkOrderExts(workOrderIds,name);

        if (CollectionUtils.isEmpty(shareList)){
            return Maps.newHashMap();
        }

        return shareList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,Function.identity(),(o1,o2)->o2));
    }
}
