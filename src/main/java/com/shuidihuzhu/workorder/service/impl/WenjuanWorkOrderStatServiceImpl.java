package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WenjuanWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/11
 */
@Service("wenjuanWorkOrderStatService")
public class WenjuanWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<WenjuanWorkOrderStat> implements WorkOrderStatService<WenjuanWorkOrderStat> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.manual_lock.getType(),
            HandleResultEnum.exception_done.getType());


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<WenjuanWorkOrderStat> getOnlyOneLevel(int oneLevel) {
        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        int doneNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.done,today);

        int lockNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.manual_lock,today);

        int num = baseStatService.getStatusInt(twoList,users,HandleResultEnum.exception_done,today);

        WenjuanWorkOrderStat s = new WenjuanWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setDoneAmount(doneNum);
        s.setAutoExceptionAmount(num);
        s.setManualLockAmount(lockNum);

        return Lists.newArrayList(s);
    }

    @Override
    public List<WenjuanWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer,Map<Integer,Integer>> result = baseStatService.getTypeStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{

                    WenjuanWorkOrderStat c = new WenjuanWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setDoneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getTwoLevel()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getTwoLevel()));
                    c.setManualLockAmount(MapUtil.getFromMap(result.get(HandleResultEnum.manual_lock.getType()),r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<WenjuanWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Long,Integer>> result = baseStatService.getUserStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{
                    WenjuanWorkOrderStat c = new WenjuanWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setDoneAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getUserId()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getUserId()));
                    c.setManualLockAmount(MapUtil.getFromMap(result.get(HandleResultEnum.manual_lock.getType()),r.getUserId()));

                    return c;

                }).collect(Collectors.toList());
    }

}
