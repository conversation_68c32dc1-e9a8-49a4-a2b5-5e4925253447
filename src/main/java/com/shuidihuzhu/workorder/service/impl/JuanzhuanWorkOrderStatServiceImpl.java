package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.model.JuanzhanWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.StaffRealTimeWorkData;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/5/9
 */
@Service("juanzhuanWorkOrderStatService")
public class JuanzhuanWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<JuanzhanWorkOrderStat>{

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType());

    public OpResult<List<JuanzhanWorkOrderStat>> getWorkOrderStatList(int one, String two, long userId,int statffStatus){

        if (one<=0){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //如果二级没选
        if (StringUtils.isEmpty(two)){
            return getOneLevel(one,userId);
        }else {
            List<Integer> twoList = Arrays.stream(two.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            return getTwoLevel(one,twoList,userId,statffStatus);
        }
    }

    private OpResult<List<JuanzhanWorkOrderStat>> getTwoLevel(int oneLevel,List<Integer> twoLevel ,long userId,int statffStatus) {
        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getTwoAndUser(oneLevel,twoLevel,userId,statffStatus));
        }else {
            return OpResult.createSucResult(getOnlyTwo(oneLevel,twoLevel));
        }
    }

    private List<JuanzhanWorkOrderStat> getTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId,int staffStatus){

        if (twoLevel.size()>1){
            return getAllTwoAndUser(oneLevel,twoLevel,userId);
        }

        List<Integer> doneList = getDoneList();

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypesAndStatus(twoList,staffStatus).stream().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getTwoAndUser(oneLevel,twoLevel.get(0),users,doneList);

        return getUserStat(list,users,twoList);
    }


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<JuanzhanWorkOrderStat> getOnlyOneLevel(int oneLevel) {
        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        int doneNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.done,today);

        int num = baseStatService.getStatusInt(twoList,users,HandleResultEnum.exception_done,today);

        JuanzhanWorkOrderStat s = new JuanzhanWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setSubmitAmount(doneNum);
        s.setAutoExceptionAmount(num);

        return Lists.newArrayList(s);
    }

    @Override
    public List<JuanzhanWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Integer,Integer>> result = baseStatService.getTypeStatBase(users,twoList,doneList);

        Map<String,StaffStatus> userMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(users)){
            List<StaffStatus> l = staffStatusDao.getStaffs(users,twoList);
            userMap.putAll(l.stream().collect(Collectors.toMap(r->r.getUserId()+"_"+r.getOrderType(), Function.identity())));
        }
        return list.stream()
                .map(r->{
                    JuanzhanWorkOrderStat c = new JuanzhanWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setSubmitAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getTwoLevel()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getTwoLevel()));
                    c.setTotalAmount(c.getDoneAmount()+c.getDoingAmount()+c.getLaterAmount());

                    StaffStatus ss = userMap.get(r.getUserId()+"_"+r.getTwoLevel());
                    c.setStaffStatus(Optional.ofNullable(ss).map(StaffStatus::getStaffStatus).orElse(-1));

                    if (users.size() == 1){
                        List<StaffRealTimeWorkData> staffList = baseWorkOrderService.listUnHandleWork(r.getUserId(),twoList);
                        Map<Integer,Integer> map = staffList.stream().collect(Collectors.toMap(StaffRealTimeWorkData::getOrderType,StaffRealTimeWorkData::getPersonalUnHandle));
                        int count = Optional.ofNullable(map.get(r.getTwoLevel())).orElse(0);
                        c.setToBeAssignedAmount(count);
                    }

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<JuanzhanWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Long,Integer>> result = baseStatService.getUserStatBase(users,twoList,doneList);

        Map<String,StaffStatus> userMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(users)){
            List<StaffStatus> l = staffStatusDao.getStaffs(users,twoList);
            userMap.putAll(l.stream().collect(Collectors.toMap(r->r.getUserId()+"_"+r.getOrderType(), Function.identity())));
        }

        return list.stream()
                .map(r->{
                    JuanzhanWorkOrderStat c = new JuanzhanWorkOrderStat();
                    BeanUtils.copyProperties(r,c);
                    c.setSubmitAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getUserId()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getUserId()));
                    c.setTotalAmount(c.getDoneAmount()+c.getDoingAmount()+c.getLaterAmount());

                    StaffStatus ss = userMap.get(r.getUserId()+"_"+r.getTwoLevel());
                    c.setStaffStatus(Optional.ofNullable(ss).map(StaffStatus::getStaffStatus).orElse(-1));

                    if (users.size() == 1){

                        List<StaffRealTimeWorkData> staffList = baseWorkOrderService.listUnHandleWork(r.getUserId(),twoList);
                        Map<Integer,Integer> map = staffList.stream().collect(Collectors.toMap(StaffRealTimeWorkData::getOrderType,StaffRealTimeWorkData::getPersonalUnHandle));
                        int count = twoList.stream().filter( k->map.get(k) != null).collect(Collectors.summingInt(u -> map.get(u)));
                        c.setToBeAssignedAmount(count);
                    }

                    return c;

                }).collect(Collectors.toList());
    }
}
