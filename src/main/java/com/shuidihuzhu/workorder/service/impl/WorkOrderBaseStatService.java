package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderStatDao;
import com.shuidihuzhu.workorder.repository.WorkOrderStatRepository;
import com.shuidihuzhu.workorder.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/3/29
 */
@Service
@Slf4j
public class WorkOrderBaseStatService {

    @Autowired
    private WorkOrderStatDao workOrderStatDao;

    @Autowired
    private UserFeignClient userFeignClient;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Autowired
    private WorkOrderStatRepository workOrderStatRepository;

    //未分配工单量、处理中工单量
    private static List<Integer> remainingLists = Lists.newArrayList(HandleResultEnum.undoing.getType(),HandleResultEnum.doing.getType());


    public List<WorkOrderStat> getOneAndUser(int one,List<Integer> twoList , List<Long> users,List<Integer> doneList){

        String today = LocalDate.now() + " 00:00:00";
        String tomorrow = LocalDate.now().plusDays(1) + " 00:00:00";

        String history = LocalDate.now().plusDays(-7) + " 00:00:00";

        Response<List<AuthUserDto>> rpcResponse = userFeignClient.getAuthUserByIds(users);
        final Map<Integer,String> userMap = Maps.newHashMap();
        if (rpcResponse!=null && rpcResponse.ok()){
            List<AuthUserDto> userList = rpcResponse.getData();
            Map<Long, String> map = userList.stream().collect(Collectors.toMap(AuthUserDto::getUserId, AuthUserDto::getUserName));
            for (Map.Entry<Long, String> entry : map.entrySet()) {
                userMap.put(entry.getKey().intValue(), entry.getValue());
            }
        }

        List<WorkOrderStat> doingList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.doing.getType(),today);
        Map<Long,Integer> doingMap = doingList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> laterList = workOrderStatDao.getLaterStat(twoList,users,today,tomorrow);
        Map<Long,Integer> laterMap =laterList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> historylaterDoingList = workOrderStatDao.getLaterStat(twoList,users,history,today);
        Map<Long,Integer> historyLaterMap =historylaterDoingList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return users.stream().
                map(u->{
                    WorkOrderStat wos = new WorkOrderStat();
                    wos.setOneLevel(one);
                    wos.setUserId(u);
                    if (userMap.get(Long.valueOf(u).intValue()) != null){
                        wos.setUserName(userMap.get(Long.valueOf(u).intValue()));
                    }
                    if(doingMap.get(u)!=null){
                        wos.setDoingAmount(doingMap.get(u));
                    }

                    if(laterMap.get(u)!=null){
                        wos.setLaterAmount(laterMap.get(u));
                    }

                    if (historyLaterMap.get(u)!=null){
                        wos.setHistoryLaterAmount(historyLaterMap.get(u));
                    }

                    int doneAmount = workOrderStatRepository.getResultAmount(twoList, doneList, today,u);
                    wos.setDoneAmount(doneAmount);

                    return wos;
                }).collect(Collectors.toList());

    }


    public WorkOrderStat getOneALL(Integer oneLevel,List<Integer> types,List<Integer> doneList) {

        String today = LocalDate.now() + " 00:00:00";
        String tomorrow = LocalDate.now().plusDays(1) + " 00:00:00";

        String history = LocalDate.now().plusDays(-7) + " 00:00:00";

        WorkOrderStat wos = new WorkOrderStat();

        wos.setOneLevel(oneLevel);
        int remainingAmount = workOrderStatDao.getRemainingAmount(types, remainingLists, today);
        wos.setRemainingAmount(remainingAmount);
        int newAmount = workOrderStatDao.getNewAmount(types, today);
        wos.setNewAmount(newAmount);
        int undoingAmount = workOrderStatDao.getUndoingAmount(types);
        wos.setUndoingAmount(undoingAmount);
        List<WorkOrderStat> doingList = workOrderStatDao.getResultStat(types, Lists.newArrayList(), HandleResultEnum.doing.getType(),today);
        long doing = doingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
        wos.setDoingAmount(Long.valueOf(doing).intValue());
        List<WorkOrderStat> laterDoingList = workOrderStatDao.getLaterStat(types, Lists.newArrayList(),today,tomorrow);
        long laterDoing = laterDoingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
        wos.setLaterAmount(Long.valueOf(laterDoing).intValue());
        List<WorkOrderStat> historylaterDoingList = workOrderStatDao.getLaterStat(types, Lists.newArrayList(),history,today);
        int historyLaterAmount = historylaterDoingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
        wos.setHistoryLaterAmount(historyLaterAmount);
        int doneAmount = workOrderStatRepository.getResultAmount(types, doneList, today,0);
        wos.setDoneAmount(doneAmount);

        return wos;

    }


    public List<WorkOrderStat> getTwoAndUser(int oneLevel,int twoLevel, List<Long> users,List<Integer> doneList){

        String today = LocalDate.now() + " 00:00:00";
        String tomorrow = LocalDate.now().plusDays(1) + " 00:00:00";

        String history = LocalDate.now().plusDays(-7) + " 00:00:00";

        Response<List<AuthUserDto>> rpcResponse = userFeignClient.getAuthUserByIds(users);
        final Map<Integer,String> userMap = Maps.newHashMap();
        if (rpcResponse!=null && rpcResponse.ok()){
            List<AuthUserDto> userList = rpcResponse.getData();
            Map<Long, String> map = userList.stream().collect(Collectors.toMap(AuthUserDto::getUserId, AuthUserDto::getUserName));
            for (Map.Entry<Long, String> entry : map.entrySet()) {
                userMap.put(entry.getKey().intValue(), entry.getValue());
            }
        }
        //只能查询一个二级分类  所以按照人员分组就可以了
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        List<WorkOrderStat> doingList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.doing.getType(),today);
        Map<Long,Integer> doingMap = doingList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> laterList = workOrderStatDao.getLaterStat(twoList, users,today,tomorrow);
        Map<Long,Integer> laterMap =laterList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> historylaterDoingList = workOrderStatDao.getLaterStat(twoList,users,history,today);
        Map<Long,Integer> historyLaterMap =historylaterDoingList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return users.stream().
                map(u->{
                    WorkOrderStat wos = new WorkOrderStat();
                    wos.setOneLevel(oneLevel);
                    wos.setTwoLevel(twoLevel);
                    wos.setUserId(u);
                    if (userMap.get(Long.valueOf(u).intValue()) != null){
                        wos.setUserName(userMap.get(Long.valueOf(u).intValue()));
                    }
                    if(doingMap.get(u)!=null){
                        wos.setDoingAmount(doingMap.get(u));
                    }

                    if(laterMap.get(u)!=null){
                        wos.setLaterAmount(laterMap.get(u));
                    }

                    if (historyLaterMap.get(u)!=null){
                        wos.setHistoryLaterAmount(historyLaterMap.get(u));
                    }

                    int doneAmount = workOrderStatRepository.getResultAmount(twoList, doneList, today,u);
                    wos.setDoneAmount(doneAmount);

                    return wos;
                }).collect(Collectors.toList());
    }



    public List<WorkOrderStat> getAllTwoAndUser(int oneLevel,List<Integer> twoLevel, long user,List<Integer> doneList){

        String today = LocalDate.now() + " 00:00:00";

        //只能查询一个人  所以按照类型分组就可以了
        List<Long>  users = Lists.newArrayList(user);

        Response<List<AuthUserDto>> rpcResponse = userFeignClient.getAuthUserByIds(users);
        final Map<Integer,String> userMap = Maps.newHashMap();
        if (rpcResponse!=null && rpcResponse.ok()){
            List<AuthUserDto> userList = rpcResponse.getData();
            Map<Long, String> map = userList.stream().collect(Collectors.toMap(AuthUserDto::getUserId, AuthUserDto::getUserName));
            for (Map.Entry<Long, String> entry : map.entrySet()) {
                userMap.put(entry.getKey().intValue(), entry.getValue());
            }
        }

        List<WorkOrderStat> doingList = workOrderStatDao.getResultStat(twoLevel, users, HandleResultEnum.doing.getType(),today);
        Map<Integer,Integer> doingMap = doingList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> laterList = workOrderStatDao.getResultStat(twoLevel, users, HandleResultEnum.later_doing.getType(),null);
        Map<Integer,Integer> laterMap =laterList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return twoLevel.stream().
                map(l->{
                    WorkOrderStat wos = new WorkOrderStat();
                    wos.setOneLevel(oneLevel);
                    wos.setTwoLevel(l);
                    wos.setUserId(user);

                    if (userMap.get(Long.valueOf(user).intValue()) != null){
                        wos.setUserName(userMap.get(Long.valueOf(user).intValue()));
                    }

                    wos.setDoingAmount(MapUtil.getFromMap(doingMap,l));
                    wos.setLaterAmount(MapUtil.getFromMap(laterMap,l));

                    int doneAmount = workOrderStatRepository.getResultAmount(Lists.newArrayList(l), doneList, today,user);
                    wos.setDoneAmount(doneAmount);

                    return wos;
                }).collect(Collectors.toList());
    }

    public List<WorkOrderStat> getTwoALL(int one ,List<Integer> twoList,List<Integer> doneList) {

        List<WorkOrderStat> result = Lists.newArrayList();

        final String today = LocalDate.now() + " 00:00:00";

        String tomorrow = LocalDate.now().plusDays(1) + " 00:00:00";

        String history = LocalDate.now().plusDays(-7) + " 00:00:00";

        twoList.stream()
                .filter(r->workOrderTypeService.getOneFromTwo(r)>0)
                .forEach(r->{

                        WorkOrderStat wos = new WorkOrderStat();

                        wos.setOneLevel(one);
                        wos.setTwoLevel(r);
                        List<Integer> types = Lists.newArrayList(r);
                        int remainingAmount = workOrderStatDao.getRemainingAmount(types, remainingLists, today);
                        wos.setRemainingAmount(remainingAmount);
                        int newAmount = workOrderStatDao.getNewAmount(types, today);
                        wos.setNewAmount(newAmount);
                        int undoingAmount = workOrderStatDao.getUndoingAmount(types);
                        wos.setUndoingAmount(undoingAmount);
                        List<WorkOrderStat> doingList = workOrderStatDao.getResultStat(types, Lists.newArrayList(), HandleResultEnum.doing.getType(),today);
                        long doing = doingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
                        wos.setDoingAmount(Long.valueOf(doing).intValue());
                        List<WorkOrderStat> laterDoingList = workOrderStatDao.getLaterStat(types, Lists.newArrayList(),today,tomorrow);
                        int laterDoing = laterDoingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
                        wos.setLaterAmount(Long.valueOf(laterDoing).intValue());
                        List<WorkOrderStat> historylaterDoingList = workOrderStatDao.getLaterStat(types, Lists.newArrayList(),history,today);
                        int historyLaterAmount = historylaterDoingList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();
                        wos.setHistoryLaterAmount(historyLaterAmount);
                        int doneAmount = workOrderStatRepository.getResultAmount(types, doneList, today,0);
                        wos.setDoneAmount(doneAmount);

                        result.add(wos);
                });

        return result;
    }

    public Map<Integer,Map<Integer,Integer>> getTypeStatBase(List<Long> users, List<Integer> twoList,List<Integer> handleResults){

        String today = LocalDate.now() + " 00:00:00";
        Map<Integer,Map<Integer,Integer>> m = Maps.newHashMap();

        handleResults.stream().forEach(r->{

            Map<Integer,Integer> map = getTypeStatusMap(twoList,users,r,today);
            m.put(r,map);

        });

        return m;
    }

    @Deprecated
    public Map<Integer,Map<Integer,Integer>> getTypeStat(List<Long> users, List<Integer> twoList,List<HandleResultEnum> handleResults){

        String today = LocalDate.now() + " 00:00:00";
        Map<Integer,Map<Integer,Integer>> m = Maps.newHashMap();

        handleResults.stream().forEach(r->{

            Map<Integer,Integer> map = getTypeStatusMap(twoList,users,r,today);
            m.put(r.getType(),map);

        });

        return m;
    }

    public Map<Integer,Map<Long,Integer>> getUserStatBase(List<Long> users,List<Integer> twoList,List<Integer> handleResults){

        String today = LocalDate.now() + " 00:00:00";
        Map<Integer,Map<Long,Integer>> m = Maps.newHashMap();

        handleResults.stream().forEach(r->{

            Map<Long,Integer> map = getStatusMap(twoList,users,r,today);
            m.put(r,map);

        });

        return m;

    }

    @Deprecated
    public  Map<Integer,Map<Long,Integer>> getUserStat(List<Long> users,List<Integer> twoList,List<HandleResultEnum> handleResults){

        String today = LocalDate.now() + " 00:00:00";
        Map<Integer,Map<Long,Integer>> m = Maps.newHashMap();

        handleResults.stream().forEach(r->{

            Map<Long,Integer> map = getStatusMap(twoList,users,r,today);
            m.put(r.getType(),map);

        });

        return m;

    }


    public int getStatusInt(List<Integer> twoList, List<Long> users,HandleResultEnum handleResultEnum, String today){

        List<WorkOrderStat> list = workOrderStatDao.getResultStat(twoList, users, handleResultEnum.getType(),today);
        if (CollectionUtils.isEmpty(list)){
            return 0;
        }
        int num = list.stream().mapToInt(WorkOrderStat::getQueryNum).sum();

        return num;
    }

    public Map<Long,Integer> getStatusMap(List<Integer> twoList, List<Long> users,int handleResult, String today ){

        List<WorkOrderStat> list = workOrderStatDao.getResultStat(twoList, users, handleResult,today);
        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        Map<Long,Integer> map = list.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return map;
    }

    @Deprecated
    public Map<Long,Integer> getStatusMap(List<Integer> twoList, List<Long> users,HandleResultEnum handleResultEnum, String today ){

        List<WorkOrderStat> list = workOrderStatDao.getResultStat(twoList, users, handleResultEnum.getType(),today);
        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        Map<Long,Integer> map = list.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return map;
    }

    public Map<Integer,Integer> getTypeStatusMap(List<Integer> twoList, List<Long> users,int handleResult, String today ){

        List<WorkOrderStat> list = workOrderStatDao.getResultStat(twoList, users, handleResult,today);
        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        Map<Integer,Integer> map = list.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return map;
    }

    @Deprecated
    public Map<Integer,Integer> getTypeStatusMap(List<Integer> twoList, List<Long> users,HandleResultEnum handleResultEnum, String today ){

        List<WorkOrderStat> list = workOrderStatDao.getResultStat(twoList, users, handleResultEnum.getType(),today);
        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }
        Map<Integer,Integer> map = list.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        return map;
    }


}
