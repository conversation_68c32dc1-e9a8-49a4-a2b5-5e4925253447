package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Sets;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.IRequiredCheckDataSourceService;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR>
 * @time 2019/4/4 下午3:01
 * @desc
 */
@Service
public class RequiredCheckDataSourceServiceImpl implements IRequiredCheckDataSourceService {

    private static final String WORK_ORDER = "workOrderDataSource";


    /**
     * 这里面的才是健康检查需要的，其他一律返回up
     * @return
     */
    public static Set<String> requiredDBList(){
        return Sets.newHashSet(WORK_ORDER);
    }

    /**
     * 获取当前系统中直接依赖的reidsson bean NAME
     *
     * 这个方法会会被健康检查用到，只有在set中返回的才会做真正的健康检查
     *
     * @return
     */
    public static Set<String> requiredRedisName() {
        return Sets.newHashSet();
    }


    @Override
    public Set<String> requiredDbDataSource() {
        return requiredDBList();
    }

    @Override
    public Set<String> requiredRedissonDataSource() {
        return requiredRedisName();
    }

}
