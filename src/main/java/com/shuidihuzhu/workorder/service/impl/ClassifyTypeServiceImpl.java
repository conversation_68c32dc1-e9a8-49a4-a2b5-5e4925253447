package com.shuidihuzhu.workorder.service.impl;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.AsyncFunction;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.client.cf.workorder.model.vo.AdminWorkOrderSecType;
import com.shuidihuzhu.client.cf.workorder.model.vo.AdminWorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.model.order.OrderUserHandleResultCount;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.ClassifyTypeService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/3/27
 */
@Service
@Slf4j
public class ClassifyTypeServiceImpl implements ClassifyTypeService {

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private VonPermissionService vonPermissionService;

    @Resource(name = WorkOrderConfig.Async.CLASSIFY_ALL_QUERY)
    private Executor executor;
    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    @Override
    public List<Integer> classifyType(int classifyType) {

        return ClassifyTypeEnum.getByOneLevel(classifyType);
    }

    @Override
    public Map<Integer, List<Integer>> userClassify(long userId) {

        log.info("userClassify userId={}",userId);

        PermissionParam permissionParam = new PermissionParam();
        permissionParam.setPermissions(workOrderTypeService.getAllPermissions());
        permissionParam.setUserId(userId);
        String appCode = AuthSaasContext.getAuthAppCode();
        permissionParam.setAppCode(appCode);
        Set<String> permissions = vonPermissionService.validUserPermissions(permissionParam).getData();

        log.info("userClassify permissions={}",permissions);

        if (CollectionUtils.isEmpty(permissions)){
            return Maps.newHashMap();
        }

        Map<Integer,List<Integer>> map = workOrderTypeService.getClassiyByPermissions(permissions);

        return map;
    }

    private static String TAG = "userClassifyAll";

    @Override
    public Response<List<AdminWorkOrderType>> userClassifyAll(long userId) {

        List<AdminWorkOrderType> result = Lists.newArrayList();

        Map<Integer,List<Integer>> map = userClassify(userId);

        if (map == null || map.size()==0){
            return NewResponseUtil.makeFail("无工单权限");
        }

        // 异步处理每个key 最后统一聚合
        final List<Integer> orderTypeList = map.values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        final List<CompletableFuture<AdminWorkOrderSecType>> futures = orderTypeList.stream()
                .map(o -> CompletableFuture.supplyAsync(() -> getByUserIdAndOrderType(userId, o), executor))
                .collect(Collectors.toList());

        Map<Integer, List<AdminWorkOrderSecType>> oneTypeMap = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.groupingBy(a -> workOrderTypeService.getOneFromTwo(a.getOrderType())));

        for (Integer one : oneTypeMap.keySet()) {
            AdminWorkOrderType adminWorkOrderType = new AdminWorkOrderType();

            List<AdminWorkOrderSecType> secTypes = oneTypeMap.get(one);
            if (one == OneTypeEnum.delay_finance.getType()) {
                sortByOrderType(secTypes);
            }

            adminWorkOrderType.setSecTypes(secTypes);
            adminWorkOrderType.setType(one);
            result.add(adminWorkOrderType);
        }

        return NewResponseUtil.makeSuccess(result);
    }

    private static void sortByOrderType(List<AdminWorkOrderSecType> list) {
        // 定义 orderType 的排序顺序
        List<Integer> orderTypesOrder = Arrays.asList(
                WorkOrderType.draw_cash_apply.getType(),
                WorkOrderType.second_pause.getType(),
                WorkOrderType.funduseshenhe.getType(),
                WorkOrderType.modify_payee_info.getType(),
                WorkOrderType.all_refund_audit.getType(),
                WorkOrderType.long_tail.getType(),
                WorkOrderType.prompt_draw_cash.getType(),
                WorkOrderType.end_case_handle.getType(),
                WorkOrderType.precipitation.getType(),
                WorkOrderType.money_back.getType()
        );

        // 创建映射，以便快速查找 orderType 的排序位置
        Map<Integer, Integer> orderTypeToPosition = new HashMap<>();
        for (int i = 0; i < orderTypesOrder.size(); i++) {
            orderTypeToPosition.put(orderTypesOrder.get(i), i);
        }

        // 根据 orderType 的排序顺序对列表进行排序
        list.sort(Comparator.comparingInt(o -> orderTypeToPosition.getOrDefault(o.getOrderType(), Integer.MAX_VALUE)));
    }

    private AdminWorkOrderSecType getByUserIdAndOrderType(long userId, Integer r) {
        AdminWorkOrderSecType secType = getCount(userId,r);
        secType.setOrderType(r);
        final StaffStatus ss = staffStatusService.getStaffStatus(userId, r);
        if (ss == null) {
            secType.setStaffStatus(StaffStatusEnum.stop.getType());
        } else {
            secType.setStaffStatus(ss.getStaffStatus());
            secType.setStaffType(ss.getOperType());
        }
        return secType;
    }


    private AdminWorkOrderSecType getCount(long userId,int orderType){
        final StopWatch ss = new StopWatch();

        ss.start("A");
        AdminWorkOrderSecType result = new AdminWorkOrderSecType();
        List<Integer> handleResultList = Lists.newArrayList(
                HandleResultEnum.doing.getType(),
                HandleResultEnum.later_doing.getType(),
                HandleResultEnum.reach_agree.getType()
        );
        List<OrderUserHandleResultCount> orderUserHandleResultCounts =
                baseWorkOrderService.getCountByUserIdAndHandleResult(orderType, userId, handleResultList);

        for (OrderUserHandleResultCount orderUserHandleResultCount : orderUserHandleResultCounts) {
            if (orderUserHandleResultCount.getHandleResult() == HandleResultEnum.doing.getType()) {
                result.setSelfDoingCount(orderUserHandleResultCount.getCount());
            } else if (orderUserHandleResultCount.getHandleResult() == HandleResultEnum.later_doing.getType()) {
                result.setSelfYanhouCount(orderUserHandleResultCount.getCount());
            } else if (orderUserHandleResultCount.getHandleResult() == HandleResultEnum.reach_agree.getType()) {
                result.setSelfReachAgree(orderUserHandleResultCount.getCount());
            }
        }
        ss.stop();

        //处理完成
        String status = HandleResultEnum.done.getType()+"";

        List<Integer> types = getDoneList(orderType);
        if (CollectionUtils.isNotEmpty(types)){
            status = Joiner.on(",").join(types);
        }

        ss.start("B");
        Integer selfFinishCount = baseWorkOrderService.getCountByHandleResult(userId,orderType,status );
        result.setSelfFinishCount(selfFinishCount);
        ss.stop();

        ss.start("C");
        //全组未处理
        Integer teamUndoCount = baseWorkOrderService.getAllCountByHandleResult(orderType,HandleResultEnum.undoing.getType(), userId);
        result.setTeamUndoCount(teamUndoCount);
        ss.stop();

//        System.out.println("ClassifyTypeServiceImpl.getCount" + ss.prettyPrint());
        return result;
    }

    private List<Integer> getDoneList(int orderType) {
        return vonConfigFacade.getDoneListConfig().getData().get(orderType);
    }
}
