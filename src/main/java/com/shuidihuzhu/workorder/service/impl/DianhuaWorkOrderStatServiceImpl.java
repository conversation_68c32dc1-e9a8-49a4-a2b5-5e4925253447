package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.DianhuaWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/8/15
 */
@Service("dianhuaWorkOrderStatService")
public class DianhuaWorkOrderStatServiceImpl implements WorkOrderStatService<DianhuaWorkOrderStat> {

    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(
            HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType(),
            HandleResultEnum.cancel.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType(),
            HandleResultEnum.manual_lock.getType()
    );

    @Override
    public OpResult<List<DianhuaWorkOrderStat>> getWorkOrderStatList(int one, String two, long userId) {

        if (one<=0){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        //如果二级没选
        if (StringUtils.isEmpty(two)){
            return getOne(one,userId);
        }else {
            List<Integer> twoList = Arrays.stream(two.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            return getTwo(one,twoList,userId);
        }
    }



    public OpResult<List<DianhuaWorkOrderStat>> getTwo(int oneLevel ,List<Integer> twoLevel ,long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getTwoAndUser(oneLevel,twoLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyTwo(oneLevel,twoLevel));
        }
    }

    public OpResult<List<DianhuaWorkOrderStat>> getOne(int oneLevel ,long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getOneAndUser(oneLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyOne(oneLevel));
        }
    }

    private List<DianhuaWorkOrderStat> getOnlyOne(int oneLevel) {

        String today = LocalDate.now() + " 00:00:00";
        List<Long> users = Lists.newArrayList();
        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        DianhuaWorkOrderStat d = new DianhuaWorkOrderStat();
        BeanUtils.copyProperties(workOrderStat,d);

        d.setAutoExceptionAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.exception_done,today));
        d.setCancelAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.cancel,today));
        d.setSubmitAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.done,today));
        d.setPassAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.audit_pass,today));
        d.setRejectAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.audit_reject,today));
        d.setManualLockAmount(baseStatService.getStatusInt(twoList,users,HandleResultEnum.manual_lock,today));

        return Lists.newArrayList(d);
    }


    private List<DianhuaWorkOrderStat> getOneAndUser(int oneLevel,long userId){

        List<Long> users = Lists.newArrayList(userId);
        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().distinct().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getOneAndUser(oneLevel,twoList,users,doneList);

        return getUserStat(list,users,twoList);

    }


    private List<DianhuaWorkOrderStat> getOnlyTwo(int one ,List<Integer> twoList) {


        List<WorkOrderStat> list = baseStatService.getTwoALL(one,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        return getTypeStat(list,users,twoList);
    }


    public List<DianhuaWorkOrderStat> getTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        if (twoLevel.size()>1){
            return getAllTwoAndUser(oneLevel,twoLevel,userId);
        }

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getTwoAndUser(oneLevel,twoLevel.get(0),users,doneList);

        return getUserStat(list,users,twoList);
    }


    public List<DianhuaWorkOrderStat> getAllTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        List<WorkOrderStat> list = baseStatService.getAllTwoAndUser(oneLevel,twoLevel,userId,doneList);

        return getTypeStat(list,users,twoList);
    }


    @Override
    public List<DianhuaWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        String today = LocalDate.now() + " 00:00:00";

        //自动
        Map<Long,Integer> doneExceptionMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //审核通过
        Map<Long,Integer> passMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.audit_pass,today);

        //审核通过
        Map<Long,Integer> rejectMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.audit_reject,today);

        //撤销
        Map<Long,Integer> cancelMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.cancel,today);

        //提交结果
        Map<Long,Integer> doneMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.done,today);

        //手动结果
        Map<Long,Integer> manualMap = baseStatService.getStatusMap(twoList,users,HandleResultEnum.manual_lock,today);


        return list.stream()
                .map(r->{
                    DianhuaWorkOrderStat d = new DianhuaWorkOrderStat();

                    BeanUtils.copyProperties(r,d);
                    long u = r.getUserId();
                    d.setAutoExceptionAmount(MapUtil.getFromMap(doneExceptionMap,u));
                    d.setCancelAmount(MapUtil.getFromMap(cancelMap,u));
                    d.setSubmitAmount(MapUtil.getFromMap(doneMap,u));
                    d.setPassAmount(MapUtil.getFromMap(passMap,u));
                    d.setRejectAmount(MapUtil.getFromMap(rejectMap,u));
                    d.setManualLockAmount(MapUtil.getFromMap(manualMap,u));
                    return d;

                }).collect(Collectors.toList());
    }

    @Override
    public List<DianhuaWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        String today = LocalDate.now() + " 00:00:00";

        //自动异常关闭
        Map<Integer,Integer> doneExceptionMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.exception_done,today);

        //提交结果
        Map<Integer,Integer> doneMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.done,today);

        //撤销
        Map<Integer,Integer> cancelMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.cancel,today);

        //审核通过
        Map<Integer,Integer> passMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.audit_pass,today);

        //审核通过
        Map<Integer,Integer> rejectMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.audit_reject,today);
        //手动关闭
        Map<Integer,Integer> manualMap = baseStatService.getTypeStatusMap(twoList,users,HandleResultEnum.manual_lock,today);

        return list.stream()
                .map(r->{

                    DianhuaWorkOrderStat d = new DianhuaWorkOrderStat();

                    BeanUtils.copyProperties(r,d);
                    int u = r.getTwoLevel();
                    d.setAutoExceptionAmount(MapUtil.getFromMap(doneExceptionMap,u));
                    d.setCancelAmount(MapUtil.getFromMap(cancelMap,u));
                    d.setSubmitAmount(MapUtil.getFromMap(doneMap,u));
                    d.setPassAmount(MapUtil.getFromMap(passMap,u));
                    d.setRejectAmount(MapUtil.getFromMap(rejectMap,u));
                    d.setManualLockAmount(MapUtil.getFromMap(manualMap,u));
                    return d;

                }).collect(Collectors.toList());
    }

}
