package com.shuidihuzhu.workorder.service.impl;

import com.shuidihuzhu.cf.constants.eagle.EagleVersionConst;
import com.shuidihuzhu.cf.model.eagle.EagleResult;
import com.shuidihuzhu.data.analytics.javasdk.core.HawkeyeServer;
import com.shuidihuzhu.data.analytics.javasdk.model.hawkeye.ExperimentVo;
import com.shuidihuzhu.workorder.model.EagleParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018-10-25  14:49
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=17990332
 */
@Service
@Slf4j
public class EagleFacadeImpl {

    private static final int PROJECT_ID_CF = 33;

    private static final String PROJECT_BIZ_CF = "cf";

    private static final String TAG = "EAGLE_TAG";

    @Resource
    private HawkeyeServer hawkeyeServer;

    @NotNull
    public EagleResult get(EagleParam param) {
        if (param.getUserId() == 0) {
            log.warn("userId 不能为0 param: {}", param);
            return defaultResult();
        }
        if (StringUtils.isNotBlank(param.getKey())) {
            return getEagleResultByKey(param);
        }
        if (param.getExperimentId() != 0) {
            return getEagleResultByExperimentId(param);
        }
        log.warn("需求变量key 和 实验id experimentId 至少填一个 param: {}", param);
        return defaultResult();
    }

    public @NotNull EagleResult get(long userId, String key) {
        EagleParam p = EagleParam.builder(userId).key(key).build();
        return get(p);
    }



    public Boolean getBooleanByResult(EagleResult result, String key, Boolean defValue) {
        Object v = getVal(result, key);
        if (v == null) {
            return defValue;
        }
        if (v instanceof Boolean) {
            return (Boolean) v;
        }
        log.warn("结果异常 取默认值 key: {}, result: {}", key, result);
        return defValue;
    }

    public String getStringByResult(EagleResult result, String key, String defValue) {
        Object v = getVal(result, key);
        if (v == null) {
            return defValue;
        }
        if (v instanceof String) {
            return (String) v;
        }

        // todo delete 向前兼容
        if (v instanceof Integer) {
            return String.valueOf(v);
        }
        log.warn("结果异常 取默认值 key: {}, result: {}", key, result);
        return defValue;
    }

    public String getStringByKey(long userId, String key, String defValue) {
        EagleResult result = get(userId,key);
        return getStringByResult(result, key, defValue);
    }

    public String getStringByKeyWithUnescape(long userId, String key, String defValue) {
        String v = getStringByKey(userId, key, defValue);
        return StringEscapeUtils.unescapeJava(v);
    }

    public Boolean getBooleanByKey(long userId, String key, Boolean defValue) {
        EagleResult result = get(userId,key);
        return getBooleanByResult(result, key, defValue);
    }

    public Integer getIntegerByKey(long userId, String key, Integer defValue) {
        EagleResult result = get(userId,key);
        Object v = getVal(result, key);

        if (v == null) {
            return defValue;
        }

        if (v instanceof Integer) {
            return (Integer) v;
        }

        return defValue;
    }

    public Number getNumberByKey(long userId, String key, Number defValue) {
        EagleResult result = get(userId,key);
        Object v = getVal(result, key);

        if (v == null) {
            return defValue;
        }

        if (v instanceof Number) {
            return (Number) v;
        }

        return defValue;
    }

    public String getFromKey(long userId, String key, String defValue) {
        return getStringByKey(userId, key, defValue);
    }

    public String getFromKeyWithUnescape(long userId, String key, String defValue) {
        String v = getFromKey(userId, key, defValue);
        return StringEscapeUtils.unescapeJava(v);
    }

    private Object getVal(EagleResult result, String key) {
        if (result == null) {
            return null;
        }
        Map<String, Object> extInfo = result.getExtInfo();
        if (extInfo == null) {
            return null;
        }
        return extInfo.get(key);
    }

    @NotNull
    private EagleResult getResultFromExperiment(ExperimentVo.Experiment experiment) {
        EagleResult result = new EagleResult();
        result.setExperimentId(experiment.getExperimentId());
        result.setVersion(experiment.getVersion());
        result.setExtInfo((Map<String, Object>) experiment.getExtInfo());
        return result;
    }

    @NotNull
    private EagleResult defaultResult() {
        EagleResult result = new EagleResult();
        result.setVersion(EagleVersionConst.DEFAULT);
        return result;
    }

    @NotNull
    private EagleResult getEagleResultByExperimentId(EagleParam param) {
        List<ExperimentVo.Experiment> queryList = hawkeyeServer.query(
                PROJECT_ID_CF,
                PROJECT_BIZ_CF,
                param.getExtInfo(),
                param.getDebug(),
                param.getUserId(),
                "",
                param.getIp(),
                param.getKey()
        );
        log.info("{}", queryList);
        ExperimentVo.Experiment experiment = queryList.stream()
                .filter(Objects::nonNull)
                .filter(v -> v.getExperimentId() == param.getExperimentId())
                .findAny()
                .orElse(null);
        if (experiment == null) {
            return defaultResult();
        }
        return getResultFromExperiment(experiment);
    }

    @NotNull
    private EagleResult getEagleResultByKey(EagleParam param) {
        ExperimentVo.Experiment vars = hawkeyeServer.getVars(
                PROJECT_ID_CF,
                PROJECT_BIZ_CF,
                param.getExtInfo(),
                param.getDebug(),
                param.getUserId(),
                param.getIp(),
                param.getKey());
        log.info("{} get key={}, data={}", TAG, param.getKey(), vars);
        if (vars == null) {
            log.info("{} get null key={}", TAG, param.getKey());
            return defaultResult();
        }
        return getResultFromExperiment(vars);
    }
}
