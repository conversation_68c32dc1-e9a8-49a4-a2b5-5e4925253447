package com.shuidihuzhu.workorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Ordering;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.clewtrack.model.CfUserInvitedLaunchCaseRecordModel;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.growthtool.client.CfChannelFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderReleaseEvent;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.delegate.UserInfoDelegate;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.core.service.core.OrderReadService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAutoFreeConfig;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonReminderConfig;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonReprocessConfig;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.core.dao.write.WorkOrderWriteDAO;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderMini;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.order.OrderUserHandleResultCount;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.repository.WorkOrderRepository;
import com.shuidihuzhu.workorder.service.AssignWorkOrderService;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.OrganizationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/28
 */
@Slf4j
@Service
public class BaseWorkOrderServiceImpl implements BaseWorkOrderService {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private WorkOrderRepository workOrderRepository;

    @Autowired
    private WorkOrderWriteDAO workOrderWriteDAO;

    @Autowired
    private UserOperationRecordDao recordDao;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private CfChannelFeignClient cfChannelFeignClient;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Resource
    private OrganizationService organizationService;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private AssignWorkOrderService assignWorkOrderService;

    @Resource(name="juanzhuanWorkOrderService")
    private JuanzhuanWorkOrderServiceImpl juanzhuanWorkOrderService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Autowired
    private OrderReadService orderReadService;

    @Autowired
    private AssignGroupService assignGroupService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Resource
    private WorkOrderTypeService workOrderTypeService;


    private static final List<Integer> credit_canReprocessHandleStatus = Lists.newArrayList(
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType(),
            HandleResultEnum.manual_lock.getType()
    );

    public List<Integer> CHU_SHEN_ORDER_TYPE_LIST = Lists.newArrayList();

    @PostConstruct
    public void init() {
        //初始化初次审核工单类型
        CHU_SHEN_ORDER_TYPE_LIST = workOrderTypeService.getByOneLevel(OneTypeEnum.chuci.getType());
        if(CollectionUtils.isNotEmpty(CHU_SHEN_ORDER_TYPE_LIST)){
            CHU_SHEN_ORDER_TYPE_LIST.remove(Integer.valueOf(WorkOrderType.bohui.getType()));
            CHU_SHEN_ORDER_TYPE_LIST.remove(Integer.valueOf(WorkOrderType.huifang.getType()));
            CHU_SHEN_ORDER_TYPE_LIST.remove(Integer.valueOf(WorkOrderType.bu_chong_yi_yuan_xin_xi.getType()));
        }
    }

    @Override
    public int getCountByHandleResult(long userId, int orderType, String handleResult) {

        List<Integer> results = Arrays.stream(handleResult.split(",")).map(Integer::valueOf).collect(Collectors.toList());

        final HashSet<Integer> doneTypes = Sets.newHashSet(vonConfigFacade.getDoneListConfigMap().getOrDefault(orderType, Lists.newArrayList()));
        //处理完成时，只返回当天的处理完成数量
        int handleFinish = 0;
        for (Integer result : results) {
            if (doneTypes.contains(result)) {
                handleFinish = 1;
                break;
            }
        }
        Date dateOfZero = new Date(new DateTime().dayOfYear().roundFloorCopy().getMillis());

        return workOrderDao.getCountByHandleResult(userId, orderType, results, handleFinish, dateOfZero);
    }

    @Override
    public int getAllCountByHandleResult(int orderType, int handleResult,long userId) {

        if (orderType == WorkOrderType.xiafaprogress.getType()) {
            return xiafa(orderType, handleResult, userId);
        }

        AssignGroupDO group = assignGroupService.getGroupByUserId(userId, orderType);
        if (group == null) {
            return workOrderDao.getAllCountByHandleResultAndGroupId(orderType, handleResult, 0);
        }

        return workOrderDao.getAllCountByHandleResultAndGroupId(orderType, handleResult, group.getId());
    }

    private int xiafa(int orderType, int handleResult, long userId) {
        Long orgId = organizationService.getOrgIdByUserId(userId);
        boolean userIsNeiShen = getNerShenGroupIds().contains(orgId);
        OrderSearchParam p = new OrderSearchParam();
        p.setOrderType(String.valueOf(orderType));
        p.setHandleResult(handleResult);
        p.setCurrent(1);
        p.setPageSize(500);
        Response<PaginationListVO<BasicWorkOrder>> resp = orderReadService.search(p);

        if (resp.notOk()) {
            return 0;
        }
        PaginationListVO<BasicWorkOrder> data = resp.getData();
        List<BasicWorkOrder> list = data.getList();
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return Math.toIntExact(list.stream().filter(v -> {
            Map<String, String> map = Maps.newHashMap();
            for (WorkOrderExt ext : v.getBizExtList()) {
                map.put(ext.getExtName(), ext.getExtValue());
            }
            String isNeiShenSupplyStr = map.get("isNeiShenSupply");
            Boolean isNeiShenSupply = StringUtils.isBlank(isNeiShenSupplyStr) ? null : Boolean.parseBoolean(isNeiShenSupplyStr);
            if (isNeiShenSupply == null) {
                return true;
            }

            return userIsNeiShen == isNeiShenSupply;
        }).count());
    }

    public Collection<Long> getNerShenGroupIds() {
        Config config = ConfigService.getConfig("sdc.inner");
        String value = config.getProperty("apollo.sea.group.nei-shen.id-arr", "876,877,878,879,880");
        String[] split = StringUtils.split(value, ",");
        return Arrays.stream(split)
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }


    private int getJuanzhuanType(int orderType, int handleResult,long userId){

        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(WorkOrderType.d0_1v1_tzb.getPermission());
        //特种兵用
        Set<Long> tzbSet  = Optional.ofNullable(response).map(Response::getData).orElse(Lists.newArrayList()).stream().map(AuthUserDto::getUserId).collect(Collectors.toSet());
        List<WorkOrderBaseVo> list = juanzhuanWorkOrderService.getJuanzhuanOrders(orderType,handleResult);

        if (CollectionUtils.isEmpty(list)){
            return 0;
        }

        Map<String,List<WorkOrderBaseVo>> works = list.stream().collect(Collectors.groupingBy(r->{
            if (JuanzhuanWorkOrder.ext_type_tzb.equals(r.getOrderExtType())){
                return "tzb";
            }else {
                return "common";
            }
        }));

        if (tzbSet.contains(userId)){
            return Optional.ofNullable(works.get("tzb")).map(List::size).orElse(0);
        }
        return Optional.ofNullable(works.get("common")).map(List::size).orElse(0);
    }

    @Override
    public void freeWorkOrder(long userId, int orderType) {
        freeWorkOrderWithTime(userId, orderType, null, null);
    }

    @Override
    public void freeWorkOrderWithTime(long userId, int orderType, Date startTime, Date endTime) {

        List<Integer> list = Lists.newArrayList();

        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);

        if (workOrderTypeDO == null) {
            log.info("freeWorkOrder no type userId={} ordetype={}", userId, orderType);
            return;
        }
        VonAutoFreeConfig config = vonConfigFacade.getAutoFreeConfig(orderType);

        if (config.getEnableDoingAutoFree()) {
            list.add(HandleResultEnum.doing.getType());
        }

        if (config.getEnableLaterDoAutoFree()) {
            list.add(HandleResultEnum.later_doing.getType());
        }

        if (CollectionUtils.isEmpty(list)) {
            // 没有需要回收的类型
            log.info("freeWorkOrder 没有需要回收的类型 userId={}", userId);
            return;
        }

        List<WorkOrderBase> doingList = workOrderDao.getWorkorderListByUser(userId, orderType, list);

        if (CollectionUtils.isEmpty(doingList)) {
            log.info("freeWorkOrder all userId={}", userId);
            return;
        }

        List<Long> allWorkOrderIdList = doingList.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        List<WorkOrderBase> freeWorkOrderList = doingList;

        /*
         主动服务工单释放规则需满足如下一种
         1. 未打跟进标签的
         2. 有跟进标签 并在选择时间范围内
         */
        if (orderType == WorkOrderType.cailiao_fuwu.getType()) {
            Map<Long, WorkOrderExt> extMap = orderExtService.getExtMap(allWorkOrderIdList, OrderExtName.followLabel);

            // 查询条件1的工单
            freeWorkOrderList = doingList.stream().filter(v -> {
                String followLabel = Optional.ofNullable(extMap.get(v.getId()))
                        .map(WorkOrderExt::getExtValue)
                        .orElse(null);
                return followLabel == null;
            }).collect(Collectors.toList());

            // 若选择了时间查询符合条件2的工单
            if (startTime != null && endTime != null) {
                List<WorkOrderBase> hasFollowLabelAndInSelectTimeList = doingList.stream().filter(v -> {
                    WorkOrderExt ext = extMap.get(v.getId());
                    if (ext == null) {
                        return false;
                    }
                    String followLabel = ext.getExtValue();
                    if (followLabel == null) {
                        return false;
                    }
                    Date createTime = ext.getCreateTime();
                    return createTime.after(startTime) && createTime.before(endTime);
                }).collect(Collectors.toList());
                freeWorkOrderList.addAll(hasFollowLabelAndInSelectTimeList);
            }

            if (CollectionUtils.isEmpty(freeWorkOrderList)) {
                log.info("freeWorkOrderList all userId={}", userId);
                return;
            }
        }

        List<Long> freeWorkOrderIdList = freeWorkOrderList.stream().map(WorkOrderBase::getId).collect(Collectors.toList());;
        int result = freeWorkOrderWithNoCheckOldStatus(freeWorkOrderIdList);

        //记录工单释放
        if (result > 0) {
            List<WorkOrderRecord> records = WorkOrderRecord.freeRecord(freeWorkOrderList);
            recordDao.saveRecordList(records);
        }
    }

    @Override
    public int freeWorkOrder(List<Long> workOrderIds) {
        int result = workOrderDao.freeWorkOrder(workOrderIds, HandleResultEnum.doing.getType(), HandleResultEnum.undoing.getType());
        if (result > 0) {
            onWorkOrderFree(workOrderIds);
        }
        return result;
    }

    @Override
    public int freeWorkOrderWithNoCheckOldStatus(List<Long> workOrderIds) {
        int result = workOrderDao.freeWorkOrderWithNoCheckOldStatus(workOrderIds, HandleResultEnum.undoing.getType());
        if (result > 0) {
            onWorkOrderFree(workOrderIds);
        }
        return result;
    }

    @Override
    public int callbackOrder(List<Long> workOrderIds) {
        List<Integer> preStatus = Lists.newArrayList(HandleResultEnum.doing.getType(), HandleResultEnum.later_doing.getType());
        int result = workOrderDao.callbackOrder(workOrderIds, preStatus, HandleResultEnum.undoing.getType());
        if (result > 0) {
            onWorkOrderFree(workOrderIds);
        }
        return result;
    }

    /**
     * 工单释放调用 发送mq消息
     *
     * @param workOrderIds
     */
    private void onWorkOrderFree(List<Long> workOrderIds) {
        List<Message<WorkOrderReleaseEvent>> messages = Lists.newArrayListWithCapacity(CollectionUtils.size(workOrderIds));
        for (Long workOrderId : workOrderIds) {
            Message<WorkOrderReleaseEvent> v = createMessage(workOrderId);
            messages.add(v);
        }
        producer.send(messages);
    }

    private Message<WorkOrderReleaseEvent> createMessage(Long workOrderId) {
        WorkOrderReleaseEvent e = new WorkOrderReleaseEvent();
        e.setWorkOrderId(workOrderId);
        return MessageBuilder.createWithPayload(e)
                .setTags(CfClientMQTagCons.WORK_ORDER_RELEASE)
                .addKey(CfClientMQTagCons.WORK_ORDER_RELEASE, workOrderId)
                .build();
    }


    @Override
    public int getOrderCount(List<Integer> orderType, List<Integer> results, String time) {

        if (StringUtils.isEmpty(time)) {
            return 0;
        }

        time = LocalDate.now() + " " + time;

        return workOrderDao.getOrderCount(orderType, results, time);
    }

    @Override
    public WorkOrderVO getWorkOrderById(long workId) {

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workId);
        WorkOrderVO vo = trans2VO(workOrderBase);
        if (vo == null) {
            return new WorkOrderVO();
        }

        return vo;

    }

    @Override
    public WorkOrderVO getOrderByTypeAndTime(int caseId, int orderType, String time) {

        if (StringUtils.isEmpty(time)) {
            return null;
        }

        WorkOrderBase workOrderBase = workOrderDao.getOrderByTypeAndTime(caseId, orderType, time);

        return trans2VO(workOrderBase);
    }

    @Nullable
    private WorkOrderVO trans2VO(WorkOrderBase workOrderBase) {
        if (workOrderBase == null) {
            return null;
        }
        WorkOrderVO vo = new WorkOrderVO();
        vo.setWorkOrderId(workOrderBase.getId());
        vo.setOrderLevel(workOrderBase.getOrderlevel());
        vo.setHandleResult(workOrderBase.getHandleResult());
        vo.setCaseId(workOrderBase.getCaseId());
        vo.setOrderType(workOrderBase.getOrderType());

        vo.setUpdateTime(workOrderBase.getUpdateTime());
        vo.setCreateTime(workOrderBase.getCreateTime());
        vo.setHandleTime(workOrderBase.getHandleTime());
        vo.setOperatorId(workOrderBase.getOperatorId());
        vo.setFinishTime(workOrderBase.getFinishTime());
        vo.setAssignGroupId(workOrderBase.getAssignGroupId());
        if (workOrderBase.getOrderType() == WorkOrderType.xiafaprogress.getType()) {
            List<WorkOrderExt> WorkOrderExts = daoExt.getWorkOrderExtByName(workOrderBase.getId(), Lists.newArrayList(OrderExtName.supplyProgressId.getName(),OrderExtName.supplyActionId.getName()));
            Map<String, WorkOrderExt> map = WorkOrderExts.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, Function.identity(),(o1,o2)->o2));
            vo.setSupplyProgressId(Optional.ofNullable(map.get(OrderExtName.supplyProgressId.getName())).map(WorkOrderExt::getExtValue).map(Long::valueOf).orElse(0L));
            vo.setSupplyActionId(Optional.ofNullable(map.get(OrderExtName.supplyActionId.getName())).map(WorkOrderExt::getExtValue).map(Long::valueOf).orElse(0L));

        }

        if (workOrderBase.getOrderType() == WorkOrderType.ugcpinglun.getType()) {
            Map<String, WorkOrderExt> map = getUgcWorkOrderExt(workOrderBase);
            vo.setContentType(map.get(OrderExtName.contentType.getName()).getExtValue());
            if (Objects.nonNull(map.get(OrderExtName.extId.getName()))){
                vo.setExtId(map.get(OrderExtName.extId.getName()).getExtValue());
            }
            if (Objects.nonNull(map.get(OrderExtName.verificationId.getName()))) {
                vo.setVerificationId(map.get(OrderExtName.verificationId.getName()).getExtValue());
            }

            if (Objects.nonNull(map.get(OrderExtName.medicalStatus.getName()))) {
                vo.setMedicalStatus(map.get(OrderExtName.medicalStatus.getName()).getExtValue());
            }

            if (Objects.nonNull(map.get(OrderExtName.medicalWorkType.getName()))) {
                vo.setMedicalWorkType(map.get(OrderExtName.medicalWorkType.getName()).getExtName());
            }
        }

        if (workOrderBase.getOrderType() == WorkOrderType.ugc_complaint_verify.getType()) {

            Map<String, WorkOrderExt> map = getUgcWorkOrderExt(workOrderBase);

            if (Objects.nonNull(map.get(OrderExtName.contentType.getName()))) {
                vo.setContentType(map.get(OrderExtName.contentType.getName()).getExtValue());
            }

            if (Objects.nonNull(map.get(OrderExtName.verificationId.getName()))) {
                vo.setVerificationId(map.get(OrderExtName.verificationId.getName()).getExtValue());
            }

            if (Objects.nonNull(map.get(OrderExtName.medicalWorkType.getName()))) {
                vo.setMedicalWorkType(map.get(OrderExtName.medicalWorkType.getName()).getExtValue());
            }

            if (Objects.nonNull(map.get(OrderExtName.avalibaleComplaintList.getName()))) {
                vo.setAvalibaleComplaintList(map.get(OrderExtName.avalibaleComplaintList.getName()).getExtValue());
            }

        }


        if (workOrderBase.getOrderType() == WorkOrderType.cailiao_4.getType()
                || workOrderBase.getOrderType() == WorkOrderType.cailiao_5.getType()
                || workOrderBase.getOrderType() == WorkOrderType.cailiao_zhu_dong_fu_wu.getType()
        ) {
            WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.conditionResult.getName());
            vo.setConditionResult(Optional.ofNullable(workOrderExt).map(WorkOrderExt::getExtValue).orElse("[]"));
        }
        if (WorkOrderType.QC_WORK_ORDER_LIST.contains(workOrderBase.getOrderType())) {
            WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.qcId.getName());
            vo.setQcId(Integer.parseInt(workOrderExt.getExtValue()));
        }
        //捐转工单
        Set<Integer> set = workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType()).stream().collect(Collectors.toSet());
        if (set.contains(workOrderBase.getOrderType())) {
            List<WorkOrderExt> workOrderExt = daoExt.getWorkOrderExtByName(workOrderBase.getId(),
                    Lists.newArrayList(OrderExtName.showName.getName(), OrderExtName.donationCount.getName(), OrderExtName.shareCount.getName(), OrderExtName.amount.getName(), OrderExtName.label.getName(), OrderExtName.OperComment.getName(), OrderExtName.CallStatus.getName()));
            Map<String, WorkOrderExt> map = workOrderExt.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, Function.identity(), (o1, o2) -> o2));
            vo.setShowName(Optional.ofNullable(map.get(OrderExtName.showName.getName())).map(WorkOrderExt::getExtValue).orElse(""));
            vo.setDonationCount(Optional.ofNullable(map.get(OrderExtName.donationCount.getName())).map(WorkOrderExt::getExtValue).map(Integer::valueOf).orElse(0));
            vo.setShareCount(Optional.ofNullable(map.get(OrderExtName.shareCount.getName())).map(WorkOrderExt::getExtValue).map(Integer::valueOf).orElse(0));
            vo.setAmount(Optional.ofNullable(map.get(OrderExtName.amount.getName())).map(WorkOrderExt::getExtValue).map(Integer::valueOf).orElse(0));
            vo.setLabel(Optional.ofNullable(map.get(OrderExtName.label.getName())).map(WorkOrderExt::getExtValue).orElse(""));
            vo.setComment(Optional.ofNullable(map.get(OrderExtName.OperComment.getName())).map(WorkOrderExt::getExtValue).orElse(""));
            vo.setCallStatus(Optional.ofNullable(map.get(OrderExtName.CallStatus.getName())).map(WorkOrderExt::getExtValue).orElse(""));

        }

        return vo;
    }

    private Map<String, WorkOrderExt> getUgcWorkOrderExt(WorkOrderBase workOrderBase) {
        List<WorkOrderExt> workOrderExt = daoExt.getWorkOrderExtByNameMaster(workOrderBase.getId(), Lists.newArrayList(OrderExtName.contentType.getName(), OrderExtName.extId.getName(), OrderExtName.verificationId.getName(), OrderExtName.medicalStatus.getName(), OrderExtName.medicalWorkType.getName(),OrderExtName.avalibaleComplaintList.getName()));
        return workOrderExt.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, Function.identity(), (v1, v2) -> v2));
    }

    @Override
    public Integer getWorkOrderListCount(QueryListParam queryListParam) {

        List<Integer> types = Lists.newArrayList();
        if (StringUtils.isNotEmpty(queryListParam.getOrderType())) {
            types = Arrays.stream(queryListParam.getOrderType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        OpResult<List<Integer>> o = getCaseList(queryListParam);

        if (!o.isSuccess()) {
            return 0;
        }

        List<Integer> caseIds = o.getData();

        int count = workOrderRepository.getAllWorkorderCount(queryListParam.getOperId(),
                types, queryListParam.getHandleResult(), queryListParam.getWorkOrderId(),
                caseIds, queryListParam.getStartCreateTime(),
                queryListParam.getEndCreateTime(), queryListParam.getStartHandleTime(), queryListParam.getEndHandleTime(),
                queryListParam.getStartDoneTime(), queryListParam.getEndDoneTime(),queryListParam.isOuterUser()?1:0,queryListParam.getBelonger());

        return count;
    }

    @Override
    public OpResult<PageResult<QueryListResult>> getWorkOrderList(QueryListParam queryListParam) {


        PageResult<QueryListResult> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = queryListParam.getPageSize() + 1;

        List<Integer> types = Lists.newArrayList();
        if (StringUtils.isNotEmpty(queryListParam.getOrderType())) {
            types = Arrays.stream(queryListParam.getOrderType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        OpResult<List<Integer>> o = getCaseList(queryListParam);

        if (!o.isSuccess()) {
            return OpResult.createSucResult(pageResult);
        }

        List<Integer> caseIds = o.getData();

        String forceIndex = "";
//        if (queryListParam.getWorkOrderId() == 0
//                && queryListParam.getCaseId() == 0
//                && queryListParam.getOperId() > 0
//                && CollectionUtils.isNotEmpty(types)){
//            forceIndex = "  force index(idx_operator_id_order_type_handle_result) ";
//        }

        List<WorkOrderBase> list = workOrderDao.getAllWorkorderList(queryListParam.getOperId(),
                types, p, queryListParam.getHandleResult(), queryListParam.getWorkOrderId(), queryListParam.getPageWorkOrderId(),
                queryListParam.getPaging(), caseIds, queryListParam.getStartCreateTime(),
                queryListParam.getEndCreateTime(), queryListParam.getStartHandleTime(), queryListParam.getEndHandleTime(),
                queryListParam.getStartDoneTime(), queryListParam.getEndDoneTime(),queryListParam.isOuterUser()?1:0,forceIndex,queryListParam.getBelonger());

        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            pageResult.setPageList(Lists.newArrayList());
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(queryListParam.getPaging())) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        if(StringUtils.isEmpty(queryListParam.getOrderType())){
            types = list.stream().map(WorkOrderBase::getOrderType).collect(Collectors.toList());
        }

        OpResult<List<QueryListResult>> voResult = promoteVoList(list, types);
        if (voResult.isFail()) {
            return OpResult.createFailResult(voResult.getErrorCode());
        }

        pageResult.setPageList(voResult.getData());

        return OpResult.createSucResult(pageResult);
    }

    private OpResult<List<QueryListResult>> promoteVoList(List<WorkOrderBase> list, List<Integer> types) {

        List<Integer> cIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());
        //查询案例
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(cIds);

        if (feignResponse == null || feignResponse.notOk()) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        List<CrowdfundingInfo> caseList = feignResponse.getData();

        Map<Integer, CrowdfundingInfo> map = caseList.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> channelMap = getChannel(caseList);

        List<Long> userIds = list.stream().map(r -> Long.valueOf(r.getOperatorId())).collect(Collectors.toList());

        List<AuthUserDto> models = userFeignClient.getAuthUserByIds(userIds).getData();

        Map<Integer, AuthUserDto> userMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(models)){
            Map<Long, AuthUserDto> authUserDtoMap = models.stream().collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity(), (o1, o2) -> o2));
            for (Long userId : authUserDtoMap.keySet()) {
                userMap.put(userId.intValue(), authUserDtoMap.get(userId));
            }
        }


        Map<Long, String> wordIdMap = getExtValues(list, types, OrderExtName.wordId.getName());

        Map<Long, String> contentTypeMap = getExtValues(list, types, OrderExtName.contentType.getName());

        Map<Long, String> supplyProgressMap = getExtValues(list, types, OrderExtName.supplyProgressId.getName());

        Map<Long, String> supplyActionMap = getExtValues(list, types, OrderExtName.supplyActionId.getName());

        Map<Long, String> headImageUrlMap = getExtValues(list, types, OrderExtName.headImageUrl.getName());

        //患者社群动态
        Map<Long,String> hopeTreeStateMap = getExtValues(list,types,OrderExtName.hopeTreeStateId.getName());

        Map<Long,String> trustDonateMsgMap = getExtValues(list,types,OrderExtName.trustDonateMsgId.getName());

        Map<Long,String> fundUseProgressMap = getExtValues(list,types, OrderExtName.fundUseProgressId.getName());

        Map<Long,String> consultEvaluationMap = getExtValues(list,types,OrderExtName.consultantEvaluationComment.getName());

        Map<Long,String> volunteerNameMap = getExtValues(list,types,OrderExtName.volunteerName.getName());

        Map<Long,String> verificationMap = getExtValues(list,types,OrderExtName.verificationId.getName());

        Map<Long,String> medicalWorkTypeMap = getExtValues(list,types,OrderExtName.medicalWorkType.getName());

        Map<Long,String> avalibaleComplaintListMap = getExtValues(list,types,OrderExtName.avalibaleComplaintList.getName());

        Map<Long, String> financeBusinessIdMap = getExtValues(list, types, OrderExtName.BUSINESS_ID.getName());
        Map<Long, String> riskLabelMarkMap = getExtValues(list, types, OrderExtName.riskLabelMarkWorkOrderScene.getName());





        List<QueryListResult> voList = list.stream()
                .filter(r -> {
                    //问卷工单需要校验案例
                    if (WorkOrderType.wenjuan.getType() == r.getOrderType()) {
                        return true;
                    }
                    //患者社群动态
                    if(WorkOrderType.ugcprogress.getType() == r.getOrderType() && hopeTreeStateMap != null){
                        return true;
                    }

                    if((WorkOrderType.fxj_publicity_msg_audit.getType() == r.getOrderType() ||
                            WorkOrderType.fxj_comment_msg_audit.getType() == r.getOrderType())&& trustDonateMsgMap != null){
                        return true;
                    }

                    // 外呼工单没有案例id
                    if(WorkOrderType.qc_call.getType() == r.getOrderType()
                        || WorkOrderType.money_back.getType() == r.getOrderType()){
                        return true;
                    }
                    //过滤不存的案例
                    if (map.containsKey(r.getCaseId())) {
                        return true;
                    }
                    log.error("getOrderList CrowdfundingInfo=null caseId={}", r.getCaseId());
                    return false;
                })
                .map(r -> getQueryListResult(map, channelMap, userMap, r, wordIdMap, contentTypeMap, supplyProgressMap, supplyActionMap,
                        headImageUrlMap,hopeTreeStateMap, fundUseProgressMap,consultEvaluationMap,volunteerNameMap,verificationMap,medicalWorkTypeMap,avalibaleComplaintListMap,trustDonateMsgMap,
                        riskLabelMarkMap, financeBusinessIdMap))
                .collect(Collectors.toList());
        return OpResult.createSucResult(voList);
    }

    @NotNull
    private QueryListResult getQueryListResult(Map<Integer, CrowdfundingInfo> map,
                                               Map<Integer, ChannelRefine.ChannelRefineResuleEnum> channelMap,
                                               Map<Integer, AuthUserDto> userMap,
                                               WorkOrderBase r,
                                               Map<Long, String> extMap,
                                               Map<Long, String> contentTypeMap,
                                               Map<Long, String> supplyProgressMap,
                                               Map<Long, String> supplyActionMap,
                                               Map<Long, String> headImageUrlMap,
                                               Map<Long, String> hopeTreeStateMap,
                                               Map<Long, String> fundUseProgressMap,
                                               Map<Long, String> consultEvaluationMap,
                                               Map<Long, String> volunteerNameMap,
                                               Map<Long, String> verificationMap,
                                               Map<Long, String> medicalWorkTypeMap,
                                               Map<Long,String> avalibaleComplaintListMap,
                                               Map<Long,String> trustDonateMsgMap,
                                               Map<Long,String> riskLabelMarkMap,
                                               Map<Long, String> financeBusinessIdMap
                                               ) {
        QueryListResult workOrderVO = new QueryListResult();

        workOrderVO.setWorkOrderId(r.getId());
        workOrderVO.setCaseId(r.getCaseId());
        workOrderVO.setOrderLevel(r.getOrderlevel());
        workOrderVO.setOrderType(r.getOrderType());
        workOrderVO.setOneType(workOrderTypeService.getOneFromTwo(r.getOrderType()));
        workOrderVO.setUpdateTime(r.getUpdateTime());
        workOrderVO.setHandleTime(r.getHandleTime());
        workOrderVO.setHandleResult(r.getHandleResult());
        workOrderVO.setCreateTime(r.getCreateTime());
        int operId = Long.valueOf(r.getOperatorId()).intValue();
        workOrderVO.setOperId(operId);
        workOrderVO.setOperName("");

        CrowdfundingInfo c = map.get(r.getCaseId());
        if (c != null) {
            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUserId(c.getUserId());
            workOrderVO.setCaseUuid(c.getInfoId());
        }else{
            c = new CrowdfundingInfo();
        }

        if (channelMap.get(r.getCaseId()) != null) {
            ChannelRefine.ChannelRefineResuleEnum resuleEnum = channelMap.get(r.getCaseId());
            workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
            workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
        }
        if (userMap.get(operId) != null) {
            workOrderVO.setOperName(userMap.get(operId).getUserName());
        } else {
            if (r.getHandleResult() == HandleResultEnum.smart_audit_pass.getType() && r.getOperatorId() == AdminUserIDConstants.SYSTEM) {
                workOrderVO.setOperName("系统");
            }
        }

        if (extMap.get(r.getId()) != null) {
            workOrderVO.setWordId(extMap.get(r.getId()));
        }

        if (contentTypeMap.get(r.getId()) != null) {
            workOrderVO.setContentType(contentTypeMap.get(r.getId()));
        }

        if (supplyProgressMap.get(r.getId()) != null) {
            workOrderVO.setSupplyProgressId(Long.parseLong(supplyProgressMap.get(r.getId())));
        }

        if (supplyActionMap.get(r.getId()) != null){
            workOrderVO.setSupplyActionId(Long.valueOf(supplyActionMap.get(r.getId())));
        }

        if (StringUtils.isNotEmpty(headImageUrlMap.get(r.getId()))) {
            workOrderVO.setHeadImageUrl(headImageUrlMap.get(r.getId()));
        }

        if(WorkOrderType.ugcprogress.getType() == r.getOrderType() && hopeTreeStateMap.get(r.getId())!= null){
            workOrderVO.setHopeTreeStateId(hopeTreeStateMap.get(r.getId()));
        }

        if(trustDonateMsgMap.get(r.getId())!= null){
            workOrderVO.setTrustDonateMsgId(trustDonateMsgMap.get(r.getId()));
        }

        if (StringUtils.isNotEmpty(fundUseProgressMap.get(r.getId()))) {
            workOrderVO.setFundUseProgressId(Integer.parseInt(fundUseProgressMap.get(r.getId())));
        }
        if (StringUtils.isNotEmpty(consultEvaluationMap.get(r.getId()))){
            workOrderVO.setConsultantEvaluationComment(consultEvaluationMap.get(r.getId()));
        }
        if (StringUtils.isNotEmpty(volunteerNameMap.get(r.getId()))){
            workOrderVO.setVolunteerName(volunteerNameMap.get(r.getId()));
        }

        if (StringUtils.isNotEmpty(verificationMap.get(r.getId()))){
            workOrderVO.setVerificationId(verificationMap.get(r.getId()));
        }

        if (StringUtils.isNotEmpty(medicalWorkTypeMap.get(r.getId()))){
            workOrderVO.setMedicalWorkType(medicalWorkTypeMap.get(r.getId()));
        }

        if (StringUtils.isNotEmpty(avalibaleComplaintListMap.get(r.getId()))){
            workOrderVO.setAvalibaleComplaintList(avalibaleComplaintListMap.get(r.getId()));
        }
        if (StringUtils.isNotEmpty(riskLabelMarkMap.get(r.getId()))){
            workOrderVO.setRiskLabelMarkWorkOrderScene(Integer.parseInt(riskLabelMarkMap.get(r.getId())));
        }

        // 资金业务ID
        if (StringUtils.isNotBlank(financeBusinessIdMap.get(r.getId()))) {
            workOrderVO.setFinanceBusinessId(Long.parseLong(financeBusinessIdMap.get(r.getId())));
        }

        workOrderVO.setCanReminder(checkReminder(r));

        // 设置是否可以重新审核
        workOrderVO.setCanReprocess(checkCanReprocess(r, c));
        return workOrderVO;
    }

    /**
     * 检查是否可以重新审核
     * <p>
     * 该工单为审核工单 or 回访工单；
     * 该审核工单、回访工单的工单必须为通过或驳回
     * 该案例未结束。
     * 案例不能有没有结果的对应类型工单
     *
     * @param workOrder
     * @param crowdfundingInfo
     * @return
     */
    private boolean checkCanReprocess(WorkOrderBase workOrder, CrowdfundingInfo crowdfundingInfo) {
        if (Objects.isNull(workOrder) || Objects.isNull(crowdfundingInfo)) {
            return false;
        }

        long workOrderId = workOrder.getId();
        int orderType = workOrder.getOrderType();
        int handleResult = workOrder.getHandleResult();

        int caseId = crowdfundingInfo.getId();

        //重新审核配置信息
        VonReprocessConfig config = vonConfigFacade.getReprocessConfig(orderType);
        Boolean workOrderReprocess = config.getWorkOrderReprocess();
        String workOrderHandleResult = config.getWorkOrderHandleResult();
        //工单不可以重新审核
        if (!workOrderReprocess) {
            return false;
        }
        //是否需要特殊处理
        if (orderType == WorkOrderType.zengxinnormal.getType() ||
                orderType == WorkOrderType.zengxinrisk.getType()) {
            //只有本条是最新的一条处理完成才可以重审
            if (credit_canReprocessHandleStatus.contains(handleResult)) {
                List<WorkOrderBase> workOrderBases = workOrderDao.listByCaseIdAndTypeAndResult(
                        caseId,
                        Lists.newArrayList(orderType),
                        Lists.newArrayList());
                boolean containsStopCase = workOrderBases.stream().anyMatch(item -> item.getHandleResult() == HandleResultEnum.stop_case.getType());
                //存在停止筹款不能重新审核
                if (containsStopCase) {
                    return false;
                }
                Ordering<WorkOrderBase> handleTimeSort = Ordering.natural().reverse().onResultOf(WorkOrderBase::getHandleTime);
                Long lastFinishWorkId = workOrderBases.stream()
                        .min(handleTimeSort)
                        .map(WorkOrderBase::getId).orElse(0L);
                if (workOrderId != lastFinishWorkId) {
                    return false;
                }
            }
        }

        if (StringUtils.isNotBlank(workOrderHandleResult)) {
            List<String> workOrderHandleResults = Splitter.on(",").splitToList(workOrderHandleResult);
            if (!workOrderHandleResults.contains(String.valueOf(handleResult))) {
                return false;
            }
        }

        // 对于同一个案例，所有的预审工单中只允许对最新的一张操作重新审核
        if (CHU_SHEN_ORDER_TYPE_LIST.contains(orderType)) {
            List<WorkOrderBase> workOrderBases = workOrderDao.listByCaseIdAndTypeAndResult(
                    caseId,
                    CHU_SHEN_ORDER_TYPE_LIST,
                    Lists.newArrayList());
            Long lastOrderId = workOrderBases.stream()
                    .max(Comparator.comparing(WorkOrderBase::getCreateTime))
                    .map(WorkOrderBase::getId)
                    .orElse(0L);
            if (workOrderId != lastOrderId) {
                return false;
            }
        }

        List<WorkOrderBase> workOrders = workOrderDao.getOrderByCaseId(
                caseId,
                Lists.newArrayList(orderType),
                Lists.newArrayList(HandleResultEnum.unDoResult())
        );
        if (CollectionUtils.isNotEmpty(workOrders)) {
            return false;
        }

        return true;
    }

    @Override
    public boolean checkCanReprocess(WorkOrderBase workOrder) {
        FeignResponse<CrowdfundingInfo> response = client.getCaseInfoById(workOrder.getCaseId());
        if (response.notOk()) {
            return false;
        }
        CrowdfundingInfo info = response.getData();
        return checkCanReprocess(workOrder, info);
    }

    @Override
    public boolean checkCanReprocess(long workOrderId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        return checkCanReprocess(workOrder);
    }

    /**
     * 是否可以被催单
     *
     * @param workOrderBase
     * @return
     */
    public boolean checkReminder(WorkOrderBase workOrderBase) {

        WorkOrderTypeDO workOrderType = workOrderTypeService.getFromOrderTypeCode(workOrderBase.getOrderType());
        if (workOrderType == null) {
            return false;
        }
        VonReminderConfig reminderConfig = vonConfigFacade.getReminderConfig(workOrderBase.getOrderType());
        Boolean workOrderReminder = reminderConfig.getWorkOrderReminder();
        if (workOrderReminder) {
            return checkReminder(workOrderBase.getOrderlevel(), workOrderBase.getHandleResult());
        }

        return false;
    }

    private boolean checkReminder(int orderLevel, int handleResult) {

        if (OrderLevel.urgent.getType() != orderLevel &&
                (HandleResultEnum.undoing.getType() == handleResult ||
                        HandleResultEnum.doing.getType() == handleResult)) {
            return true;
        }
        return false;
    }


    @Override
    public Map<Integer, ChannelRefine.ChannelRefineResuleEnum> getChannel(int caseId) {

        //查询案例
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(caseId));

        if (feignResponse == null || feignResponse.notOk() || feignResponse.getData() == null) {
            return Maps.newHashMap();
        }

        return getChannel(feignResponse.getData());
    }

    /**
     * 查询渠道
     *
     * @param caseList
     * @return
     */
    @Override
    public Map<Integer, ChannelRefine.ChannelRefineResuleEnum> getChannel(List<CrowdfundingInfo> caseList) {

        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> map = Maps.newHashMap();

        //查询渠道
        List<ChannelRefineDTO> channelRefineDTOS = caseList.stream()
                .map(r -> {
                    ChannelRefineDTO dto = new ChannelRefineDTO();
                    dto.setChannel(r.getChannel());
                    dto.setInfoId(Long.valueOf(r.getId()));
                    dto.setUserId(r.getUserId());
                    return dto;
                }).collect(Collectors.toList());

        Response<List<CfUserInvitedLaunchCaseRecordModel>> response = cfChannelFeignClient.getCfUserInvitedLaunchCaseRecordByInfoIds(channelRefineDTOS);
        log.info("ShenheWorkOrder refineDTO={} response={}", channelRefineDTOS, JSON.toJSONString(response));

        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            return map;
        }

        List<CfUserInvitedLaunchCaseRecordModel> recordModels = response.getData();

        recordModels.stream().forEach(r -> {
            String channel = r.getChannel();
            map.put(Long.valueOf(r.getInfoId()).intValue(), ChannelRefine.ChannelRefineResuleEnum.parse(channel));
        });

        return map;
    }

    @Override
    public OpResult changeOrderLevel(long workOrderId, int orderLevel, long operatorId, String userComment) {

        OrderLevel l = OrderLevel.getFromType(orderLevel);
        if (l == null || workOrderId <= 0) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        int a = workOrderDao.changeOrderLevel(workOrderId, orderLevel);

        //记录工单优先级
        if (a > 0) {
            WorkOrderRecord records = WorkOrderRecord.reminderRecord(workOrderId, operatorId, userComment);
            recordDao.saveRecord(records);
        }

        return OpResult.createSucResult(a);
    }


    @Override
    public int closeOrderBycaseIdAndType(int caseId, int orderType, int handleResult, long operatorId, String comment) {


        WorkOrderBase workOrderBase = workOrderDao.getOrderBycaseIdAndType(caseId, orderType);

        if (workOrderBase == null){
            return 0;
        }

        long workOrderId = workOrderBase.getId();

        int a = workOrderDao.closeOrderById(Lists.newArrayList(workOrderId), handleResult);

        //记录工单优先级
        if (a > 0) {
            if (HandleResultEnum.undoing.getType() == handleResult) {
                onWorkOrderFree(Lists.newArrayList(workOrderId));
            }

            WorkOrderRecord record = new WorkOrderRecord();
            record.setWorkOrderType(0);
            record.setOperatorId(operatorId);
            record.setComment(comment);
            record.setOperateMode(OperateMode.handle.getType());
            record.setOperateDesc("修改工单状态为: " + handleResult);
            record.setWorkOrderId(workOrderId);

            recordDao.saveRecord(record);

            // 发送工单状态变化事件 用于解锁主动工单结束释放锁
            sendMsg(workOrderId, handleResult);
        }

        return 0;
    }

    @Override
    public void closeOrderBycaseIdAndTypeWithoutHandleTime(int caseId, int orderType, int handleResult, long operatorId, String comment) {


        WorkOrderBase workOrderBase = workOrderDao.getOrderBycaseIdAndType(caseId, orderType);

        if (workOrderBase == null){
            return ;
        }

        long workOrderId = workOrderBase.getId();

        int a = workOrderWriteDAO.updateHandleResultByIds(Lists.newArrayList(workOrderId), handleResult);

        //记录工单优先级
        if (a > 0) {
            if (HandleResultEnum.undoing.getType() == handleResult) {
                onWorkOrderFree(Lists.newArrayList(workOrderId));
            }

            WorkOrderRecord record = new WorkOrderRecord();
            record.setWorkOrderType(0);
            record.setOperatorId(operatorId);
            record.setComment(comment);
            record.setOperateMode(OperateMode.handle.getType());
            record.setOperateDesc("修改工单状态为: " + handleResult);
            record.setWorkOrderId(workOrderId);

            recordDao.saveRecord(record);

            // 发送工单状态变化事件 用于解锁主动工单结束释放锁
            sendMsg(workOrderId, handleResult);
        }

    }

    private void sendMsg(long workOrderId,int handleResult){
        WorkOrderResultChangeEvent e = new WorkOrderResultChangeEvent();
        e.setHandleResult(handleResult);
        e.setWorkOrderId(workOrderId);
        Message<WorkOrderResultChangeEvent> message = MessageBuilder.createWithPayload(e)
                .setTags(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE)
                .addKey(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE, workOrderId)
                .setDelayLevel(DelayLevel.S1)
                .build();
        producer.send(message);
    }

    @Override
    public List<WorkOrderBase> listByCaseId(int caseId, int orderType) {
        return workOrderDao.listByCaseId(caseId, orderType);
    }

    @Override
    public WorkOrderVO getLastByCaseIdAndOrderType(int caseId, int orderType) {
        WorkOrderBase base = workOrderDao.getLastByCaseIdAndOrderType(caseId, orderType);
        return trans2VO(base);
    }

    @Override
    public WorkOrderVO getLastByCaseIdAndOrderTypes(int caseId, List<Integer> orderTypes) {
        WorkOrderBase lastByCaseIdAndOrderTypes = workOrderDao.getLastByCaseIdAndOrderTypes(caseId, orderTypes);
        WorkOrderBase base = lastByCaseIdAndOrderTypes;
        return trans2VO(base);
    }

    @Override
    public List<WorkOrderVO> queryByCaseAndTypes(int caseId, List<Integer> orderTypes) {
        if (caseId <= 0 || CollectionUtils.isEmpty(orderTypes)) {
            return Lists.newArrayList();
        }
        List<WorkOrderBase> workOrderBases = workOrderDao.queryByCaseAndTypes(caseId, orderTypes);

        List<WorkOrderVO> res = Lists.newArrayList();
        for (WorkOrderBase workOrderBase : workOrderBases) {
            res.add(trans2VO(workOrderBase));
        }

        return res;
    }

    @Override
    public List<WorkOrderVO> listByCaseIdsAndTypes(List<Integer> caseIds, List<Integer> orderTypes) {
        if (CollectionUtils.isEmpty(caseIds) || CollectionUtils.isEmpty(orderTypes)) {
            return Lists.newArrayList();
        }
        List<WorkOrderBase> workOrderBases = workOrderDao.listByCaseIdsAndTypes(caseIds, orderTypes);

        List<WorkOrderVO> res = Lists.newArrayList();
        for (WorkOrderBase workOrderBase : workOrderBases) {
            res.add(trans2VO(workOrderBase));
        }
        return res;
    }

    @Override
    public List<WorkOrderVO> ListByCaseIdAndTypeAndResult(int caseId, List<Integer> orderTypes, List<Integer> results) {
        List<WorkOrderBase> v = workOrderDao.listByCaseIdAndTypeAndResult(caseId, orderTypes, results);

        if (CollectionUtils.isEmpty(v)) {
            return Lists.newArrayList();
        }
        return v.stream().map(this::trans2VO).collect(Collectors.toList());
    }

    @Override
    public List<Integer> listCaseIdsByTypeAndCount(List<Integer> caseIds, Integer orderType, Integer count) {

        if (CollectionUtils.isEmpty(caseIds)) {
            return new ArrayList<>();
        }

        List<WorkOrderMini> workOrderMinis = workOrderDao.listCaseIdsByTypeAndCount(caseIds, orderType);
        if (CollectionUtils.isEmpty(workOrderMinis)) {
            return new ArrayList<>();
        }

        return workOrderMinis.stream()
                .filter(f -> (f.getOrderCount() >= count))
                .map(WorkOrderMini::getCaseId)
                .collect(Collectors.toList());
    }

    @Override
    public boolean closeOrderByWorkOrderId(long workOrderId, int handleResult, long operatorId, String comment) {
        int affectRow = workOrderDao.closeOrderById(Lists.newArrayList(workOrderId), handleResult);
        //记录工单优先级
        if (affectRow > 0) {
            if (HandleResultEnum.undoing.getType() == handleResult) {
                onWorkOrderFree(Lists.newArrayList(workOrderId));
            }

            WorkOrderRecord record = new WorkOrderRecord();
            record.setWorkOrderType(0);
            record.setOperatorId(operatorId);
            record.setComment(comment);
            record.setOperateMode(OperateMode.handle.getType());
            record.setOperateDesc("修改状态为" + HandleResultEnum.getShowMsgByType(handleResult) + "_" + handleResult);
            record.setWorkOrderId(workOrderId);

            recordDao.saveRecord(record);
        }

        return true;
    }

    @Override
    public boolean closeOrderByWorkOrderIds(List<Long> workOrderIds, int handleResult, long operatorId, String comment) {
        int affectRow = workOrderDao.closeOrderById(workOrderIds, handleResult);
        //记录工单优先级
        if (affectRow > 0) {
            if (HandleResultEnum.undoing.getType() == handleResult) {
                onWorkOrderFree(workOrderIds);
            }

            List<WorkOrderRecord> workOrderRecords = Lists.newArrayList();
            for (Long workOrderId : workOrderIds) {
                WorkOrderRecord record = new WorkOrderRecord();
                record.setWorkOrderType(0);
                record.setOperatorId(operatorId);
                record.setComment(comment);
                record.setOperateMode(OperateMode.handle.getType());
                record.setOperateDesc(HandleResultEnum.getMsgByType(handleResult));
                record.setWorkOrderId(workOrderId);
                workOrderRecords.add(record);
            }

            recordDao.saveRecordList(workOrderRecords);
        }

        return true;
    }

    @Override
    public boolean createWorkOrderEnforce(WorkOrderBase wordOrder, int operatorId) {
        int operatorOrgId = organizationService.getUserOrgId(wordOrder.getOperatorId());
        wordOrder.setOperatorOrgId(operatorOrgId);
        //生成工单
        orderOperationFacade.createWorkOrder(wordOrder);
        log.info("createWorkOrderEnforce workId:{}", wordOrder.getId());
        //添加record记录
        wordOrder.setOperatorId(operatorId);
        WorkOrderRecord workOrderRecord = WorkOrderRecord.createRecord(wordOrder);
        recordDao.saveRecord(workOrderRecord);
        return true;
    }

    @Override
    public int reportTransfer(List<Long> ids, int orderType, int operatorId, int recipientId) {
        int operatorOrgId = organizationService.getUserOrgId(recipientId);
        int a = workOrderDao.reportTransfer(ids, orderType, recipientId, operatorOrgId);
        List<WorkOrderRecord> workOrderRecords = Lists.newArrayList();

        AuthUserDto adminUserAccountModel = userFeignClient.getAuthUserById(Long.valueOf(String.valueOf(recipientId))).getData();

        ids.forEach(r -> {
            WorkOrderRecord workOrderRecord = WorkOrderRecord.create(0, r, orderType, operatorId,
                    "举报工单转移到用户" + adminUserAccountModel.getUserName(), OperateMode.transfer);
            workOrderRecords.add(workOrderRecord);
        });
        recordDao.saveRecordList(workOrderRecords);

        return a;
    }

    @Override
    public List<StaffRealTimeWorkData> listStaffRealTimeWorkData(long userId) {
        return listUnHandleWork(userId, workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType()));
    }

    @Override
    public List<StaffRealTimeWorkData> listUnHandleWork(long userId, List<Integer> orderTypes) {
        //过滤出只属于捐转工单的
        List<Integer> jzTypes = workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType())
                .stream()
                .filter(item -> !item.equals(WorkOrderType.d0_1v1.getType()) || !item.equals(WorkOrderType.d0_1v1_tzb.getType()) )
                .collect(Collectors.toList());
        List<Integer> filterJzTypes = orderTypes.stream().filter(jzTypes::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterJzTypes)) {
            return Lists.newArrayList();
        }

        //生产环境
        Map<Integer, List<WorkOrderBase>> workTypeTBase = groupByProductEnv(userId);

        List<StaffRealTimeWorkData> realTimeWorkData = Lists.newArrayList();
        for (Integer jzType : jzTypes) {

            StaffRealTimeWorkData staffRealTimeWorkData = new StaffRealTimeWorkData();
            staffRealTimeWorkData.setOrderType(jzType);

            List<WorkOrderBase> baseList = workTypeTBase.getOrDefault(jzType, Lists.newArrayList());
            int unHandleWorkCount = countUnHandledWorkOrder(baseList, jzType);

            staffRealTimeWorkData.setPersonalUnHandle(unHandleWorkCount);
            realTimeWorkData.add(staffRealTimeWorkData);
        }
        return realTimeWorkData;
    }

    @Override
    public int qcTransfer(List<Long> ids, int orderType, long operatorId, long targetUserId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        int operatorOrgId = organizationService.getUserOrgId(targetUserId);
        List<WorkOrderBase> workOrderBases = workOrderDao.listById(ids);
        ids = workOrderBases.stream()
                .filter(t -> t.getHandleResult() == HandleResultEnum.undoing.getType() || t.getHandleResult() == HandleResultEnum.not_auto_assign.getType())
                .map(WorkOrderBase::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return -1;
        }
        //批量分配
        int a = workOrderDao.assignWorkOrders(ids, targetUserId, operatorOrgId, HandleResultEnum.doing.getType());
        if (a > 0) {
            List<WorkOrderRecord> workOrderRecords = Lists.newArrayList();
            String targetName = organizationService.getUserName(targetUserId);
            ids.stream().forEach(r -> {
                WorkOrderRecord workOrderRecord = WorkOrderRecord.create(0, r, orderType, operatorId, "批量手动分配给:" + targetName, OperateMode.manualAssignation);
                workOrderRecords.add(workOrderRecord);
            });
            recordDao.saveRecordList(workOrderRecords);
        }
        return a;
    }

    @Override
    public List<WorkOrderBase> queryListByBatch(long id, int handleResult, String endTime, int orderType, int limit) {
        if (id < 0 || orderType < 0){
            return Lists.newArrayList();
        }
        return workOrderDao.queryListByBatch(id, handleResult, endTime, orderType, limit);
    }

    @Override
    public int closeQcOrderList(List<Long> workIds, int handleResult){
        if (CollectionUtils.isEmpty(workIds)){
            return 0;
        }
        List<WorkOrderBase> workOrderBases = workOrderDao.listById(workIds);
        if (CollectionUtils.isEmpty(workOrderBases)){
            return 0;
        }
        Map<Long, WorkOrderBase> map =
                workOrderBases.stream().collect(Collectors.toMap(WorkOrderBase::getId, Function.identity(), (t1, t2)->t2));
        workIds = workOrderBases.stream()
                .filter(t -> t.getHandleResult() == HandleResultEnum.undoing.getType()).map(WorkOrderBase::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workIds)){
            return 0;
        }
        int result = workOrderDao.closeOrderById(workIds, handleResult);
        if (result > 0){
            List<WorkOrderRecord> records =  workIds.stream().map(workOrderId ->{
                WorkOrderRecord record = new WorkOrderRecord();
                WorkOrderBase workOrderBase =  map.get(workOrderId);
                if (workOrderBase != null){
                    record.setWorkOrderType(Optional.ofNullable(workOrderBase.getOrderType()).orElse(0));
                }
                record.setOperatorId(0);
                record.setComment("过期自动关闭");
                record.setOperateMode(OperateMode.handle.getType());
                record.setOperateDesc("关闭工单");
                record.setWorkOrderId(workOrderId);
                return record;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(records)){
                recordDao.saveRecordList(records);
            }
        }
        return result;
    }

    @Override
    public int openAgainQcWorkOrder(long workOrderId, long operatorId, String comment) {
        if (workOrderId < 0 || StringUtils.isBlank(comment)){
            return 0;
        }
        WorkOrderBase workOrderBase =  workOrderDao.getWorkOrderById(workOrderId);
        if (workOrderBase == null || workOrderBase.getHandleResult() != HandleResultEnum.overdue_auto_close.getType()){
            return 0;
        }
       int result = workOrderDao.freeWorkOrder(Lists.newArrayList(workOrderId),HandleResultEnum.overdue_auto_close.getType() ,HandleResultEnum.undoing.getType());
       if (result > 0){
           WorkOrderRecord record = new WorkOrderRecord();
           record.setWorkOrderType(workOrderBase.getOrderType());
           record.setOperatorId(operatorId);
           record.setComment(comment);
           record.setOperateMode(OperateMode.handle.getType());
           record.setOperateDesc("重新质检");
           record.setWorkOrderId(workOrderId);
           recordDao.saveRecord(record);
       }
       return result;
    }

    @Override
    public int deleteWorkOrderByIdAndOrderType(long id) {
        if (id < 0){
            return 0;
        }
        return workOrderDao.deleteWorkOrderByIdAndOrderType(id);
    }

    @Override
    public int changeQcWorkOrderStatus(long workOrderId, long operatorId) {
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workOrderId);
        if (Objects.isNull(workOrderBase)){
            return 0;
        }
        WorkOrderExt workOrderExt =
        daoExt.getWorkOrderExt(workOrderId, OrderExtName.qcOldOperationId.getName());
        if (workOrderExt == null){
            return 0;
        }
        long oldAssignerId = Long.parseLong(workOrderExt.getExtValue());
        int operatorOrgId = organizationService.getUserOrgId(oldAssignerId);
        int againQcType = 2;
        workOrderDao.updateOperator(workOrderId, oldAssignerId, operatorOrgId);
        workOrderDao.updateHandleResultByIdAndOrderType(workOrderId, HandleResultEnum.done.getType(), workOrderBase.getOrderType());
        daoExt.updateExtValue(List.of(workOrderId), OrderExtName.againQcType.getName(), Integer.toString(againQcType));
        WorkOrderRecord record = new WorkOrderRecord();
        record.setWorkOrderType(workOrderBase.getOrderType());
        record.setOperatorId(operatorId);
        record.setComment("结束处理");
        record.setOperateMode(OperateMode.handle.getType());
        record.setOperateDesc("处理工单");
        record.setWorkOrderId(workOrderId);
        int result = recordDao.saveRecord(record);
        return result;
    }

    @Override
    public int batchAllocation(List<Long> ids, int orderType, int operatorId, int recipientId) {
        AuthUserDto authUserDto = userFeignClient.getAuthUserById(Long.valueOf(String.valueOf(recipientId))).getData();

        String comment = "批量分配工单:" + authUserDto.getUserName();
        return assignWorkOrderService.assignWorkOrderByIds(ids, orderType, operatorId, recipientId, comment, OperateMode.batchAllocation);
    }

    @Override
    public List<RawWorkOrder> getRawWorkOrder(List<Long> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Collections.emptyList();
        }
        return workOrderDao.listRawByIds(workOrderIds);
    }

    @Override
    public List<WorkOrderBase> existExtValue(int caseId, int orderType, Date startTime, String extName, String extVal) {
        List<WorkOrderBase> workOrderBases = workOrderDao.listOrderByTypeAndTime(caseId, orderType, startTime);
        if (CollectionUtils.isEmpty(workOrderBases)) {
            return Collections.emptyList();
        }
        Set<Long> existWorkOrderIds = daoExt.getWorkOrderExtList(workOrderBases.stream()
                .map(WorkOrderBase::getId).collect(Collectors.toList()), extName, extVal)
                .stream().map(WorkOrderExt::getWorkOrderId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(existWorkOrderIds)) {
            return Collections.emptyList();
        }

        return workOrderBases.stream().filter(o -> existWorkOrderIds.contains(o.getId())).collect(Collectors.toList());
    }

    @Override
    public Response<List<QueryListResult>> getListByOrderIdListOld(List<Long> workOrderIdList) {
        List<WorkOrderBase> list = workOrderDao.listById(workOrderIdList);
        List<Integer> types = list.stream().map(WorkOrderBase::getOrderType).collect(Collectors.toList());
        OpResult<List<QueryListResult>> result = promoteVoList(list, types);
        if (result.isFail()) {
            return NewResponseUtil.makeResponse(result.getErrorCode().getCode(), result.getErrorCode().getMsg(), null);
        }
        return NewResponseUtil.makeSuccess(result.getData());
    }

    @Override
    public List<Long> getIdsByTypeResultTime(List<Integer> orderTypes, List<Integer> results, Date createTime) {
        if (CollectionUtils.isEmpty(orderTypes) || CollectionUtils.isEmpty(results) || Objects.isNull(createTime)) {
            return Lists.newArrayList();
        }
        return workOrderDao.getIdsByTypeResultTime(orderTypes, results, createTime);
    }

    @Override
    public List<Long> getIdsByTypeResultBetweenTime(List<Integer> orderTypes, List<Integer> results, Date createTime, Date endTime) {
        if (CollectionUtils.isEmpty(orderTypes) || CollectionUtils.isEmpty(results) || Objects.isNull(createTime) || Objects.isNull(endTime)) {
            return Lists.newArrayList();
        }
        return workOrderDao.getIdsByTypeResultBetweenTime(orderTypes, results, createTime, endTime);
    }

    @Override
    public List<OrderUserHandleResultCount> getCountByUserIdAndHandleResult(int orderType, long userId, List<Integer> handleResultList) {
        return workOrderDao.getCountByUserIdAndHandleResult(orderType, userId, handleResultList);
    }

    private Map<Integer, List<WorkOrderBase>> groupByProductEnv(long userId) {
        String dayFormat = "yyyy-mm-dd HH:mm:ss";
        DateTime now = DateTime.now();
        DateTime todayStartTime = DateTime.now().withTimeAtStartOfDay();
        DateTime lastDayStartTime = todayStartTime.minusDays(1);
        DateTime twoDaysAgoStartTime = todayStartTime.minusDays(2);

        int orderType = WorkOrderType.d0_1v1.getType();
        //如果有特种兵权限  就是特种兵的待分配
        PermissionParam permissionParam = new PermissionParam();
        permissionParam.setPermissions(workOrderTypeService.getAllPermissions());
        permissionParam.setUserId(userId);
        permissionParam.setAppCode(AuthSaasContext.getAuthAppCode());
        boolean flag = permissionFeignClient.validUserPermission(permissionParam).getData();
//        boolean flag =  seaAuthClientV1.hasPermissionWithUser(Long.valueOf(userId).intValue(),WorkOrderType.d0_1v1_tzb.getPermission()).getResult();
        if (flag){
            orderType = WorkOrderType.d0_1v1_tzb.getType();
        }
        //统计1v1捐转测试中待分配数量
        List<WorkOrderBase> workOrderBases = workOrderDao.listWorkFor1v1(userId, orderType, twoDaysAgoStartTime.toString(dayFormat), now.toString(dayFormat));

        log.info("listUnHandleWorkFor1v1返回条数:{}", workOrderBases.size());
        if (CollectionUtils.isEmpty(workOrderBases)) {
            return Maps.newHashMap();
        }
        //将workOrderBases按照天分组
        return workOrderBases.stream().collect(Collectors.groupingBy(item -> {
            DateTime createDate = new DateTime(item.getCreateTime());
            if (createDate.isAfter(todayStartTime)) {
                return WorkOrderType.d0_1v1_1.getType();
            } else if (createDate.isAfter(lastDayStartTime) && createDate.isBefore(todayStartTime)) {
                return WorkOrderType.d1_1v1.getType();
            } else {
                return WorkOrderType.d2_1v1.getType();
            }
        }));
    }


    private int countUnHandledWorkOrder(List<WorkOrderBase> baseList, int orderType) {
        if (CollectionUtils.isEmpty(baseList)) {
            return 0;
        }
        List<WorkOrderBase> unHandleBases = Collections.synchronizedList(Lists.newArrayList());
        Lists.partition(baseList.stream().map(WorkOrderBase::getCaseId).collect(Collectors.toList()), 20)
                .forEach(caseIds -> unHandleBases.addAll(workOrderDao.listByCaseIdsAndTypes(caseIds, Lists.newArrayList(orderType))
                        .stream()
                        .filter(item -> item.getHandleResult() == HandleResultEnum.undoing.getType())
                        .collect(Collectors.toList()))
                );
        return unHandleBases.size();
    }


    private OpResult<List<Integer>> getCaseList(QueryListParam queryListParam) {


        Set<Integer> set = Sets.newHashSet();
        List<Integer> caseIds = Lists.newArrayList();

        long userId = 0;
        if (StringUtils.isNotEmpty(queryListParam.getMobile())) {
            UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByMobile(queryListParam.getMobile());
            if (userInfoModel != null) {
                userId = userInfoModel.getUserId();
            }

            if (userId > 0) {
                FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListByUserId(userId);
                if (feignResponse != null && feignResponse.getData() != null) {
                    List<CrowdfundingInfo> l = feignResponse.getData();
                    if (l.size() > 5) {
                        l = feignResponse.getData().subList(0, 5);
                    }
                    set = l.stream().map(CrowdfundingInfo::getId).collect(Collectors.toSet());
                    caseIds = l.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
                }
            }

            //如果手机号和案例都填了   但是不包含 直接返回空值
            if (queryListParam.getCaseId() > 0 && CollectionUtils.isNotEmpty(set) && !set.contains(queryListParam.getCaseId())) {
                return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
            }
            //如果包含仅返回查询的
            if (set.contains(queryListParam.getCaseId())) {
                return OpResult.createSucResult(Lists.newArrayList(queryListParam.getCaseId()));
            }

            if (CollectionUtils.isEmpty(caseIds)) {
                return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
            }
        }

        if (queryListParam.getCaseId() > 0) {
            caseIds.add(queryListParam.getCaseId());
        }

        return OpResult.createSucResult(caseIds);

    }


    private Map<Long, String> getExtValues(List<WorkOrderBase> list, List<Integer> types, String name) {

        if (types.contains(WorkOrderType.ugcpinglun.getType())
                || types.contains(WorkOrderType.ugcprogress.getType())
                || types.contains(WorkOrderType.xiafaprogress.getType())
                || types.contains(WorkOrderType.funduseshenhe.getType())
                || types.contains(WorkOrderType.consultant_evaluation.getType())
                || types.contains(WorkOrderType.ugc_complaint_verify.getType())
                || types.contains(WorkOrderType.fxj_publicity_msg_audit.getType())
                || types.contains(WorkOrderType.fxj_comment_msg_audit.getType())
                || types.contains(WorkOrderType.report_split_draw.getType())
                || types.contains(WorkOrderType.risk_label_mark.getType())
                || types.contains(WorkOrderType.all_refund_audit.getType())
                || types.contains(WorkOrderType.precipitation.getType())
                || types.contains(WorkOrderType.money_back.getType())
                || types.contains(WorkOrderType.end_case_handle.getType())
                || types.contains(WorkOrderType.prompt_draw_cash.getType())
                || types.contains(WorkOrderType.draw_cash_apply.getType())
                || types.contains(WorkOrderType.second_pause.getType())
                || types.contains(WorkOrderType.long_tail.getType())
                || types.contains(WorkOrderType.modify_payee_info.getType())
        ) {

            List<Long> orderIds = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

            List<WorkOrderExt> wordIds = daoExt.getWorkOrderExts(orderIds, name);

            return wordIds.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, WorkOrderExt::getExtValue, (o1, o2) -> o2));
        }

        return Maps.newHashMap();
    }
}
