package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.GroupFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;
import com.shuidihuzhu.client.auth.saas.model.dto.SimpleUserVo;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.api.client.CfOrderStatFeignClient;
import com.shuidihuzhu.client.cf.api.model.CfOtherStatModel;
import com.shuidihuzhu.client.cf.workorder.model.OrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.dao.OrderStatDao;
import com.shuidihuzhu.workorder.dao.WorkOrderStatDao;
import com.shuidihuzhu.workorder.service.OrganizationService;
import com.shuidihuzhu.workorder.service.juanzhuanStatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/6/20
 */
@Service
@Slf4j
public class juanzhuanStatServiceImpl implements juanzhuanStatService {

    @Autowired
    private OrderStatDao orderStatDao;

    @Autowired
    private WorkOrderStatDao workOrderStatDao;

    @Autowired
    private AlarmClient alarmClient;

    @Autowired
    private CfOrderStatFeignClient cfOrderStatFeignClient;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private UserGroupFeignClient userGroupFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private GroupFeignClient groupFeignClient;

    @Override
    public void statAll() {

        int statType = OrderStat.all_type;

        String startTime = LocalDate.now()+" 00:00:00";

        LocalDateTime time = LocalDateTime.now();
        String endTime = time.withMinute(0).withSecond(0).withNano(0).toString();
        String batchTag = statType+"";

        OrderStat os = statOrderStat(statType,batchTag);

        int d0 = WorkOrderType.d0_1v1.getType();
        int d0_tzb = WorkOrderType.d0_1v1_tzb.getType();

        int d01 = WorkOrderType.d0_1v1_1.getType();
        List<Long> userIds = Lists.newArrayList();

        os.setD01Num(workOrderStatDao.getTypeAndResult(Lists.newArrayList(d0,d0_tzb), Lists.newArrayList(HandleResultEnum.undoing.getType()),startTime,endTime,0,OrderStat.user_id_limit,userIds));

        os.setD02Num(workOrderStatDao.getTypeAndResult(Lists.newArrayList(d01), Lists.newArrayList(HandleResultEnum.undoing.getType()),startTime,endTime,0,OrderStat.user_id_limit,userIds));

        int r =  orderStatDao.save(os);
        log.info("save stat all r={}",r);
        send(batchTag);

    }

    @Override
    public void statInner() {

        int statType = OrderStat.inner_type;
        String batchTag = statType+"";

        OrderStat os = statOrderStat(statType,batchTag);

        orderStatDao.save(os);

        send(batchTag);

    }

    @Override
    public void statOuter() {

        int statType = OrderStat.outer_type;
        String batchTag = statType+"";


        OrderStat os = statOrderStat(statType,batchTag);

        orderStatDao.save(os);

        send(batchTag);

    }

    @Override
    public void statOrg(List<Integer> orgIds) {

        for (Integer id : orgIds){
            Response<AuthGroupDto> dtoResponse = groupFeignClient.selectByGroupBizId(Long.valueOf(String.valueOf(id)));
            if (dtoResponse.notOk() || dtoResponse.getData() == null){
                continue;
            }
            long groupId = dtoResponse.getData().getId();
            Response<GroupMembersResultDto> authRpcResponse = userGroupFeignClient.getGroupMebmbers(groupId);
            GroupMembersResultDto or = Optional.ofNullable(authRpcResponse)
                    .filter(Response::ok)
                    .map(Response::getData).orElse(null);
            if (or != null){
                List<Long> userIds = or.getMembers().stream().map(SimpleUserVo::getId).collect(Collectors.toList());
                String batchTag = OrderStat.org_type+"_"+id;
                //一个组织机构一条数据
                OrderStat os = statOrderStat(OrderStat.org_type,userIds,batchTag);
                orderStatDao.save(os);
                send(batchTag);
            }
        }
    }

    @Override
    public void statUser() {

        String startTime = LocalDate.now()+" 00:00:00";

        LocalDateTime time = LocalDateTime.now();
        String endTime = time.withMinute(0).withSecond(0).withNano(0).toString();
        int d0 = WorkOrderType.d0_1v1.getType();
        int d0_tzb = WorkOrderType.d0_1v1_tzb.getType();

        int d01 = WorkOrderType.d0_1v1_1.getType();

        List<WorkOrderBase> list = workOrderStatDao.getOrderData(startTime,endTime,Lists.newArrayList(d0,d0_tzb),1,0,Lists.newArrayList());
        List<Long> d0UserIds = list.stream().map(WorkOrderBase::getOperatorId).distinct().collect(Collectors.toList());

        List<WorkOrderBase> d01List = workOrderStatDao.getOrderData(startTime,endTime,Lists.newArrayList(d01),1,0,Lists.newArrayList());
        List<Long> d01UserIds = d01List.stream().map(WorkOrderBase::getOperatorId).distinct().collect(Collectors.toList());

        d0UserIds.addAll(d01UserIds);

        List<Integer> userIds = d01UserIds.stream().map(Long::intValue).distinct().collect(Collectors.toList());

        Response<List<AuthUserDto>> authRpcResponse = userFeignClient.getAuthUserByIds(d01UserIds);
        Map<Long, String> map = Optional.ofNullable(authRpcResponse)
                .filter(Response::ok)
                .map(Response::getData).orElse(Lists.newArrayList())
                .stream()
                .collect(Collectors.toMap(AuthUserDto::getUserId, AuthUserDto::getUserName, (o1, o2) -> o2));

        Map<Integer,String> userMap = new HashMap<>();

        for (Map.Entry<Long, String> entry : map.entrySet()) {
            userMap.put(entry.getKey().intValue(), entry.getValue());
        }

        List<OrderStat> result = userIds.stream().map(r->{

            OrderStat os = statOrderStat(OrderStat.staff_type,Lists.newArrayList(Long.valueOf(r)),OrderStat.staff_type+"_"+r);
            if (os != null){
                os.setUserName(userMap.get(r));
            }
            return os;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(result)){
            orderStatDao.saveBatch(result);
        }

        sendUrl(OrderStat.staff_type);
    }

    @Override
    public List<OrderStat> getList(List<Integer> batchHours, int statType) {
        return orderStatDao.getStatList(batchHours,statType);
    }

    private OrderStat statOrderStat(int userType, String batchTag){
        return statOrderStat(userType,Lists.newArrayList(),batchTag);
    }

    private OrderStat statOrderStat(int statType,List<Long> userIds,String batchTag){

        String startTime = LocalDate.now()+" 00:00:00";

        LocalDateTime time = LocalDateTime.now();
        String endTime = time.withMinute(0).withSecond(0).withNano(0).toString();

        int d0_0 = WorkOrderType.d0_1v1.getType();
        int d0_tzb = WorkOrderType.d0_1v1_tzb.getType();
        List<Integer> d0 =Lists.newArrayList(d0_0,d0_tzb);

        int d0_1 = WorkOrderType.d0_1v1_1.getType();
        List<Integer> d01 =Lists.newArrayList(d0_1);

        int userIdLimit = OrderStat.user_id_limit;

        OrderStat os = new OrderStat();
        os.setUserName("-");
        os.setUserId(Optional.ofNullable(userIds).filter(CollectionUtils::isNotEmpty).map(r->r.get(0)).map(Long::intValue).orElse(0));
        os.setBatchHour(getBeforeHours());
        os.setBatchTag(batchTag);
        os.setStatType(statType);

        os.setD01AssignNum(workOrderStatDao.getTypeAndAssign(d0,startTime,endTime,statType,userIdLimit,userIds));

        int callNum = workOrderStatDao.getExtTypeStatNum(d0, OrderExtName.CallStatus.getName(),Lists.newArrayList("1","2"),startTime,endTime,statType,userIdLimit,userIds);
        int tNum = workOrderStatDao.getTypeAndResult(d0, Lists.newArrayList(),startTime,endTime,statType,userIdLimit,userIds);
        //拨打/全部
        os.setD01CallRate(getPercent(callNum,tNum));

        int callSuccNum = workOrderStatDao.getExtTypeStatNum(d0, OrderExtName.CallStatus.getName(),Lists.newArrayList("1"),startTime,endTime,statType,userIdLimit,userIds);
        //呼通/拨打
        os.setD01CallSuccRate(getPercent(callSuccNum,callNum));
        os.setD01CallNum(callSuccNum);

        os.setD02AssignNum(workOrderStatDao.getTypeAndAssign(d01,startTime,endTime,1,OrderStat.user_id_limit,userIds));
        callNum = workOrderStatDao.getExtTypeStatNum(d01, OrderExtName.CallStatus.getName(),Lists.newArrayList("1","2"),startTime,endTime,statType,userIdLimit,userIds);
        tNum = workOrderStatDao.getTypeAndResult(d01, Lists.newArrayList(),startTime,endTime,statType,userIdLimit,userIds);
        //拨打/全部
        os.setD02CallRate(getPercent(callNum,tNum));

        callSuccNum = workOrderStatDao.getExtTypeStatNum(d01, OrderExtName.CallStatus.getName(),Lists.newArrayList("1"),startTime,endTime,statType,userIdLimit,userIds);
        os.setD02CallSuccRate(getPercent(callSuccNum,callNum));
        os.setD02CallNum(callSuccNum);

        List<WorkOrderBase> list = workOrderStatDao.getOrderData(startTime,endTime,d0,statType,userIdLimit,userIds);
        List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());
        int allNum = caseIds.size();

        Response<CfOtherStatModel> CfOtherStatModelResponse = cfOrderStatFeignClient.getTotalNumToday(caseIds);
        CfOtherStatModel cfOtherStatModel = Optional.ofNullable(CfOtherStatModelResponse).filter(Response::ok)
                .map(Response::getData).orElse(null);

        if (cfOtherStatModel != null){
            os.setShareRate(getDivide(cfOtherStatModel.getShareNum(),allNum));
            os.setDonationRate(getDivide(cfOtherStatModel.getDonationNum(),allNum));
        }else {
            os.setShareRate("0");
            os.setDonationRate("0");
        }

        Response<Integer> feignResponse = cfOrderStatFeignClient.getVerfiyNumToday(caseIds);
        int verfiy = Optional.ofNullable(feignResponse).filter(Response::ok)
                .map(Response::getData).orElse(0);
        os.setVerifyRate(getDivide(verfiy,allNum));

        return os;
    }


    private int getBeforeHours(){
        LocalDateTime time = LocalDateTime.now();

       String format = time.format(DateTimeFormatter.ofPattern("yyyyMMddHH"));

       return Integer.valueOf(format);
    }

    private int getBeforeDayHours(int day){
        LocalDateTime time = LocalDateTime.now();

        String format = time.plusDays(day).format(DateTimeFormatter.ofPattern("yyyyMMddHH"));

        return Integer.valueOf(format);
    }


    private String getPercent(Integer divisor,Integer dividend){

        if (divisor == null || divisor == 0 || dividend== null || dividend == 0){
            return "0";
        }

        BigDecimal a = BigDecimal.valueOf(divisor);
        BigDecimal b = BigDecimal.valueOf(dividend);

        return a.multiply(BigDecimal.valueOf(100)).divide(b,2,RoundingMode.HALF_UP).toString();
    }

    private String getDivide(Integer divisor,Integer dividend){

        if (divisor == null || divisor == 0 || dividend== null || dividend == 0){
            return "0";
        }

        BigDecimal a = BigDecimal.valueOf(divisor);
        BigDecimal b = BigDecimal.valueOf(dividend);

        return a.divide(b,1,RoundingMode.HALF_UP).toString();
    }

    //发送企业微信消息
    private void sendUrl(int statType){
        int batchHour = getBeforeHours();
        String url = "https://sea-api.shuiditech.com/admin/stat/juanzhan-list-excel?batchHour="+batchHour;
        alarmClient.sendByGroup("wx-alarm-prod-*************","人员明细统计数据: "+url);
    }

    //发送企业微信消息
    private void send(String batchTag){

        OrderStat now = orderStatDao.getByBatchHourAndBatchTag(getBeforeHours(),batchTag);
        OrderStat y = orderStatDao.getByBatchHourAndBatchTag(getBeforeDayHours(-1),batchTag);
        OrderStat s = orderStatDao.getByBatchHourAndBatchTag(getBeforeDayHours(-7),batchTag);
        OrderStat ss = orderStatDao.getByBatchHourAndBatchTag(getBeforeDayHours(-14),batchTag);

        StringBuilder sb = new StringBuilder();
        sb.append("**")
                .append("日期").append("|")
                .append("类型").append("|")
                .append("d0第一次工单未分配量").append("|")
                .append("d0第一次工单已分配量").append("|")
                .append("d0第一次工单拨打率").append("|")
                .append("d0第一次工单呼通率").append("|")
                .append("d0第二次工单未分配量").append("|")
                .append("d0第二次工单已分配量").append("|")
                .append("d0第二次工单拨打率").append("|")
                .append("d0第二次工单呼通率").append("|")
                .append("当天转发/分配案例").append("|")
                .append("当天证实/分配案例").append("|")
                .append("当天捐单/分配案例").append("**")
                .append(" \n");
        sb.append("---|---|---|---|---|---|---|---|---|---|---|---|---").append(" \n");
        sb.append(getSB(now)).append(getSB(y)).append(getSB(s)).append(getSB(ss));

        alarmClient.sendMarkdownByGroup("wx-alarm-prod-*************",sb.toString());

    }

    private StringBuilder getSB(OrderStat o){

        StringBuilder sb = new StringBuilder();

        if (o == null){
            return sb;
        }

        sb.append(formatDate(o.getBatchHour())).append("|")
                .append(statTypeName(o.getStatType(),o.getBatchTag())).append("|")
                .append(o.getD01Num()).append("|")
                .append(o.getD01AssignNum()).append("|")
                .append(o.getD01CallRate()+"%").append("|")
                .append(o.getD01CallSuccRate()+"%").append("|")
                .append(o.getD01Num()).append("|")
                .append(o.getD02AssignNum()).append("|")
                .append(o.getD01CallRate()+"%").append("|")
                .append(o.getD01CallSuccRate()+"%").append("|")
                .append(o.getShareRate()).append("|")
                .append(o.getVerifyRate()).append("|")
                .append(o.getDonationRate())
        .append(" \n");
        return sb;
    }

    private String formatDate(int time){
        String batchHour = time+"";
        return batchHour.substring(0,4)+"."+batchHour.substring(4,6)+"."+batchHour.substring(6,8)+" "+batchHour.substring(8,batchHour.length());
    }

    private String statTypeName(int statType,String batchTag){
        if (statType == OrderStat.all_type){
            return "整体数据";
        }
        if (statType == OrderStat.inner_type){
            return "自有团队数据";
        }
        if (statType == OrderStat.outer_type){
            return "外包团队数据";
        }
        if (statType == OrderStat.org_type){
            int orgId = Integer.valueOf(batchTag.substring(batchTag.indexOf("_")+1));
            return organizationService.getOrgName(orgId);
        }
        if (statType == OrderStat.staff_type){
            return "人员数据";
        }
        return "";
    }
}
