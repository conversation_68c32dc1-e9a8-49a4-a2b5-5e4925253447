package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 */
@Service("fundUseWorkOrderService")
@Slf4j
public class FundUseWorkOrderServiceImpl extends WorkOrderFacade<FundUseWorkOrder, FundUseHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(FundUseWorkOrder wordOrder) {
        //check this fund_use_progress has create or not
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(FundUseWorkOrder wordOrder) {

        if (wordOrder.getOrderlevel() == 0) {
            wordOrder.setOrderlevel(OrderLevel.edium.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(FundUseHandleOrderParam param) {
        int result = workOrderDao.handle(param.getWorkOrderId(), param.getHandleResult());
        if (result > 0) {
            return OpResult.createSucResult(result);
        }
        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }


    @Override
    public OpResult<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param) {

        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize + 1;

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(), results, p, param.getWorkOrderId(),
                paging, param.getCaseId(), param.getStartTime(), param.getEndTime(), param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());

        List<Long> orderIds = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        Map<Integer, CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (o1, o2) -> o2));

        Map<Long, String> fundUseProgressIdMap = getExt(orderIds, OrderExtName.fundUseProgressId.getName());

        Map<Long, String> financeBusinessIdMap = getExt(orderIds, OrderExtName.BUSINESS_ID.getName());

        Map<Long, String> precipitationSourceMap = getExt(orderIds, OrderExtName.PRECIPITATION_SOURCE.getName());

        List<WorkOrderVO> voList = list.stream().filter(r -> {
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())) {
                return true;
            }

            if (r.getOrderType() == WorkOrderType.money_back.getType()) {
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}", r.getCaseId());
            return false;

        }).map(r -> {
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());

            if (r.getCaseId() > 0) {
                CrowdfundingInfo c = map.get(r.getCaseId());

                workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                workOrderVO.setTitle(c.getTitle());
                workOrderVO.setCaseUuid(c.getInfoId());
            }


            workOrderVO.setExtId(fundUseProgressIdMap.get(r.getId()));
            // 资金业务ID
            workOrderVO.setFinanceBusinessId(Long.parseLong(financeBusinessIdMap.getOrDefault(r.getId(), "0")));
            if (fundUseProgressIdMap.get(r.getId()) != null) {
                workOrderVO.setFundUseProgressId(Integer.valueOf(fundUseProgressIdMap.get(r.getId())));
            }
            String precipitationSource = precipitationSourceMap.getOrDefault(r.getId(), StringUtils.EMPTY);
            workOrderVO.setPrecipitationSource(precipitationSource);
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setOrderType(r.getOrderType());

            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);

    }


    public List<WorkOrderVO> funduseListByCaseId(int caseId, List<Integer> orderTypes) {
        List<WorkOrderBase> workOrderBases = workOrderDao.listByCaseIdAndTypeAndResult(caseId, orderTypes, Lists.newArrayList());
        if (CollectionUtils.isEmpty(workOrderBases)) {
            return Lists.newArrayList();
        }
        List<Long> orderIds = workOrderBases.stream().map(WorkOrderBase::getId).distinct().collect(Collectors.toList());

        Map<Long, String> fundUseProgressIdMap = getExt(orderIds, OrderExtName.fundUseProgressId.getName());
        List<WorkOrderVO> workOrderVOList = Lists.newArrayList();
        workOrderBases.forEach(item -> {
            WorkOrderVO workOrderVO = new WorkOrderVO();
            workOrderVO.setWorkOrderId(item.getId());
            workOrderVO.setCaseId(item.getCaseId());
            workOrderVO.setOrderLevel(item.getOrderlevel());
            workOrderVO.setUpdateTime(item.getUpdateTime());
            workOrderVO.setHandleTime(item.getHandleTime());
            workOrderVO.setHandleResult(item.getHandleResult());
            workOrderVO.setOperatorId(item.getOperatorId());
            workOrderVO.setOrderType(item.getOrderType());
            workOrderVO.setExtId(fundUseProgressIdMap.get(item.getId()));
            workOrderVO.setFundUseProgressId(Integer.valueOf(fundUseProgressIdMap.get(item.getId())));
            workOrderVOList.add(workOrderVO);
        });
        return workOrderVOList;
    }

    @Override
    public Map<Long, String> getExt(List<Long> orderIds, String name) {

        List<WorkOrderExt> list = daoExt.getWorkOrderExts(orderIds, name);

        if (CollectionUtils.isEmpty(list)) {
            return Maps.newHashMap();
        }

        return list.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, WorkOrderExt::getExtValue, (o1, o2) -> o2));
    }
}
