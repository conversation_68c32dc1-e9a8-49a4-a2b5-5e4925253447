package com.shuidihuzhu.workorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.api.chaifenbeta.crowdfunding.CrowdfundingChaiFenV2FeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.FirstCallOutTransfer;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.delegate.UserInfoDelegate;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.model.von.VonOrderCreateContext;
import com.shuidihuzhu.workorder.core.service.core.AssignStatService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.create.VonOrderCreatePlugin;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.*;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.enums.WorkOrderOperatorEnum;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.service.*;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AssignWorkOrderServiceImpl implements AssignWorkOrderService {

    @Autowired
    AssignateWorkOrderPublisher publisher;
    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private StaffStatusService staffStatusService;
    @Autowired
    private UserOperationRecordDao recordDao;
    @Resource
    private MeterRegistry meterRegistry;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private Analytics analytics;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private BaseWorkOrderService baseWorkOrderService;
    @Resource(name = "chuciWorkOrderService")
    private WorkOrderFacade chuciWorkOrderService;
    @Resource(name = "ugcWorkOrderService")
    private WorkOrderFacade ugcWorkOrderService;
    @Resource(name = "creditWorkOrderService")
    private WorkOrderFacade creditWorkOrderService;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private WorkOrderDaoExt daoExt;
    @Resource(name = "fundUseWorkOrderService")
    private FundUseWorkOrderServiceImpl fundUseWorkOrderService;

    @Resource
    private OrganizationService organizationService;

    @Resource(name = "ugcWorkOrderService")
    private UgcWorkOrderServiceImpl workOrderFacade;

    @Autowired
    private VonOrderCreatePlugin vonOrderCreatePlugin;
    @Autowired
    private UserFeignClient userFeignClient;
    @Resource
    private CrowdfundingChaiFenV2FeignClient crowdfundingChaiFenV2FeignClient;
    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Autowired
    private AssignStatService assignStatService;
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    private static final List<Integer> attachmentType = Arrays.asList(AttachmentTypeEnum.ATTACH_TREATMENT.value(), AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY.value(),
            AttachmentTypeEnum.ATTACH_TREATMENT_NOTE.value(), AttachmentTypeEnum.ATTACH_IN_HOSPITAL.value(),
            AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL.value());

    @Override
    public void assignWorkOrder(WorkOrderBase workOrder, Long operatorId) {
        // 招募系统处理分配工单单独处理
        WorkOrderOperatorEnum workOrderOperatorEnum = WorkOrderOperatorEnum.CF_WORK_ORDER;
        if (WorkOrderType.PR_WORK_ORDER_LIST.contains(workOrder.getOrderType())) {
            workOrderOperatorEnum = WorkOrderOperatorEnum.PR_SYSTEM_ASSIGN_WORK_ORDER;
        }
        
        int result = doAssign(workOrder, operatorId, workOrderOperatorEnum);
        if (result <= 0) {
            return;
        }
        WorkOrderRecord workOrderRecord = WorkOrderRecord.assignationRecord(workOrder.getId(), workOrder.getOrderType(), operatorId, 0);
        onAssigned(workOrder, operatorId, workOrderRecord);
    }

    @Override
    public int assignWorkOrderByIds(List<Long> orderIds, int orderType, long operatorId, long assignerId, String comment, OperateMode operateMode) {
        List<WorkOrderBase> orders = workOrderDao.getWorkOrderListByIds(orderIds);
        return assignWorkOrder(orders, orderType, operatorId, assignerId, comment, operateMode);
    }

    @Override
    public int assignWorkOrder(List<WorkOrderBase> workOrders, int orderType, long operatorId, long assignerId, String comment, OperateMode operateMode) {
        List<Long> orderIds = workOrders.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        int result = doAssign(orderIds, orderType, operatorId);
        if (result <= 0) {
            return 0;
        }
        List<WorkOrderRecord> workOrderRecords = orderIds.stream()
                .map(r -> WorkOrderRecord.assignationRecord(r, orderType, operatorId, assignerId, comment, operateMode))
                .collect(Collectors.toList());
        recordDao.saveRecordList(workOrderRecords);

        //发送工单状态变化事件
        for (WorkOrderBase v : workOrders) {
            EventPublishHelper.sendOrderStatusChangeOld(this, v);
            sendAssignSuccMsg(v, operatorId);
        }
        return result;
    }

    @Override
    public void assignWorkOrder(List<WorkOrderBase> workOrders, Long operatorId) {
        int orderType = workOrders.get(0).getOrderType();
        List<Long> orderIds = workOrders.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        // 将这批工单批量分配给一个操作人
        int result = doAssign(orderIds, orderType, operatorId);
        if (result > 0) {

            // 构建一个工单操作记录对象，主要是工单id，工单类型，操作人id，OperateMode（手动分配 || 分配工单(就是自动分配)）
            List<WorkOrderRecord> workOrderRecords = workOrders.stream()
                    .map(r -> WorkOrderRecord.assignationRecord(r.getId(), r.getOrderType(), operatorId, 0))
                    .collect(Collectors.toList());

            // 保存到 work_order_record 表
            recordDao.saveRecordList(workOrderRecords);
            WorkOrderBase workOrder = workOrders.get(0);
            //发送工单状态变化事件
            EventPublishHelper.sendOrderStatusChangeOld(this, workOrder);

            // 发送分配成功消息
            sendAssignSuccMsg(workOrder, operatorId);
            log.info("assignWorkOrder batch ids {}, operatorId {}", orderIds, operatorId);
        }
    }

    @Override
    public Response<Long> assignWorkOrder(long workOrderId, long operatorId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        assignWorkOrder(workOrder, operatorId);
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    /**
     * 手动分配
     *
     * @param workOrderId 工单id
     * @param operatorId  分给谁
     * @param assignerId  分配人
     * @return
     */
    @Override
    public Response<Long> manualAssignWorkOrder(long workOrderId, long operatorId, long assignerId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long oldOperatorId = workOrder.getOperatorId();
        WorkOrderOperatorEnum workOrderOperatorEnum = WorkOrderOperatorEnum.CF_WORK_ORDER;
        //招募的工单单独处理
        if (WorkOrderType.PR_WORK_ORDER_LIST.contains(workOrder.getOrderType())) {
            if (workOrder.getHandleResult() != HandleResultEnum.undoing.getType() &&
                    workOrder.getHandleResult() != HandleResultEnum.later_doing.getType()
                  && workOrder.getHandleResult() != HandleResultEnum.doing.getType()
                  && workOrder.getHandleResult() != HandleResultEnum.wait_for_matching.getType()) {
                return NewResponseUtil.makeError(ErrorCode.WORK_ORDER_HAS_ASSIGNED);
            }
            workOrderOperatorEnum = WorkOrderOperatorEnum.PR_MANUAL_ASSIGN_WORK_ORDER;
        } else {
            if (oldOperatorId > 0) {
                return NewResponseUtil.makeError(ErrorCode.WORK_ORDER_HAS_ASSIGNED);
            }
        }

        int orderType = workOrder.getOrderType();
        Boolean offline = staffStatusService.isOffline(operatorId, orderType);
        if (offline == null) {
            return NewResponseUtil.makeResponse(
                    ErrorCode.SYSTEM_ERROR.getCode(),
                    "该员工需要上线才可分配工单",
                    workOrderId
            );
        }
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if (!WorkOrderType.REPORT_TYPES.contains(orderType)){
            if (offline) {
                return NewResponseUtil.makeResponse(
                        ErrorCode.STAFF_HAS_OFFLINE.getCode(),
                        "该员工的" + workOrderTypeDO.getMsg() + "为离线状态，无法分配工单。",
                        workOrderId
                );
            }
        }

        int result = doAssign(workOrder, operatorId, workOrderOperatorEnum);
        if (result > 0) {
            WorkOrderRecord workOrderRecord = WorkOrderRecord.assignationRecord(workOrder.getId(), orderType, operatorId, assignerId);
            onAssigned(workOrder, operatorId, workOrderRecord);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Long> callbackOrderAndAssign(long workOrderId, long operatorId, long assignerId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long oldOperatorId = workOrder.getOperatorId();

        if (workOrder.getHandleResult() != HandleResultEnum.doing.getType() &&
                workOrder.getHandleResult() != HandleResultEnum.later_doing.getType()) {
            return NewResponseUtil.makeError(ErrorCode.ORDER_RELEASE_FAIL);
        }

        // 回收工单
        if (operatorId <= 0) {
            return callbackOrder(workOrderId, assignerId, "回收成功");
        }

        int orderType = workOrder.getOrderType();
        Boolean offline = staffStatusService.isOffline(operatorId, orderType);
        if (offline == null) {
            return NewResponseUtil.makeResponse(
                    ErrorCode.SYSTEM_ERROR.getCode(),
                    "该员工需要上线才可分配工单",
                    workOrderId
            );
        }
        if (offline) {
            WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
            return NewResponseUtil.makeResponse(
                    ErrorCode.STAFF_HAS_OFFLINE.getCode(),
                    "该员工的" + workOrderTypeDO.getMsg() + "为离线状态，无法分配工单。",
                    workOrderId
            );
        }

        // 自动回收
        if (oldOperatorId > 0) {
            callbackOrder(workOrderId, assignerId, "回收成功");
        }

        int result = doAssign(workOrder, operatorId, WorkOrderOperatorEnum.CF_WORK_ORDER);
        if (result > 0) {
            WorkOrderRecord workOrderRecord = WorkOrderRecord.assignationRecord(workOrder.getId(), orderType, operatorId, assignerId);
            onAssigned(workOrder, operatorId, workOrderRecord);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Long> reprocessWorkOrder(long workOrderId, long operatorId, String comment) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_ERROR);
        }
        boolean canReprocess = baseWorkOrderService.checkCanReprocess(workOrder);
        if (!canReprocess) {
            return NewResponseUtil.makeError(ErrorCode.CAN_NOT_REPROCESS);
        }

        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        int orderType = workOrder.getOrderType();
        chuciWorkOrder.setReprocessOrder(String.valueOf(workOrderId));
        chuciWorkOrder.setCaseId(workOrder.getCaseId());
        chuciWorkOrder.setOrderType(orderType);
        chuciWorkOrder.setOperatorId(operatorId);
        chuciWorkOrder.setHandleResult(HandleResultEnum.doing.getType());

        OpResult<Long> createResult;
        switch (orderType) {
            case WorkOrderTypeConstants.SHENHE:
            case WorkOrderTypeConstants.HUIFANG:
            case WorkOrderTypeConstants.DIANHUA_SHENHE:
            case WorkOrderTypeConstants.YILIAO_SHENHE:
            case WorkOrderTypeConstants.HIGH_RISK_SHENHE:
            case WorkOrderTypeConstants.BU_CHONG_YI_YUAN_XIN_XI:
            case WorkOrderTypeConstants.AI_PHOTO:
            case WorkOrderTypeConstants.AI_ERCI:
                createResult = chuciWorkOrderService.doCreate(chuciWorkOrder);
                break;
            case WorkOrderTypeConstants.UGC_PINGLUN:
            case WorkOrderTypeConstants.UGC_PROGRESS:
                createResult = creatUgc(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.UGC_COMPLAINT_VERIFY:
                createResult = createComplaintVerify(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.ZENGXIN_NORMAL:
            case WorkOrderTypeConstants.ZENGXIN_RISK:
                createResult = createCredit(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.XIAFA_PROGRESS:
                createResult = createSupplyProgress(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.REPORT_SPLIT_DRAW:
            case WorkOrderTypeConstants.FUND_USE_SHENHE:
                createResult = creatFundUseProgress(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.AI_CONTENT:
                createResult = creatAiContent(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.PICTURE_PUBLICITY_REVIEW:
                createResult = creatPicturePublicityReview(workOrder, operatorId);
                break;
            case WorkOrderTypeConstants.CONTENT:
            case WorkOrderTypeConstants.QC_HIGH_RISK_QUALITY_INSPECTION:
                createResult = createCommon(workOrder, operatorId);
                break;
            default:
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        if (createResult.isFail()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }

        long newWorkOrderId = Optional.of(createResult).map(OpResult::getData).orElse(0L);

        // 老工单重新审核记录
        WorkOrderRecord reprocessRecord = WorkOrderRecord.assignationReprocessRecord(workOrder.getId(),
                workOrder.getOrderType(), operatorId, comment + "-【新工单id：" + newWorkOrderId + "】");
        recordDao.saveRecord(reprocessRecord);

        // 新工单领取记录
        WorkOrderRecord assignRecord = WorkOrderRecord.assignationRecord(newWorkOrderId,
                0, operatorId, 0, "【老工单id：" + workOrder.getId() + "】", null);
        recordDao.saveRecord(assignRecord);

        return NewResponseUtil.makeSuccess(workOrderId);
    }

    private OpResult<Long> createCommon(WorkOrderBase workOrder, long operatorId) {
        WorkOrderCreateParam p = new WorkOrderCreateParam();
        p.setOrderType(workOrder.getOrderType());
        p.setCaseId(workOrder.getCaseId());
        p.setComment("重新审核创建");

        // 重新审核直接指定处理人
        p.setOperatorId(operatorId);
        p.setHandleResult(HandleResultEnum.doing.getType());

        // 全量复制ext
        List<WorkOrderExt> workOrderExt = workOrderExtService.getWorkOrderExt(workOrder.getId());
        p.setWorkOrderExt(workOrderExt);

        Response<VonOrderCreateContext> resp = vonOrderCreatePlugin.create(p);
        return OpResult.createWithResponse(resp, VonOrderCreateContext::getCreateOrderId);
    }

    private OpResult<Long> creatAiContent(WorkOrderBase workOrder, long operatorId) {
        if (!Objects.equals(workOrder.getHandleResult(), HandleResultEnum.stop_case.getType())) {
            log.info("reprocessWorkOrder creatAiContent list is error {}", workOrder);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
        }
        ChuciWorkOrder chuciWorkOrder = new ChuciWorkOrder();
        chuciWorkOrder.setReprocessOrder(String.valueOf(workOrder.getId()));
        chuciWorkOrder.setCaseId(workOrder.getCaseId());
        chuciWorkOrder.setOrderType(WorkOrderType.ai_erci.getType());
        chuciWorkOrder.setOperatorId(operatorId);
        chuciWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        return chuciWorkOrderService.create(chuciWorkOrder);
    }

    private OpResult<Long> creatPicturePublicityReview(WorkOrderBase workOrder, long operatorId) {

        Response<List<String>> fundingAttachment = crowdfundingChaiFenV2FeignClient.getFundingAttachment(workOrder.getCaseId());
        if (Objects.isNull(fundingAttachment) || fundingAttachment.notOk() || CollectionUtils.isEmpty(fundingAttachment.getData())) {
            log.info("reprocessWorkOrder creatPicturePublicityReview fundingAttachment is error {}", workOrder);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
        }

        List<Integer> attachmentIdList = fundingAttachment.getData()
                .stream()
                .map(m -> JSONObject.parseObject(m, CrowdfundingAttachmentVo.class))
                .filter(f -> attachmentType.contains(f.getType()))
                .map(CrowdfundingAttachmentVo::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attachmentIdList)) {
            log.info("reprocessWorkOrder creatPicturePublicityReview attachmentId is empty {} {}", workOrder, fundingAttachment);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
        }

        WorkOrderVO data = baseWorkOrderService.getWorkOrderById(workOrder.getId());

        WorkOrderCreateParam newWorkOrder = new WorkOrderCreateParam();
        newWorkOrder.setCaseId(workOrder.getCaseId());
        newWorkOrder.setOrderType(workOrder.getOrderType());
        newWorkOrder.setOrderlevel(data.getOrderLevel());
        newWorkOrder.setOperatorId(operatorId);
        newWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        newWorkOrder.addExt(OrderExtName.picturePublicityReviewAttachment, attachmentIdList);

        Response<VonOrderCreateContext> response = vonOrderCreatePlugin.create(VonOrderCreateContext.createParam(newWorkOrder));
        return OpResult.createSucResult(response.getData().getCreateOrderId());
    }


    private OpResult<Long> createCredit(WorkOrderBase workOrder, long operatorId) {
        CreditWorkOrder creditWorkOrder = new CreditWorkOrder();
        BeanUtils.copyProperties(workOrder, creditWorkOrder);
        creditWorkOrder.setReprocessOrder(String.valueOf(workOrder.getId()));
        creditWorkOrder.setOperatorId(operatorId);
        creditWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        return creditWorkOrderService.doCreate(creditWorkOrder);
    }


    private OpResult<Long> creatUgc(WorkOrderBase workOrder, long operatorId) {
        UgcWorkOrder ugcWorkOrder = new UgcWorkOrder();
        ugcWorkOrder.setCaseId(workOrder.getCaseId());
        ugcWorkOrder.setOrderType(workOrder.getOrderType());
        ugcWorkOrder.setOperatorId(operatorId);
        ugcWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        ugcWorkOrder.setReprocessOrder(String.valueOf(workOrder.getId()));
        List<String> names = Lists.newArrayList(OrderExtName.wordId.getName(), OrderExtName.extId.getName(), OrderExtName.contentType.getName(), OrderExtName.headImageUrl.getName(), OrderExtName.verificationId.getName(), OrderExtName.medicalWorkType.getName());
        List<WorkOrderExt> list = daoExt.getWorkOrderExtByName(workOrder.getId(), names);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> map = list.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue));
            ugcWorkOrder.setWordId(map.get(OrderExtName.wordId.getName()));
            ugcWorkOrder.setExtId(map.get(OrderExtName.extId.getName()));
            ugcWorkOrder.setContentType(map.get(OrderExtName.contentType.getName()));
            ugcWorkOrder.setHeadImageUrl(map.get(OrderExtName.headImageUrl.getName()));
            ugcWorkOrder.setVerificationId(map.get(OrderExtName.verificationId.getName()));
            ugcWorkOrder.setMedicalWorkType(map.get(OrderExtName.medicalWorkType.getName()));
            // 取上个工单状态
            List<WorkOrderVO> sourceList = workOrderFacade.getOrderListByIds(Lists.newArrayList(workOrder.getId()));
            if (CollectionUtils.isNotEmpty(sourceList)) {
                WorkOrderVO sourceWorkOrderVo = sourceList.get(0);
                if (Objects.nonNull(sourceWorkOrderVo)) {
                    ugcWorkOrder.setMedicalStatus(sourceWorkOrderVo.getMedicalStatus());
                }
            }
        }


        OpResult<Long> opResult = ugcWorkOrderService.doCreate(ugcWorkOrder);
        return opResult;
    }

    private OpResult<Long> createComplaintVerify(WorkOrderBase workOrder, long operatorId) {

        WorkOrderVO data = baseWorkOrderService.getWorkOrderById(workOrder.getId());

        WorkOrderCreateParam newWorkOrder = new WorkOrderCreateParam();

        newWorkOrder.setCaseId(workOrder.getCaseId());
        newWorkOrder.setOrderType(workOrder.getOrderType());
        newWorkOrder.setOrderlevel(data.getOrderLevel());
        newWorkOrder.setOperatorId(operatorId);
        newWorkOrder.setHandleResult(HandleResultEnum.doing.getType());

        // 医护工单类型 1 是 0 否
        WorkOrderExt workOrderExt1 = new WorkOrderExt();
        workOrderExt1.setExtName(OrderExtName.medicalWorkType.getName());
        workOrderExt1.setExtValue(data.getMedicalWorkType());

        // 证实记录id
        WorkOrderExt workOrderExt2 = new WorkOrderExt();
        workOrderExt2.setExtName(OrderExtName.verificationId.getName());
        workOrderExt2.setExtValue(data.getVerificationId());

        // 内容类型
        WorkOrderExt workOrderExt3 = new WorkOrderExt();
        workOrderExt3.setExtName(OrderExtName.contentType.getName());
        workOrderExt3.setExtValue("6"); // 对应 AdminUGCTask.Content枚举 -> 证实 6

        WorkOrderExt workOrderExt4 = new WorkOrderExt();
        workOrderExt4.setExtName(OrderExtName.avalibaleComplaintList.getName());
        workOrderExt4.setExtValue(StringUtils.isEmpty(data.getAvalibaleComplaintList()) ? JSON.toJSONString(Lists.newArrayList()) :data.getAvalibaleComplaintList());

        ArrayList<WorkOrderExt> workOrderExts = Lists.newArrayList(workOrderExt1, workOrderExt2, workOrderExt3, workOrderExt4);
        newWorkOrder.setWorkOrderExt(workOrderExts);

        Response<VonOrderCreateContext> response = vonOrderCreatePlugin.create(VonOrderCreateContext.createParam(newWorkOrder));
        OpResult<Long> result = OpResult.createSucResult(response.getData().getCreateOrderId());
        return result;

    }

    private OpResult<Long> creatFundUseProgress(WorkOrderBase workOrder, long operatorId) {
        List<String> names = Lists.newArrayList(OrderExtName.fundUseProgressId.getName(), OrderExtName.crowdfundingProgressId.getName());
        List<WorkOrderExt> list = daoExt.getWorkOrderExtByName(workOrder.getId(), names);
        if (CollectionUtils.isEmpty(list)) {
            log.info("reprocessWorkOrder creatFundUseProgress list is error {}", workOrder);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
        }
        Map<String, String> map = list.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue, (x, y) -> y));
        String fundUseProgressIdValue = map.get(OrderExtName.fundUseProgressId.getName());
        if (StringUtils.isEmpty(fundUseProgressIdValue)) {
            log.info("reprocessWorkOrder creatFundUseProgress list is error {}", workOrder);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_CON);
        }
        WorkOrderCreateParam workOrderCreateParam = new WorkOrderCreateParam();
        workOrderCreateParam.setOrderType(workOrder.getOrderType());
        workOrderCreateParam.setCaseId(workOrder.getCaseId());
        workOrderCreateParam.setOrderlevel(workOrder.getOrderlevel());
        workOrderCreateParam.setComment("重新审核创建");
        // 重新审核直接指定处理人
        workOrderCreateParam.setOperatorId(operatorId);
        workOrderCreateParam.setHandleResult(HandleResultEnum.doing.getType());
        workOrderCreateParam.addExt(OrderExtName.fundUseProgressId, fundUseProgressIdValue);
        Response<VonOrderCreateContext> vonOrderCreateContextResponse = vonOrderCreatePlugin.create(workOrderCreateParam);
        long workOrderId = Optional.ofNullable(vonOrderCreateContextResponse)
                .map(Response::getData)
                .map(VonOrderCreateContext::getCreateOrderId)
                .orElse(0L);

        String crowdfundingProgressIdValue = map.get(OrderExtName.crowdfundingProgressId.getName());
        if (StringUtils.isNotEmpty(crowdfundingProgressIdValue) && workOrderId > 0) {
            daoExt.insertWorkOrderExt(workOrderId, OrderExtName.crowdfundingProgressId.name(), crowdfundingProgressIdValue);
        }
        return OpResult.createSucResult(workOrderId);
    }

    private OpResult<Long> createSupplyProgress(WorkOrderBase workOrder, long operatorId) {
        UgcWorkOrder ugcWorkOrder = new UgcWorkOrder();
        ugcWorkOrder.setCaseId(workOrder.getCaseId());
        ugcWorkOrder.setOrderType(workOrder.getOrderType());
        ugcWorkOrder.setOperatorId(operatorId);
        ugcWorkOrder.setHandleResult(HandleResultEnum.doing.getType());
        ugcWorkOrder.setReprocessOrder(String.valueOf(workOrder.getId()));
        List<String> names = Lists.newArrayList(OrderExtName.supplyProgressId.getName());
        List<WorkOrderExt> list = daoExt.getWorkOrderExtByName(workOrder.getId(), names);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> map = list.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue));
            ugcWorkOrder.setSupplyProgressId(map.get(OrderExtName.supplyProgressId.getName()));
        }
        return ugcWorkOrderService.doCreate(ugcWorkOrder);
    }

    @Override
    public Response<Long> releaseWorkOrder(long workOrderId, long operatorId, String comment) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = baseWorkOrderService.freeWorkOrder(Lists.newArrayList(workOrderId));
        if (result > 0) {
            List<WorkOrderRecord> records = WorkOrderRecord.freeRecord(Lists.newArrayList(workOrder), comment, operatorId);
            recordDao.saveRecordList(records);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }


    private Response<Long> callbackOrder(long workOrderId, long operatorId, String comment) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = baseWorkOrderService.callbackOrder(Lists.newArrayList(workOrderId));
        if (result > 0) {
            WorkOrderRecord r = WorkOrderRecord.create(workOrder, operatorId, comment, OperateMode.callback);
            recordDao.saveRecord(r);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Long> qcAgainAssignWorkOrder(long workOrderId, long operatorId, long assignerId, String comment) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result =
                workOrderDao.updateHandleResultByIdAndOrderType(workOrderId, HandleResultEnum.doing.getType(), workOrder.getOrderType());
        if (result > 0) {
            int operatorOrgId = organizationService.getUserOrgId(assignerId);
            String userName = organizationService.getUserName(assignerId);
            workOrderDao.updateOperator(workOrder.getId(), assignerId, operatorOrgId);
            WorkOrderRecord record = new WorkOrderRecord();
            record.setOperatorId(operatorId);
            comment = "修改原因：" + comment + "；选择的修改同学：" + userName;
            record.setComment(StringUtils.length(comment) >= 1000 ? OperateMode.reprocess.getMsg() : comment);
            record.setOperateMode(OperateMode.changeQc.getType());
            record.setOperateDesc(OperateMode.changeQc.getMsg());
            record.setWorkOrderId(workOrderId);
            recordDao.saveRecord(record);
            daoExt.insertWorkOrderExt(workOrderId, OrderExtName.againQcType.getName(), String.valueOf(1));
            daoExt.insertWorkOrderExt(workOrderId, OrderExtName.qcOldOperationId.getName(),
                    String.valueOf(workOrder.getOperatorId()));
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    /**
     * @param workOrderId 工单id
     * @param operatorId  操作分配的人
     * @param assignerId  接收工单的人
     * @return
     */
    @Override
    public Response<Long> qcAssignWorkOrder(long workOrderId, long operatorId, long assignerId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        long oldOperatorId = workOrder.getOperatorId();
        if (oldOperatorId > 0) {
            return NewResponseUtil.makeError(ErrorCode.WORK_ORDER_HAS_ASSIGNED);
        }
        int result = doAssign(workOrder, assignerId, WorkOrderOperatorEnum.CF_WORK_ORDER);
        if (result > 0) {
            var userName = organizationService.getUserName(assignerId);
            WorkOrderRecord workOrderRecord = WorkOrderRecord.qcAssignationRecord(workOrder.getId(), workOrder.getOrderType(), operatorId, userName);
            onAssigned(workOrder, assignerId, workOrderRecord);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public List<Long> transferWorkOrder(List<Long> workOrderList, Long destOptUid, Long operatorId, String reason, List<Integer> unChangeHandleResult) {
        Map<Long,WorkOrderBase> validWorkOrderMap = workOrderDao.listById(workOrderList).stream()
                .filter(workOrder -> workOrder.getOperatorId() == operatorId &&
                        Set.of(HandleResultEnum.doing.getType(), HandleResultEnum.later_doing.getType(),HandleResultEnum.wait_for_matching.getType())
                                .contains(workOrder.getHandleResult()))
                .collect(Collectors.toMap(WorkOrderBase::getId, Function.identity()));
        if (MapUtils.isEmpty(validWorkOrderMap)) {
            return Lists.newArrayListWithCapacity(0);
        }

        Map<Integer, Long> inOrderTypeMap = validWorkOrderMap.values().stream()
                .collect(Collectors.groupingBy(WorkOrderBase::getOrderType, Collectors.counting()));

        int operatorOrgId = organizationService.getUserOrgId(destOptUid);
        // 由于预匹配转单不改处理状态，单独处理
        Set<Long> unChangeHandleResultSet = new HashSet<>();
        Set<Long> original = new HashSet<>();
        for (Long aLong : validWorkOrderMap.keySet()) {
            if(unChangeHandleResult.contains(validWorkOrderMap.get(aLong).getHandleResult())) {
                unChangeHandleResultSet.add(aLong);
            } else {
                original.add(aLong);
            }
        }
        if(CollectionUtils.isNotEmpty(unChangeHandleResultSet)) {
            workOrderDao.transferWorkOrders(unChangeHandleResultSet, destOptUid, operatorOrgId, null);
        }
        if(CollectionUtils.isNotEmpty(original)) {
            workOrderDao.transferWorkOrders(original, destOptUid, operatorOrgId, HandleResultEnum.doing.getType());
        }
        inOrderTypeMap.keySet().forEach(orderType->staffStatusService.addStaffAssignTime(destOptUid, orderType));

        log.info("Transfer order success, orderIds:{}, destOptUid:{}, operatorId:{}", validWorkOrderMap.keySet(), destOptUid, operatorId);

        List<WorkOrderRecord> recordLogs = validWorkOrderMap.values().stream()
                .map(workOrder -> WorkOrderRecord.create(0, workOrder.getId(), workOrder.getOrderType(), operatorId,
                        Optional.ofNullable(reason).filter(StringUtils::isNotBlank).orElse("用户(" + operatorId + ")转单给(" + destOptUid + ")"), OperateMode.transferOrder))
                .collect(Collectors.toList());
        recordDao.saveRecordList(recordLogs);

        inOrderTypeMap.forEach((key, value) -> meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT, OperationStat.OPERATION, OperationStat.transfer_succ,
                OperationStat.ORDERTYPE, String.valueOf(key)).increment(value));

        //发送分配工单event
        inOrderTypeMap.keySet().forEach(orderType -> publisher.publishEvent(new AssignateWorkOrderEvent(this, orderType)));
        return Lists.newArrayList(validWorkOrderMap.keySet());
    }

    @Override
    public void simpleTransfer(List<SimpleTransferParam> simpleTransferParams) {
        if (CollectionUtils.isEmpty(simpleTransferParams)) {
            return;
        }
        Map<Long, RawWorkOrder> workOrderMap = workOrderDao.listRawByIds(simpleTransferParams.stream().map(SimpleTransferParam::getWorkOrderId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(RawWorkOrder::getId, Function.identity()));
        for (SimpleTransferParam simpleTransferParam : simpleTransferParams) {
            RawWorkOrder rawWorkOrder = workOrderMap.get(simpleTransferParam.getWorkOrderId());
            if (rawWorkOrder == null) {
                log.warn("Order is not found {}!", simpleTransferParam.getWorkOrderId());
                continue;
            }
            int result = doAssign(rawWorkOrder.getId(), rawWorkOrder.getOrderType(), rawWorkOrder.getHandleResult(), simpleTransferParam.getDestOptUid());
            if (result > 0) {
                WorkOrderRecord workOrderRecord = WorkOrderRecord.assignationRecord(rawWorkOrder.getId(), rawWorkOrder.getOrderType(),
                        simpleTransferParam.getOperatorId(), simpleTransferParam.getDestOptUid(), simpleTransferParam.getReason(),  null);
                onAssigned(rawWorkOrder, simpleTransferParam.getDestOptUid(), workOrderRecord);
            }
        }
    }

    private int doAssign(long orderId, int orderType, int handleResult, long operatorId) {
        log.info("assign work-order orderId:{}, , operatorId:{}", orderId, operatorId);
        HandleResultEnum handleResultEnum = HandleResultEnum.getFromType(handleResult);
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if (workOrderTypeDO == null) {
            log.warn("Illegal work order type {} !", orderType);
            return 0;
        }
        if (handleResult == HandleResultEnum.undoing.getType()) {
            handleResult = HandleResultEnum.doing.getType();
        }

        int operatorOrgId = organizationService.getUserOrgId(operatorId);
        int result = workOrderDao.assignPrWorkOrder(orderId, operatorId, operatorOrgId, handleResult);
        staffStatusService.addStaffAssignTime(operatorId, orderType);

        log.info("doAssignate workorder assignWorkOrder:{}.", result);

        meterRegistry.counter(
                OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION, OperationStat.assignate_succ,
                OperationStat.ORDERTYPE, String.valueOf(orderType),
                OperationStat.ONE_TYPE, workOrderTypeService.getOneFromTwo(orderType)+""
        ).increment();
        return result;
    }

    private int doAssign(WorkOrderBase workOrder, Long operatorId, WorkOrderOperatorEnum workOrderOperatorEnum) {
        int orderType = workOrder.getOrderType();
        log.info("assign work-order orderId:{}, caseId:{}, orderType:{}, operatorId:{}",
                workOrder.getId(), workOrder.getCaseId(), orderType, operatorId);
        int operatorOrgId = organizationService.getUserOrgId(operatorId);
        int result;

        // 如果是招募的工单
        if (WorkOrderType.PR_WORK_ORDER_LIST.contains(workOrder.getOrderType())) {
            result = dealAssignForPr(workOrder, operatorId, workOrderOperatorEnum, operatorOrgId);
        } else {
            result = workOrderDao.assignWorkOrder(workOrder.getId(), operatorId, operatorOrgId, HandleResultEnum.doing.getType());
        }

        staffStatusService.addStaffAssignTime(operatorId, workOrder.getOrderType());

        log.info("doAssignate workorder assignWorkOrder:{}.", result);

        meterRegistry.counter(
                OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION, OperationStat.assignate_succ,
                OperationStat.ORDERTYPE, String.valueOf(orderType),
                OperationStat.ONE_TYPE, workOrderTypeService.getOneFromTwo(orderType)+""
        ).increment();
        return result;
    }

    private int dealAssignForPr(WorkOrderBase workOrder, Long operatorId, WorkOrderOperatorEnum workOrderOperatorEnum, int operatorOrgId) {
        int result;
        int handleResult = workOrder.getHandleResult() == HandleResultEnum.wait_for_matching.getType() ?
                workOrder.getHandleResult() :
                HandleResultEnum.doing.getType();
        log.info("AssignWorkOrderServiceImpl.dealAssignForPr orderId:{}, orderType:{}, operatorId:{}, handleResult:{}, workOrderOperatorEnum:{} ",
                workOrder.getId(), workOrder.getOrderType(), operatorId, handleResult, workOrderOperatorEnum.getName());
        // 如果是系统分配
        if (workOrderOperatorEnum == WorkOrderOperatorEnum.PR_SYSTEM_ASSIGN_WORK_ORDER) {
            // assignWorkOrder 如果 operator_id = 0 系统才可分配
            result = workOrderDao.assignWorkOrder(workOrder.getId(), operatorId, operatorOrgId, handleResult);
        } else {
            // 如果是手动分配 assignPrWorkOrder 中没判断 operator_id = 0 代表手动可以强制分配
            result = workOrderDao.assignPrWorkOrder(workOrder.getId(), operatorId, operatorOrgId, handleResult);
        }
        return result;
    }

    private int doAssign(List<Long> orderIds, int orderType, Long operatorId) {
        log.info("assign work-order orderType:{}, operatorId:{}", orderType, operatorId);
        // 获取处理人的组织id
        int operatorOrgId = organizationService.getUserOrgId(operatorId);
        // 修改work_order表，更新工单的分配状态，设置处理人id，处理人组织id，以及更新状态为处理中
        int result = workOrderDao.assignWorkOrders(orderIds, operatorId, operatorOrgId, HandleResultEnum.doing.getType());
        // 更新处理人的最新分配时间
        staffStatusService.addStaffAssignTime(operatorId, orderType);

        // redis统计领单数量
        assignStatService.onOrderAssign(orderIds, orderType, operatorId);

        log.info("doAssignate workorder assignWorkOrder:{}.", result);

        meterRegistry.counter(
                OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION,
                OperationStat.assignate_succ,
                OperationStat.ORDERTYPE,
                String.valueOf(orderType)
        ).increment();
        return result;
    }


    private void onAssigned(WorkOrderBase workOrder, Long operatorId, WorkOrderRecord workOrderRecord) {

        track(workOrder, operatorId);
        recordDao.saveRecord(workOrderRecord);

        //发送工单状态变化事件
        EventPublishHelper.sendOrderStatusChangeOld(this, workOrder);

        sendAssignSuccMsg(workOrder, operatorId);
    }

    /**
     * 发送分配成功消息
     *
     * @param workOrder
     * @param operatorId
     */
    private void sendAssignSuccMsg(WorkOrderBase workOrder, Long operatorId) {

        if (workOrder == null) {
            return;
        }
        WorkOrderVO vo = new WorkOrderVO();

        vo.setWorkOrderId(workOrder.getId());
        vo.setOrderType(workOrder.getOrderType());
        vo.setOperatorId(operatorId);

        Message<WorkOrderVO> message = MessageBuilder.createWithPayload(vo)
                .setTags(CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC)
                .addKey(CfClientMQTagCons.WORK_ORDER_ASSIGN_SUCC, vo.getWorkOrderId())
                .setDelayLevel(DelayLevel.S1)
                .build();
        MessageResult mr = producer.send(message);
        log.info("WORK_ORDER_ASSIGN_SUCC message={} MessageResult={}", message, mr);

        MaliMQComponent.builder()
                .setTags(MQTag.VON_ORDER_ASSIGN_SUCCESS)
                .setPayload(JSON.toJSONString(workOrder))
                .send();
    }

    private void track(WorkOrderBase workOrder, long adminUserId) {
        int orderType = workOrder.getOrderType();

        if (WorkOrderType.yilei.getType() != orderType && WorkOrderType.erlei.getType() != orderType && WorkOrderType.sanlei.getType() != orderType) {
            return;
        }

        int caseId = workOrder.getCaseId();
        FeignResponse<List<CrowdfundingInfo>> feignResponse = crowdfundingFeignClient.getCrowdfundingListById(Lists.newArrayList(caseId));

        if (Objects.isNull(feignResponse) || feignResponse.notOk()) {
            return;
        }
        List<CrowdfundingInfo> list = feignResponse.getData();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        CrowdfundingInfo cf = list.get(0);
        if (Objects.isNull(cf)) {
            return;
        }

        UserInfoModel userInfo = userInfoDelegate.getUserInfoByUserId(cf.getUserId());
        if (Objects.isNull(userInfo)) {
            return;
        }

        String mis = "";
        Response<AuthUserDto> response = userFeignClient.getAuthUserById(adminUserId);
        if (response.ok() && response.getData() != null){
            mis = response.getData().getLoginName();
        }

        String cfUserId = String.valueOf(cf.getUserId());
        Map<String, Object> map = Maps.newHashMap();
        map.put("user_id", cfUserId);
        map.put("mobile", userInfo.getMobile());
        map.put("case_id", cf.getInfoId());
        map.put("employee_id", mis);

        FirstCallOutTransfer firstCallOutTransfer = new FirstCallOutTransfer();
        firstCallOutTransfer.setUser_tag(String.valueOf(cfUserId));
        firstCallOutTransfer.setUser_tag_type(UserTagTypeEnum.userid);
        firstCallOutTransfer.setCase_id(cf.getInfoId());
        firstCallOutTransfer.setInfo_id(Long.valueOf(cf.getId()));
        firstCallOutTransfer.setUser_encrypt_mobile(StringUtils.isBlank(userInfo.getCryptoMobile()) ? "" : userInfo.getCryptoMobile());
        firstCallOutTransfer.setEmp_id(mis);

        try {
            analytics.track(firstCallOutTransfer);
            // analytics.track(cfUserId, true, "cf", "first_call_out_transfer", map);
            log.info("shouci workorder assignate track parameter:{}", JSON.toJSONString(firstCallOutTransfer));
        } catch (Exception e) {
            log.info("shouci workorder assignate track exception", e);
        }
    }
}
