package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CfUserStatFeignClient;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseCountStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.ShouciWorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/4/17
 */
@Service("shouciWorkOrderService")
@Slf4j
public class ShouciWorkOrderServiceImpl extends WorkOrderFacade<ShouciWorkOrder, ShouciHandleOrderParam,ShouciWorkOrderListParam> {

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private CfUserStatFeignClient cfUserStatFeignClient;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private AssignateWorkOrderPublisher publisher;

    @Autowired
    private ShouciWorkOrderDao shouciWorkOrderDao;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(ShouciWorkOrder wordOrder) {


        WorkOrderBase workOrderBase = getWorkOrderBase(wordOrder.getCaseId(),null,Lists.newArrayList());

        if (workOrderBase != null){
            log.info("shouci vlidate caseId={}",wordOrder.getCaseId());
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(ShouciWorkOrder wordOrder) {

        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(wordOrder.getCaseId()));

        if (feignResponse == null || feignResponse.notOk()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }
        List<CrowdfundingInfo> list = feignResponse.getData();
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }
        CrowdfundingInfo cf = list.get(0);

        FeignResponse<List<CfCaseCountStat>> response = cfUserStatFeignClient.getCfCaseStatByInfoIdList(Lists.newArrayList(cf.getInfoId()));

        if (response == null || response.notOk()){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        List<CfCaseCountStat> stats = response.getData();
        if (CollectionUtils.isEmpty(stats)){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        CfCaseCountStat stat = stats.get(0);

        wordOrder.setOrderType(getOrderType(cf,stat));

        wordOrder.setOrderlevel(OrderLevel.low.getType());

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(ShouciHandleOrderParam param) {

        int result = workOrderDao.handle(param.getWorkOrderId(),param.getHandleResult());

        //更新处理结果
        if (result > 0){

            long workOrderId = param.getWorkOrderId();
            orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.OperComment, param.getOperComment());

            orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.CallStatus, param.getCallStatus());

            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    @Override
    public OpResult getOrderList(ShouciWorkOrderListParam param) {

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize()+1;

        int limit = param.getPage()*param.getPageSize();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).map(r->Integer.valueOf(r)).collect(Collectors.toList());
        List<WorkOrderBaseVo> list = shouciWorkOrderDao.getWorkorderList(param.getUserId(),
                param.getOrderType(),p,limit,param.getCaseId(),param.getStartTime(),
                param.getEndTime(),param.getCallStatus(),results);


        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }

        List<Long> workOrderId = list.stream().map(r->r.getId()).collect(Collectors.toList());

        final Map<Long,String> callMap = Maps.newHashMap();
        if (StringUtils.isEmpty(param.getCallStatus())){

            List<WorkOrderExt> extList = orderExtService.getWorkOrderExts(workOrderId,OrderExtName.CallStatus.getName());
            callMap.putAll(extList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,WorkOrderExt::getExtValue)));
        }

        List<Integer> caseIds = list.stream().map(r->r.getCaseId()).distinct().collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        if (CollectionUtils.isEmpty(feignResponse)){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{

            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setChannel(c.getChannel());
            workOrderVO.setCaseUuid(c.getInfoId());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCallStatus(r.getCallStatus());
            if (MapUtils.isNotEmpty(callMap)){
                workOrderVO.setCallStatus(callMap.get(r.getId()));
            }
            workOrderVO.setCaseUserId(c.getUserId());

            return workOrderVO;

        }).collect(Collectors.toList());

        //如果是处理中   按照优先级排序一下
        if (results.contains(HandleResultEnum.doing.getType())){
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId)).collect(Collectors.toList());
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getOrderLevel).reversed()).collect(Collectors.toList());
        }

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }

    private int getOrderType(CrowdfundingInfo crowdfundingInfo,CfCaseCountStat stat){

        LocalTime localTime = LocalTime.now();
        int hour = localTime.getHour();
        int amount = crowdfundingInfo.getAmount();
        int share = stat.getShareCount();

        if (hour >= 22 && hour <= 23) {
            if (amount >= 2124 * 100) {
                return WorkOrderType.yilei.getType();
            }
            if ((amount < 2124 * 100 && amount >= 20 * 100) || share > 13) {
                return WorkOrderType.erlei.getType();
            }
            return WorkOrderType.sanlei.getType();
        }
        //0<=hour<22
        if (amount >= 230 * 100 || share >= 7) {
            return WorkOrderType.yilei.getType();
        }
        if ((amount < 230 * 100 && amount > 0) || share >= 4) {
            return WorkOrderType.erlei.getType();
        }
        return WorkOrderType.sanlei.getType();

    }

   private WorkOrderBase getWorkOrderBase(int caseId,List<Integer>  types,List<Integer> results){

        if (CollectionUtils.isEmpty(types)){
            types = Lists.newArrayList(WorkOrderType.yilei.getType(),
                    WorkOrderType.erlei.getType(),
                    WorkOrderType.sanlei.getType());
        }

       WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(caseId,types,results);

        return workOrderBase;
   }

    public void changeShouciType(int caseId){

        log.info("changeShouciType caseId={}",caseId);
        List<Integer> types = Lists.newArrayList(WorkOrderType.erlei.getType(), WorkOrderType.sanlei.getType());

        WorkOrderBase workOrderBase = getWorkOrderBase(caseId,types,Lists.newArrayList(HandleResultEnum.undoing.getType()));

        if (workOrderBase == null){
            log.info("changeShouciType no workOrderBase caseId={}",caseId);
            return;
        }
        FeignResponse<List<CrowdfundingInfo>> feignResponse = client.getCrowdfundingListById(Lists.newArrayList(caseId));

        if (feignResponse == null || feignResponse.notOk()){
            log.info("changeShouciType no case  caseId={}",caseId);
            return;
        }
        List<CrowdfundingInfo> list = feignResponse.getData();
        if (CollectionUtils.isEmpty(list)){
            log.info("changeShouciType no case  caseId={}",caseId);
            return;
        }
        CrowdfundingInfo cf = list.get(0);

        FeignResponse<List<CfCaseCountStat>> response = cfUserStatFeignClient.getCfCaseStatByInfoIdList(Lists.newArrayList(cf.getInfoId()));

        if (response == null || response.notOk()){
            log.info("changeShouciType no share  caseId={}",caseId);
            return;
        }
        List<CfCaseCountStat> stats = response.getData();
        if (CollectionUtils.isEmpty(stats)){
            log.info("changeShouciType no share  caseId={}",caseId);
            return;
        }

        CfCaseCountStat stat = stats.get(0);
        int type = getOrderType(cf,stat);
        log.info("changeShouciType caseId={} new type={} type={}",caseId,type,workOrderBase.getOrderType());
        if (type == workOrderBase.getOrderType()){
            return;
        }
        int result = workOrderDao.updateOrdertype(workOrderBase.getId(),type,Lists.newArrayList(HandleResultEnum.undoing.getType()));
        //修改类型触发分配
        if (result > 0){
            publisher.publishEvent(new AssignateWorkOrderEvent(this,type));
        }
    }

}
