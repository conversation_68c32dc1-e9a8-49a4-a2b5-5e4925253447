package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderAnalysisVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO;
import com.shuidihuzhu.workorder.service.IAnalysisWorkHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-11-22 11:53
 **/
@Service
@Slf4j
public class AnalysisWorkHandlerServiceImpl implements IAnalysisWorkHandlerService {

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    private static List<WorkOrderOneTypeDO> needAnalysisOneType = Lists.newArrayList();
    private static DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    private static List<Integer> unHandleStatus = Lists.newArrayList(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType());

    private static List<Integer> laterHandleStatus = Lists.newArrayList(HandleResultEnum.later_doing.getType());

    private static List<String> halfHours = Lists.newArrayList();

    @PostConstruct
    public void init() {
        needAnalysisOneType = workOrderTypeService.getAllOneType();
        needAnalysisOneType.remove(new WorkOrderOneTypeDO(OneTypeEnum.yiyuanheshi.getType(), OneTypeEnum.yiyuanheshi.getMsg()));
        LocalDateTime startLocalTime = new LocalDateTime(2019, 10, 10, 21, 30);
        LocalDateTime endLocalTime = startLocalTime.plusDays(1);
        while (startLocalTime.isBefore(endLocalTime)) {
            halfHours.add(startLocalTime.toString("HH:mm"));
            startLocalTime = startLocalTime.plusMinutes(30);
        }
    }


    /**
     * 整体查询，查询前一天21:30-当天21:30
     */
    @Override
    public List<WorkOrderAnalysisVO> wholeAnalysis() {
        DateTime dateTime = new DateTime();
        DateTime endTime = dateTime.withHourOfDay(21).withMinuteOfHour(30).withSecondOfMinute(0).withMillisOfSecond(0);
        DateTime startTime = endTime.plusDays(-1);

        DateTime todayHandleEnd = dateTime.withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0);
        DateTime todayHandleStart = todayHandleEnd.plusHours(-1);
        List<WorkOrderBase> workOrderBases = workOrderDao.analysisWholeDayData(startTime.toString(formatter), endTime.toString(formatter));

        List<WorkOrderBase> todayChainIndexData = workOrderDao.getChainIndexData(startTime.toString(formatter), todayHandleEnd.toString(formatter),
                todayHandleStart.toString(formatter), todayHandleEnd.toString(formatter));

        //上周同时间段
        List<WorkOrderBase> lastWeekChainIndexData = workOrderDao.getChainIndexData(startTime.plusWeeks(-1).toString(formatter), todayHandleEnd.plusWeeks(-1).toString(formatter),
                todayHandleStart.plusWeeks(-1).toString(formatter), todayHandleEnd.plusWeeks(-1).toString(formatter));

        Map<Integer, List<WorkOrderBase>> groupByOneType =
                workOrderBases.stream().collect(Collectors.groupingBy(item -> workOrderTypeService.getOneFromTwo(item.getOrderType())));

        Map<Integer, List<WorkOrderBase>> todayHandleChainIndexData =
                todayChainIndexData.stream().filter(item -> !(unHandleStatus.contains(item.getHandleResult()) || laterHandleStatus.contains(item.getHandleResult())))
                        .collect(Collectors.groupingBy(item -> workOrderTypeService.getOneFromTwo(item.getOrderType())));

        Map<Integer, List<WorkOrderBase>> lastWeekHandleChainIndexData =
                lastWeekChainIndexData.stream().filter(item -> !(unHandleStatus.contains(item.getHandleResult()) || laterHandleStatus.contains(item.getHandleResult())))
                        .collect(Collectors.groupingBy(item -> workOrderTypeService.getOneFromTwo(item.getOrderType())));

        List<WorkOrderAnalysisVO> analysisVOList = Lists.newArrayList();

        for (WorkOrderOneTypeDO oneTypeEnum : needAnalysisOneType) {
            List<WorkOrderBase> oneTypeWorkBaseList = groupByOneType.getOrDefault(oneTypeEnum.getOneTypeCode(), Lists.newArrayList());
            WorkOrderAnalysisVO analysisVO = createWholeAnalysisVO(oneTypeWorkBaseList, oneTypeEnum.getOneTypeCode());
            //添加环比信息
            String chainIndex = "";
            int todayHandleChainIndexCount = todayHandleChainIndexData.getOrDefault(oneTypeEnum.getOneTypeCode(), Lists.newArrayList()).size();
            int lastWeekHandleChainIndexCount = lastWeekHandleChainIndexData.getOrDefault(oneTypeEnum.getOneTypeCode(), Lists.newArrayList()).size();
            if (lastWeekHandleChainIndexCount == 0) {
                log.warn("无上周环比数据,startTime:{},endTime:{}", startTime, endTime);
                chainIndex = "+∞%";
            } else {
                if (todayHandleChainIndexCount == 0) {
                    chainIndex = "-∞%";
                } else {
                    chainIndex = Math.round(((float)todayHandleChainIndexCount / (float) lastWeekHandleChainIndexCount - 1) * 100) + "%";
                }
            }
            analysisVO.setChainIndex(chainIndex);
            analysisVOList.add(analysisVO);
        }
        return analysisVOList;
    }

    @Override
    public List<WorkOrderAnalysisVO> periodAnalysis(int oneType) {
        DateTime dateTime = new DateTime();
        DateTime endTime = dateTime.withHourOfDay(21).withMinuteOfHour(30).withSecondOfMinute(0).withMillisOfSecond(0);
        DateTime startTime = endTime.plusDays(-1);
        List<WorkOrderBase> workOrderBases = workOrderDao.analysisWholeDayData(startTime.toString(formatter), endTime.toString(formatter));
        Map<String, List<WorkOrderBase>> groupByTimeOrderBaseList = workOrderBases.stream()
                .filter(item -> workOrderTypeService.getOneFromTwo(item.getOrderType()) == oneType)
                .collect(Collectors.groupingBy(AnalysisWorkHandlerServiceImpl::groupByTime));
        List<WorkOrderAnalysisVO> analysisVOList = Lists.newArrayList();
        for (int i = 0; i < halfHours.size(); i++) {
            List<WorkOrderBase> periodWorkBaseList = groupByTimeOrderBaseList.getOrDefault(halfHours.get(i), Lists.newArrayList());
            int next = (i < (halfHours.size() - 1)) ? i + 1 : 0;
            String period = halfHours.get(i) + "-" + halfHours.get(next);
            WorkOrderAnalysisVO periodAnalysisVO = createPeriodAnalysisVO(periodWorkBaseList, period);
            periodAnalysisVO.setOneType(oneType);
            analysisVOList.add(periodAnalysisVO);
        }
        return analysisVOList;
    }


    //查看创建时间在哪个时间范围内
    private static String groupByTime(@NotNull WorkOrderBase workOrderBase) {
        DateTime dateTime = new DateTime(workOrderBase.getCreateTime());
        String hourOfDay = dateTime.toString("HH");
        int minuteOfHour = dateTime.getMinuteOfHour();
        return minuteOfHour >= 30 ? hourOfDay + ":30" : hourOfDay + ":00";
    }


    private WorkOrderAnalysisVO createPeriodAnalysisVO(List<WorkOrderBase> workOrderBaseList, String period) {
        WorkOrderAnalysisVO analysisVO = new WorkOrderAnalysisVO();
        analysisVO.setPeriod(period);
        if (CollectionUtils.isEmpty(workOrderBaseList)) {
            return analysisVO;
        }
        return getWorkOrderAnalysisVO(workOrderBaseList, analysisVO);
    }


    private WorkOrderAnalysisVO createWholeAnalysisVO(List<WorkOrderBase> oneTypeWorkBaseList, int oneType) {
        WorkOrderAnalysisVO analysisVO = new WorkOrderAnalysisVO();
        analysisVO.setOneType(oneType);
        if (CollectionUtils.isEmpty(oneTypeWorkBaseList)) {
            return analysisVO;
        }
        return getWorkOrderAnalysisVO(oneTypeWorkBaseList, analysisVO);
    }


    @NotNull
    private WorkOrderAnalysisVO getWorkOrderAnalysisVO(List<WorkOrderBase> workOrderBaseList, WorkOrderAnalysisVO analysisVO) {
        int createNum = workOrderBaseList.size();
        int unHandleNum = 0;
        int laterNum = 0;
        Map<Integer, List<WorkOrderBase>> groupByHandleResult = workOrderBaseList.stream().collect(Collectors.groupingBy(WorkOrderBase::getHandleResult));
        for (Integer handleStatus : unHandleStatus) {
            unHandleNum = unHandleNum + groupByHandleResult.getOrDefault(handleStatus, Lists.newArrayList()).size();
        }
        for (Integer later : laterHandleStatus) {
            laterNum = laterNum + groupByHandleResult.getOrDefault(later, Lists.newArrayList()).size();
        }
        int handleNum = createNum - unHandleNum - laterNum;
        int handlePercent = (int) Math.round((double)handleNum / (double) createNum * 100);
        analysisVO.setUnHandleNum(unHandleNum);
        analysisVO.setCreateNum(createNum);
        analysisVO.setLaterNum(laterNum);
        analysisVO.setHandleNum(handleNum);
        analysisVO.setHandlePercent(handlePercent);
        return analysisVO;
    }

}
