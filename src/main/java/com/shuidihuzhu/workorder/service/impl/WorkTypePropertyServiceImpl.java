package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkTypePropertyDao;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WorkTypePropertyServiceImpl implements WorkTypePropertyService {

    @Autowired
    private WorkTypePropertyDao workTypePropertyDao;

    @Resource
    private WorkOrderTypeService workOrderTypeService;


    @Override
    public WorkTypeProperty.UpdatePropertyParam selectByPropertyList(int firstWorkOrder) {

        WorkTypeProperty.UpdatePropertyParam result = new WorkTypeProperty.UpdatePropertyParam();

        // 延迟工单的限制
//        List<Integer> secondWorkOrders = workOrderTypeService.getByOneLevel(firstWorkOrder);
//        if (CollectionUtils.isNotEmpty(secondWorkOrders)) {
        List<WorkTypeProperty> delayCounts = workTypePropertyDao.selectByPropertyTypes(WorkTypeProperty.FIRST_WORK_TYPE,
                    Lists.newArrayList(firstWorkOrder),
                Lists.newArrayList(WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode()));

        // 现阶段是 同一个一级分类工单下的二级工单的 延后处理数目相同
        if (CollectionUtils.isNotEmpty(delayCounts)) {
            result.setDelayOrderCount(Integer.valueOf(delayCounts.get(0).getPropertyValue()));
        }

        // 查找可以同时在线的数据
        List<WorkTypeProperty> onLineProperty = workTypePropertyDao.selectByPropertyTypes(WorkTypeProperty.FIRST_WORK_TYPE,
                Lists.newArrayList(firstWorkOrder), Lists.newArrayList(WorkTypeProperty.PropertyType.BATH_ON_LINE_ORDER_TYPE.getCode()));
        if (CollectionUtils.isNotEmpty(onLineProperty)) {
            result.setAllowBothOnLine(true);
            result.setOnLineSecondWorkOrderTypes(onLineProperty.stream()
                    .map(WorkTypeProperty::getPropertyValue).map(Integer::valueOf).collect(Collectors.toList()));
        }

        result.setFirstWorkOrderType(firstWorkOrder);
        return result;
    }

    @Override
    public void updateWorkOrderProperty(WorkTypeProperty.UpdatePropertyParam param) {

        log.info("更新工单的设置 param:{}", param);
        if (param == null) {
            return;
        }

        List<WorkTypeProperty> needAddWorkTypes = Lists.newArrayList();
        // 删除以前的设置
        workTypePropertyDao.deleteWorkOrderPropertys(WorkTypeProperty.FIRST_WORK_TYPE,
                Lists.newArrayList(param.getFirstWorkOrderType()),
                Lists.newArrayList(WorkTypeProperty.PropertyType.BATH_ON_LINE_ORDER_TYPE.getCode(),
                        WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode()));

        WorkTypeProperty delayProperty = new WorkTypeProperty();
        delayProperty.setOrderLevel(WorkTypeProperty.FIRST_WORK_TYPE);
        delayProperty.setOrderType(param.getFirstWorkOrderType());
        delayProperty.setPropertyType(WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode());
        delayProperty.setPropertyValue(String.valueOf(param.getDelayOrderCount()));
        needAddWorkTypes.add(delayProperty);

        if (CollectionUtils.isNotEmpty(param.getOnLineSecondWorkOrderTypes())) {
            for (int orderType : param.getOnLineSecondWorkOrderTypes()) {
                WorkTypeProperty property = new WorkTypeProperty();

                property.setOrderLevel(WorkTypeProperty.FIRST_WORK_TYPE);
                property.setOrderType(param.getFirstWorkOrderType());
                property.setPropertyType(WorkTypeProperty.PropertyType.BATH_ON_LINE_ORDER_TYPE.getCode());
                property.setPropertyValue(String.valueOf(orderType));

                needAddWorkTypes.add(property);
            }
        }

        if (CollectionUtils.isNotEmpty(needAddWorkTypes)) {
            log.info("新增工单类型的设置.param:{}", needAddWorkTypes);
            workTypePropertyDao.addPropertyList(needAddWorkTypes);
        }

    }


    @Override
    public List<WorkTypeProperty> selectByPropertyTypes(int orderLevel, List<Integer> orderTypes,
                                                        List<Integer> propertyType) {

        return workTypePropertyDao.selectByPropertyTypes(orderLevel, orderTypes, propertyType);
    }

    @Override
    public int getMaxDelayCount(int orderType) {

        Integer oneLevelOrder = workOrderTypeService.getOneFromTwo(orderType);
        if (oneLevelOrder == null) {
            log.info("不能通过二级分类找到一级分类 orderType:{}", orderType);
            return WorkOrderConfig.shenhe_yanhou_count;
        }

        List<WorkTypeProperty> workTypes = workTypePropertyDao.selectByPropertyTypes(WorkTypeProperty.FIRST_WORK_TYPE,
                Lists.newArrayList(oneLevelOrder), Lists.newArrayList(WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode()));

        if (CollectionUtils.isNotEmpty(workTypes)) {
            return Integer.valueOf(workTypes.get(0).getPropertyValue());
        }

        return WorkOrderConfig.shenhe_yanhou_count;
    }

    @Override
    public int getSecondMaxDelayCount(int orderType, WorkTypeProperty.PropertyType propertyType) {

        List<WorkTypeProperty> workTypes = workTypePropertyDao.selectByPropertyTypes(WorkTypeProperty.SECOND_WORK_TYPE,
                Lists.newArrayList(orderType), Lists.newArrayList(propertyType.getCode()));

        if (CollectionUtils.isNotEmpty(workTypes)) {
            return Integer.parseInt(workTypes.get(0).getPropertyValue());
        }
        return WorkOrderType.PR_ONLINE_WORK_ORDER_LIST.contains(orderType) ? WorkOrderConfig.PR_ONLINE_LIMIT:
                WorkOrderConfig.shenhe_yanhou_count;
    }


    @Override
    public List<WorkTypeProperty.UpdatePropertyParam> selectByType(int orderLevel, List<Integer> orderTypes, List<Integer> propertyType) {
        if (orderLevel > 2 || CollectionUtils.isEmpty(orderTypes)){
            return Lists.newArrayList();
        }
        List<WorkTypeProperty.UpdatePropertyParam> resultList = Lists.newArrayList();

        // 延迟工单的限制
        List<WorkTypeProperty> delayCounts = workTypePropertyDao.selectByPropertyTypes(orderLevel,
                orderTypes, propertyType);

        // 现阶段是 同一个一级分类工单下的二级工单的 延后处理数目相同
        if (CollectionUtils.isEmpty(delayCounts)) {
            return Lists.newArrayList();
        }
        WorkTypeProperty.UpdatePropertyParam  updatePropertyParam = new WorkTypeProperty.UpdatePropertyParam();
        for (WorkTypeProperty workTypeProperty : delayCounts) {
            if (orderLevel == WorkTypeProperty.FIRST_WORK_TYPE) {
                updatePropertyParam.setFirstWorkOrderType(workTypeProperty.getOrderType());
            } else {
                updatePropertyParam.setSecondWorkOrderType(workTypeProperty.getOrderType());
            }
            if (workTypeProperty.getPropertyType() == WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode()){
                updatePropertyParam.setDelayOrderCount(Integer.parseInt(workTypeProperty.getPropertyValue()));
            }
            if (workTypeProperty.getPropertyType() == WorkTypeProperty.PropertyType.HANDLING_COUNT.getCode()){
                updatePropertyParam.setHandleOrderCount(Integer.parseInt(workTypeProperty.getPropertyValue()));
            }
        }
        resultList.add(updatePropertyParam);
        return resultList;
    }

    @Override
    public void updateSecondWorkOrderProperty(WorkTypeProperty.UpdatePropertyParam param) {
        log.info("更新工单的设置 param:{}", param);
        if (param == null) {
            return;
        }
        List<WorkTypeProperty> needAddWorkTypes = Lists.newArrayList();
        // 删除以前的设置
        workTypePropertyDao.deleteWorkOrderPropertys(WorkTypeProperty.SECOND_WORK_TYPE,
                Lists.newArrayList(param.getSecondWorkOrderType()),
                Lists.newArrayList(
                        WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode(),
                        WorkTypeProperty.PropertyType.HANDLING_COUNT.getCode()));

        WorkTypeProperty delayProperty = new WorkTypeProperty();
        delayProperty.setOrderLevel(WorkTypeProperty.SECOND_WORK_TYPE);
        delayProperty.setOrderType(param.getSecondWorkOrderType());
        delayProperty.setPropertyType(WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT.getCode());
        delayProperty.setPropertyValue(String.valueOf(param.getDelayOrderCount()));
        needAddWorkTypes.add(delayProperty);

        WorkTypeProperty handleProperty = new WorkTypeProperty();
        handleProperty.setOrderLevel(WorkTypeProperty.SECOND_WORK_TYPE);
        handleProperty.setOrderType(param.getSecondWorkOrderType());
        handleProperty.setPropertyType(WorkTypeProperty.PropertyType.HANDLING_COUNT.getCode());
        handleProperty.setPropertyValue(String.valueOf(param.getHandleOrderCount()));
        needAddWorkTypes.add(handleProperty);

        if (CollectionUtils.isNotEmpty(needAddWorkTypes)) {
            log.info("新增工单类型的设置.param:{}", needAddWorkTypes);
            workTypePropertyDao.addPropertyList(needAddWorkTypes);
        }
    }

}
