package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.PrHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.PrOnlineWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.PrOnlineWorkOrderVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("prOnlineOrderService")
@Slf4j
public class PrOnlineWorkOrderServiceImpl extends WorkOrderFacade<PrOnlineWorkOrder, PrHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(PrOnlineWorkOrder workOrder) {
        if (WorkOrderType.pr_online_service.getType() != workOrder.getOrderType()) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(PrOnlineWorkOrder wordOrder) {
        wordOrder.setOrderlevel(OrderLevel.edium.getType());
        orderOperationFacade.createWorkOrder(wordOrder);
        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(PrHandleOrderParam param) {
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());
        if (Objects.isNull(workOrderBase)) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_ERROR);
        }
        int handleResult = workOrderBase.getHandleResult();
        if (handleResult != HandleResultEnum.doing.getType()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }
        workOrderDao.reportHandle(param.getWorkOrderId(), param.getHandleResult());
        return OpResult.createSucResult(null);
    }


    @Override
    public OpResult getOrderList(WorkOrderListParam param) {
        if (param.getUserId() <= 0 || StringUtils.isEmpty(param.getHandleResult())) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        PageResult<PrOnlineWorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize() + 1;
        List<Integer> results = Arrays.stream(param.getHandleResult().split(","))
                .map(Integer::valueOf).collect(Collectors.toList());

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(param.getUserId()),
                param.getOrderType(), results, p, param.getWorkOrderId(), param.getPaging(), 0,
                param.getStartTime(), param.getEndTime(), 0);

        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            pageResult.setPageList(Collections.emptyList());
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }
        List<Long> workOrderIdList  = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        Map<Long, String> patientMap = getExt(workOrderIdList ,OrderExtName.patientId.getName());
        Map<Long, String> clueMap = getExt(workOrderIdList ,OrderExtName.clewId.getName());


        List<PrOnlineWorkOrderVO> voList = list.stream().map(r -> {
            PrOnlineWorkOrderVO workOrderVO = new PrOnlineWorkOrderVO();
            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setOperatorId(r.getOperatorId());
            String patientId  = patientMap.get(workOrderVO.getWorkOrderId());
            if (StringUtils.isNotBlank(patientId)) {
                workOrderVO.setPatientId(Long.parseLong(patientId));
            }
            String clewId  = clueMap.get(workOrderVO.getWorkOrderId());
            if (StringUtils.isNotBlank(clewId)) {
                workOrderVO.setClewId(Long.parseLong(clewId));
            }
            return workOrderVO;
        }).collect(Collectors.toList());
        pageResult.setPageList(voList);
        return OpResult.createSucResult(pageResult);
    }

}
