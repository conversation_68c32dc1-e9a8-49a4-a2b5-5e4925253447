package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.PrWorkOrderVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date
 *
 * 患者招募工单生成
 */
@Service("prWorkOrderServiceImpl")
@Slf4j
public class PrWorkOrderServiceImpl extends WorkOrderFacade<PrWorkOrder, PrHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;
    @Resource
    private WorkOrderDaoExt workOrderDaoExt;
    @Autowired
    private UserOperationRecordDao recordDao;
    @Autowired
    AssignateWorkOrderPublisher publisher;
    @Autowired
    private BaseWorkOrderService baseWorkOrderService;
    @Autowired
    private WorkTypePropertyService orderTypePropertyService;
    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;
    @Resource
    private OrderExtService orderExtService;

    @Override
    public OpResult vlidate(PrWorkOrder wordOrder) {
        if (WorkOrderType.PR_WORK_ORDER_LIST.contains(wordOrder.getOrderType())) {
            return OpResult.createSucResult();
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
    }

    @Override
    public OpResult<Long> create(PrWorkOrder wordOrder) {
        wordOrder.setOrderlevel(OrderLevel.edium.getType());
        //随访工单去重
        if (wordOrder.getOrderType() == WorkOrderType.pr_return_visit_service.getType()) {
            List<WorkOrderBase> existsWorkOrders = baseWorkOrderService.existExtValue(wordOrder.getCaseId(), wordOrder.getOrderType(),
                    DateUtils.addSeconds(new Date(), -30), OrderExtName.requestCode.getName(), wordOrder.getRequestCode());
            if (existsWorkOrders.size() > 0) {
                return OpResult.createSucResult(existsWorkOrders.get(0).getId());
            }
        }
        orderOperationFacade.createWorkOrder(wordOrder);
        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(PrHandleOrderParam param) {
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());
        if (Objects.isNull(workOrderBase)) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_ERROR);
        }
        int handleResult = workOrderBase.getHandleResult();
        if (handleResult != HandleResultEnum.doing.getType() && handleResult != HandleResultEnum.later_doing.getType() && handleResult != HandleResultEnum.wait_for_matching.getType()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }
        //线上招募工单做验证
        OpResult<Object> opResult = PrDelayCountCheck(param);
        if (opResult != null){
            return opResult;
        }
        int result = workOrderDao.reportHandle(param.getWorkOrderId(), param.getHandleResult());
        if (result > 0) {
            //保存扩展信息
            Map<String, Object> ext = (Map<String, Object>) param.parseExtParam(Map.class);
            if (ext != null) {
                ext.forEach((k,v) -> orderExtService.replaceExt(workOrderBase.getId(), k, v.toString()));
            }
            if (workOrderBase.getOrderType() == WorkOrderType.pr_supplement_material_service.getType()) {
                publisher.publishEvent(new AssignateWorkOrderEvent(this, WorkOrderType.pr_supplement_material.getType()));
            }
            else if (workOrderBase.getOrderType() == WorkOrderType.pr_first_screen_reject_service.getType()) {
                publisher.publishEvent(new AssignateWorkOrderEvent(this, WorkOrderType.pr_first_screen.getType()));
            }
        }
        return OpResult.createSucResult(null);
    }

    @Nullable
    private OpResult<Object> PrDelayCountCheck(PrHandleOrderParam param) {
        if (param.getHandleResult() != HandleResultEnum.later_doing.getType()){
            return null;
        }
        int count =  baseWorkOrderService.getCountByHandleResult(param.getUserId(),
                param.getOrderType(), param.getHandleResult()+"");
        if (WorkOrderType.PR_ONLINE_WORK_ORDER_LIST.contains(param.getOrderType())) {
            if (count >= orderTypePropertyService.getSecondMaxDelayCount(param.getOrderType(), WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT)){
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }
        if (WorkOrderType.pr_second_screen.getType() == param.getOrderType()) {
            if (count >= 30) {
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }
        return null;
    }


    @Override
    public OpResult getOrderList(WorkOrderListParam param) {
        if (param.getUserId() <= 0 || StringUtils.isEmpty(param.getHandleResult())) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        PageResult<PrWorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize() + 1;
        List<Integer> results = Arrays.stream(param.getHandleResult().split(","))
                .map(Integer::valueOf).collect(Collectors.toList());

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(param.getUserId()),
                param.getOrderType(), results, p, param.getWorkOrderId(), param.getPaging(), 0,
                param.getStartTime(), param.getEndTime(), 0);

        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            pageResult.setPageList(Collections.emptyList());
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }
        List<Long> workOrderIdList  = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        Map<Long, String> patientMap = getExt(workOrderIdList ,OrderExtName.patientId.getName());
        Map<Long, String> supplementMaterialMap = getExt(workOrderIdList ,OrderExtName.supplementMaterialId.getName());


        List<PrWorkOrderVO> voList = list.stream().map(r -> {
            PrWorkOrderVO workOrderVO = new PrWorkOrderVO();
            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setOperatorId(r.getOperatorId());
            String patientId  = patientMap.get(workOrderVO.getWorkOrderId());
            if (StringUtils.isNotBlank(patientId)) {
                workOrderVO.setPatientId(Long.parseLong(patientId));
            }
            String supplementMaterialId  = supplementMaterialMap.get(workOrderVO.getWorkOrderId());
            if (StringUtils.isNotBlank(supplementMaterialId)) {
                workOrderVO.setSupplementMaterialId(Long.parseLong(supplementMaterialId));
            }
            return workOrderVO;
        }).collect(Collectors.toList());
        pageResult.setPageList(voList);
        return OpResult.createSucResult(pageResult);
    }


    public List<WorkOrderVO> queryByIds(List<Long> workOrderIdList) {
        List<WorkOrderBase> workOrderBases = workOrderDao.listById(workOrderIdList);
        if (CollectionUtils.isEmpty(workOrderBases)){
            return Lists.newArrayList();
        }
        List<WorkOrderVO> workOrderVOS = Lists.newArrayList();
        for (WorkOrderBase base : workOrderBases) {
            WorkOrderVO workOrderVO = new WorkOrderVO();
            BeanUtils.copyProperties(base, workOrderVO);
            workOrderVO.setWorkOrderId(base.getId());
            workOrderVO.setHandleResult(base.getHandleResult());
            workOrderVOS.add(workOrderVO);
        }
        return workOrderVOS;
    }

    public OpResult handlePrOrderList(long userId, List<Long> workOrderIdList, int handleResult) {
        List<WorkOrderBase> workOrderBases = workOrderDao.listById(workOrderIdList);
        if (CollectionUtils.isEmpty(workOrderBases)){
            return OpResult.createSucResult(null);
        }
        workOrderBases = workOrderBases.stream()
                .filter(t -> t.getHandleResult() == HandleResultEnum.undoing.getType()
                        || t.getHandleResult() == HandleResultEnum.doing.getType()
                        ||  t.getHandleResult() == HandleResultEnum.later_doing.getType()
                        || t.getHandleResult() == HandleResultEnum.wait_for_matching.getType()).collect(Collectors.toList());
        workOrderIdList = workOrderBases.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIdList)){
               return OpResult.createSucResult(null);
        }
        log.info("workOrderIdList:{}", workOrderIdList);
        int result = workOrderDao.closeOrderById(workOrderIdList, handleResult);
        if (result > 0){
            List<WorkOrderRecord> records =  workOrderBases.stream().map(v ->{
                WorkOrderRecord record = new WorkOrderRecord();
                record.setWorkOrderType(Optional.ofNullable(v.getOrderType()).orElse(0));
                record.setOperatorId(0);
                record.setComment(HandleResultEnum.getFromType(handleResult).getMsg());
                record.setOperateMode(OperateMode.handle.getType());
                record.setOperateDesc("关闭工单");
                record.setWorkOrderId(v.getId());
                return record;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(records)){
                recordDao.saveRecordList(records);
            }
        }
        return OpResult.createSucResult(null);

    }

    public int updateWorkOrderOperatorId(List<Long> workOrderIds, Long operatorId, int operatorOrgId) {
        log.info("workOrderIdList:{} operatorId:{} operatorOrgId:{}", workOrderIds, operatorId, operatorOrgId);
        int result = workOrderDao.updateWorkOrderOperatorId(workOrderIds, operatorId, operatorOrgId);
        if (result > 0){
            List<WorkOrderBase> workOrderBases = workOrderDao.listById(workOrderIds);
            List<WorkOrderRecord> records =  workOrderBases.stream().map(v ->{
                WorkOrderRecord record = new WorkOrderRecord();
                record.setWorkOrderType(Optional.of(v.getOrderType()).orElse(0));
                record.setOperatorId(0);
                record.setComment(HandleResultEnum.getFromType(v.getHandleResult()).getMsg());
                record.setOperateMode(OperateMode.assignation.getType());
                record.setOperateDesc("更新工单处理人");
                record.setWorkOrderId(v.getId());
                return record;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(records)){
                recordDao.saveRecordList(records);
            }
        }
        return result;
    }
}
