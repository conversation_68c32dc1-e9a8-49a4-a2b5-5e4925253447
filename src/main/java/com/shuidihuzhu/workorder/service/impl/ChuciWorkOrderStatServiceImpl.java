package com.shuidihuzhu.workorder.service.impl;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.ChuciWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/5/21
 */
@Service("chuciWorkOrderStatService")
public class ChuciWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<ChuciWorkOrderStat> implements WorkOrderStatService<ChuciWorkOrderStat> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType(),
            HandleResultEnum.stop_case.getType(),
            HandleResultEnum.return_call.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.manual_lock.getType(),
            HandleResultEnum.audit_reject.getType() );


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }


    @Override
    public List<ChuciWorkOrderStat> getOnlyOneLevel(int oneLevel) {


        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();
        //停止筹款
        int stopNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.stop_case,today);
        //自动异常关闭
        int doneExceptionNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.exception_done,today);
        //提交结果
        int doneNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.done,today);
        //回访
        int callNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.return_call,today);
        //审核通过
        int passNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.audit_pass,today);
        //审核驳回
        int rejectNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.audit_reject,today);

        ChuciWorkOrderStat s = new ChuciWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setStopAmount(stopNum);
        s.setAutoExceptionAmount(doneExceptionNum);
        s.setSubmitAmount(doneNum);
        s.setCallBackAmount(callNum);
        s.setPassAmount(passNum);
        s.setRejectAmount(rejectNum);
        s.setAutoAmount(getAutoNum(s));

        return Lists.newArrayList(s);
    }


    @Override
    public List<ChuciWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer,Map<Integer,Integer>> result = baseStatService.getTypeStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{

                    ChuciWorkOrderStat c = new ChuciWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setStopAmount(MapUtil.getFromMap(result.get(HandleResultEnum.stop_case.getType()),r.getTwoLevel()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getTwoLevel()));
                    c.setSubmitAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getTwoLevel()));
                    c.setCallBackAmount(MapUtil.getFromMap(result.get(HandleResultEnum.return_call.getType()),r.getTwoLevel()));
                    c.setPassAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_pass.getType()),r.getTwoLevel()));
                    c.setRejectAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_reject.getType()),r.getTwoLevel()));
                    c.setAutoAmount(getAutoNum(c));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<ChuciWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer,Map<Long,Integer>> result = baseStatService.getUserStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{
                    ChuciWorkOrderStat c = new ChuciWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setStopAmount(MapUtil.getFromMap(result.get(HandleResultEnum.stop_case.getType()),r.getUserId()));
                    c.setAutoExceptionAmount(MapUtil.getFromMap(result.get(HandleResultEnum.exception_done.getType()),r.getUserId()));
                    c.setSubmitAmount(MapUtil.getFromMap(result.get(HandleResultEnum.done.getType()),r.getUserId()));
                    c.setCallBackAmount(MapUtil.getFromMap(result.get(HandleResultEnum.return_call.getType()),r.getUserId()));
                    c.setPassAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_pass.getType()),r.getUserId()));
                    c.setRejectAmount(MapUtil.getFromMap(result.get(HandleResultEnum.audit_reject.getType()),r.getUserId()));
                    c.setAutoAmount(getAutoNum(c));

                    return c;

                }).collect(Collectors.toList());
    }

    private int getAutoNum(ChuciWorkOrderStat s){
        //自动关闭
        int num = s.getPassAmount()+s.getRejectAmount()+s.getCallBackAmount()
                +s.getStopAmount()+s.getSubmitAmount();

        return num;

    }
}
