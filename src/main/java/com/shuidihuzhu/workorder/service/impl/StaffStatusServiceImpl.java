package com.shuidihuzhu.workorder.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.model.view.StaffSelectVO;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.StaffFreeStatService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderReportStatisticsDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.StaffStatusNum;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.core.model.view.StaffStatusStatVO;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.service.WorkTypePropertyService;
import com.shuidihuzhu.workorder.service.custom.recruit.PrStaffStatusService;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;
import static java.util.Comparator.comparingLong;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */
@Service
@Slf4j
@RefreshScope
public class StaffStatusServiceImpl implements StaffStatusService {

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private BaseWorkOrderService workOrderService;

    @Autowired
    AssignateWorkOrderPublisher publisher;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private PermissionFeignClient permissionFeignClient;

    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private WorkTypePropertyService workPropertyService;

    @Autowired
    private WorkOrderReportStatisticsDao workOrderReportStatisticsDao;
    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private StaffFreeStatService staffFreeStatService;

    @Autowired
    private OrderGroupService orderGroupService;

    @Resource
    private PrStaffStatusService prStaffStatusService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Value("${work.order.type.both.on.line.on:0}")
    private int orderTypeBothOnLineOn;

    @Value("${apollo.offline.order-type-list:}")
    private List<Integer> offlineOrderTypes;

//    INACTIVE(10, "新注册待激活用户"),
//    NORMAL(20, "正常"),
//    LOCKED_NOT_UPDATE_PWD(30, "密码长时间未修改锁定"),
//    LOCKED_ADMIN(31, "管理员锁定"),
//    CANCEL(40, "注销"),
// 离职的人员 取40
private static int OFF_JOB_STATUS = 40;

    //捐转工单
    private Set<Integer> juanzhuan_order = Sets.newHashSet();

    @PostConstruct
    public void init() {
        juanzhuan_order = workOrderTypeService.getByOneLevel(OneTypeEnum.juanzhuan.getType()).stream().collect(Collectors.toSet());
    }

    @Override
    public int changeStatus(StaffStatus staffStatus) {

        long userId = staffStatus.getUserId();
        int orderType = staffStatus.getOrderType();
        StaffStatus ss = staffStatusDao.getStaffStatus(userId, orderType);

        if (ss != null) {
            staffStatus.setReceiptThreshold(ss.getReceiptThreshold());
        }

        if (staffStatus.getOrderType() == WorkOrderType.casefirstreport.getType() && Objects.isNull(ss)) {
            staffStatus.setReceiptThreshold(40);
            staffStatus.setAutoAllocation(1);
        }
        if (WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType) && Objects.isNull(ss)) {
            staffStatus.setAutoAllocation(1);
        }

        if (staffStatus.getOrderType() == WorkOrderType.casefirstreport.getType() && Objects.nonNull(ss)) {
            staffStatus.setReceiptThreshold(ss.getReceiptThreshold());
            staffStatus.setAutoAllocation(ss.getAutoAllocation());
        }

        if ((staffStatus.getOrderType() == WorkOrderType.casehistoryreport.getType()
                || staffStatus.getOrderType() == WorkOrderType.up_grade_second.getType()
                || WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType))
                && Objects.nonNull(ss)) {
            staffStatus.setAutoAllocation(ss.getAutoAllocation());
        }

        //默认100
        if (juanzhuan_order.contains(staffStatus.getOrderType())) {
            staffStatus.setReceiptThreshold(100);
            if (ss != null) {
                staffStatus.setReceiptThreshold(ss.getReceiptThreshold());
            }
        }

        if (orderType == WorkOrderType.cailiao_fuwu.getType()) {
            if (ss != null) {
                staffStatus.setReceiptThreshold(ss.getReceiptThreshold());
            }
            {
                StaffStatus fuwuStaff = staffStatusDao.getThresholdByType(WorkOrderType.cailiao_fuwu.getType(), 0);
                staffStatus.setReceiptThreshold(Optional.ofNullable(fuwuStaff).map(StaffStatus::getReceiptThreshold).orElse(0));
            }
        }

        int count = staffStatusDao.changeStatus(staffStatus);

        if (count > 0) {
            //存储操作记录
            staffStatusDao.saveStaffStatusRecord(staffStatus);

            //统计离线次数
            this.offlineCount(staffStatus);

            if (staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType()
                    && !WorkOrderType.REPORT_TYPES.contains(staffStatus.getOrderType())
                    && (!WorkOrderType.QC_WORK_ORDER_LIST.contains(staffStatus.getOrderType())
                    || WorkOrderType.qc_high_risk_quality_inspection.getType() == staffStatus.getOrderType())
                    && WorkOrderType.cailiao_fuwu.getType() != staffStatus.getOrderType()) {
                //如果是离线清空名下工单
                workOrderService.freeWorkOrder(staffStatus.getUserId(), staffStatus.getOrderType());
            }

            //上下线需要触发工单的分配
            if (staffStatus.getStaffStatus() == StaffStatusEnum.online.getType() || staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType()) {
                Set<Integer> groupSet = orderGroupService.getGroupSet();
                // 组分配的工单不需要上线分单 组类型触发分单就够了
                if (!groupSet.contains(orderType) && orderType != WorkOrderType.nei_shen_group.getType()) {
                    EventPublishHelper.sendOrderAssign(this, orderType);
                }
            }

            // 发送员工状态变化通知
            EventPublishHelper.sendStaffStatusChange(this, staffStatus.getUserId(),
                    staffStatus.getOrderType(), staffStatus.getStaffStatus());
        }

        return count;
    }

    private void offlineCount(StaffStatus staffStatus) {
        try {
            if (WorkOrderType.REPORT_TYPES.contains(staffStatus.getOrderType())
                    && staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType()) {
                StaffStatus ss = staffStatusDao.getStaffStatus(staffStatus.getUserId(), staffStatus.getOrderType());

                int statDay = Integer.parseInt(DateUtil.getDate2YMDStr(ss.getUpdateTime()));
                int statHour = ss.getUpdateTime().getHours();
                workOrderReportStatisticsDao.updateOfflineCount(statDay, statHour, staffStatus.getOrderType(), staffStatus.getUserId());
            }
        } catch (NumberFormatException e) {
            log.error("offlineCount error", e);
        }
    }

    @Override
    public StaffStatus getStaffStatus(long userId, int orderType) {

        return staffStatusDao.getStaffStatus(userId, orderType);
    }

    @Override
    public List<StaffStatus> getStaffStatusByTypes(long userId, List<Integer> types) {

        List<String> p = types.stream().map(r -> {
            WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(r);
            return workOrderTypeDO.getPermission();
        }).collect(Collectors.toList());

        PermissionParam permissionParam = new PermissionParam();
        permissionParam.setPermissions(p);
        permissionParam.setUserId(userId);
        permissionParam.setAppCode(AuthSaasContext.getAuthAppCode());
        Set<String> permissions = permissionFeignClient.validUserPermissions(permissionParam).getData();
        log.info("getStaffStatusByTypes userId={} types={} permissions={}", userId, types, permissions);

        if (CollectionUtils.isEmpty(permissions)) {
            return Lists.newArrayList();
        }

        types = permissions.stream()
                .filter(r -> WorkOrderType.getFromPermission(r) != null)
                .map(r -> {
                    WorkOrderType workOrderType = WorkOrderType.getFromPermission(r);
                    return workOrderType.getType();
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(types)) {
            return Lists.newArrayList();
        }
        List<StaffStatus> list = staffStatusDao.getStaffStatusByTypes(userId, types);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        return list;
    }

    @Override
    public List<StaffStatus> getStaffs(List<Long> userId, int orderType) {
        return staffStatusDao.getStaffs(userId, Lists.newArrayList(orderType));
    }

    @Override
    public List<StaffStatus> getStaffOnlineToday(List<Long> userIds, int orderType) {

        int online = StaffStatusEnum.online.getType();

        return staffStatusDao.getStaffsByStatus(userIds, orderType, online);
    }

    @Override
    public List<StaffStatus> getStaffOnlineAndStopToday(List<Long> userIds, int orderType) {

        ArrayList<Integer> statusList = Lists.newArrayList(StaffStatusEnum.online.getType(), StaffStatusEnum.stop.getType());
        return staffStatusDao.getStaffsByStatusList(userIds, orderType, statusList);
    }

    @Override
    public List<StaffStatus> getStaffOnlineAndStopAndOfflineToday(List<Long> userIds, int orderType) {
        ArrayList<Integer> statusList = Lists.newArrayList(StaffStatusEnum.online.getType(), StaffStatusEnum.stop.getType(), StaffStatusEnum.offline.getType());
        return staffStatusDao.getStaffsByStatusList(userIds, orderType, statusList);
    }


    @Override
    public int addStaffAssignTime(long userId, int orderType) {
        return staffStatusDao.addStaffAssignTime(userId, orderType);
    }

    @Override
    public long getStaffOrderByAssignTime(List<Long> users, int orderType) {
        return staffStatusDao.getStaffOrderByAssignTime(users, orderType);
    }


    @Override
    public List<StaffStat> getStaffStatusStat(long userId) {

        log.info("userClassify userId={}", userId);

        PermissionParam permissionParam = new PermissionParam();
        permissionParam.setPermissions(workOrderTypeService.getAllPermissions());
        permissionParam.setUserId(userId);
        permissionParam.setAppCode(AuthSaasContext.getAuthAppCode());
        Set<String> permissions = permissionFeignClient.validUserPermissions(permissionParam).getData();

        log.info("userClassify permissions={}", permissions);
        return getStaffStats(permissions);
    }

    /**
     * 获取全部人员
     *
     * @param orderType
     * @return
     */
    private StaffStatusStat getAll(List<Integer> orderType, int undoingNum) {

        int online = 0;
        int offline = 0;
        int stop = 0;
        Set<Long> onlineSet = null;
        Set<Long> stopSet = null;
        List<StaffStatus> list = staffStatusDao.getAllOnJobByTypes(orderType);
        //根据在线状态分组
        Map<Integer, List<StaffStatus>> map = list.stream().collect(Collectors.groupingBy(StaffStatus::getStaffStatus));

        if (CollectionUtils.isNotEmpty(map.get(StaffStatusEnum.online.getType()))) {
            List<StaffStatus> onlineList = map.get(StaffStatusEnum.online.getType());
            onlineSet = onlineList.stream().map(StaffStatus::getUserId).collect(Collectors.toSet());
            online = onlineSet.size();
        }

        if (CollectionUtils.isNotEmpty(map.get(StaffStatusEnum.stop.getType()))) {
            List<StaffStatus> stopList = map.get(StaffStatusEnum.stop.getType());
            stopSet = stopList.stream().map(StaffStatus::getUserId).collect(Collectors.toSet());
            // 暂停的要去除在线的
            if (online > 0) {
                stopSet.removeAll(onlineSet);
            }
            stop = stopSet.size();
        }

        if (CollectionUtils.isNotEmpty(map.get(StaffStatusEnum.offline.getType()))) {
            Set<Long> offlineSet = map.get(StaffStatusEnum.offline.getType()).stream().map(StaffStatus::getUserId).collect(Collectors.toSet());

            if (online > 0) {
                offlineSet.removeAll(onlineSet);
            }

            if (stop > 0) {
                offlineSet.removeAll(stopSet);
            }

            offline = offlineSet.size();
        }


        StaffStatusStat sss = new StaffStatusStat(0, online, offline, stop, undoingNum);

        return sss;
    }


    private int getCount(Map<Integer, StaffStatusNum> statusMap, StaffStatusEnum staffStatusEnum) {

        StaffStatusNum ss = statusMap.get(staffStatusEnum.getType());

        if (ss == null) {
            return 0;
        }

        return ss.getNum();
    }

    @Override
    public OpResult<PageResult<StaffStatus>> getStaffStatusByTypes(List<Integer> types, int pageSize, String paging, long userId, boolean isAll, long operatorId) {

        PageResult<StaffStatus> pageResult = new PageResult<>();

        int p = pageSize + 1;

        //如果是查询全部 单独处理 不分页
        if (isAll) {
            return getAllByTypes(types, operatorId);
        }

        List<StaffStatus> list = staffStatusDao.getOnJobStaffStatusList(types, p, paging, userId, operatorId);

        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(StaffStatus::getUserId)).collect(Collectors.toList());
        }

        List<Long> userIds = list.stream().map(r -> Long.valueOf(r.getUserId())).collect(Collectors.toList());

        List<AuthUserDto> models = userFeignClient.getAuthUserByIds(userIds).getData();

        if (CollectionUtils.isNotEmpty(models)) {

            Map<Long, AuthUserDto> map = models.stream().collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity()));
            list = list.stream().map(r -> {

                AuthUserDto m = map.get(r.getUserId());
                if (m != null) {
                    r.setName(m.getUserName());
                    r.setAccount(m.getLoginName());
                }

                return r;
            }).collect(Collectors.toList());

        }

        sortListStaff(list);

        pageResult.setPageList(list);


        return OpResult.createSucResult(pageResult);
    }


    @Override
    public Set<StaffStatus> getStaffStatByPermissions() {

        Set<StaffStatus> set = Sets.newHashSet();

        Arrays.stream(WorkOrderType.values()).forEach(r -> {

            Response<List<AuthUserDto>> rpcResponse = permissionFeignClient.getUsersByPermission(r.getPermission());
            log.info("getStaffStatByPermissions Permission={} rpcResponse={}", r.getPermission(), rpcResponse);

            if (rpcResponse.ok()) {
                List<AuthUserDto> list = rpcResponse.getData();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.stream().filter(Objects::nonNull)
                            .forEach(rr -> {
                                if (rr.getStatus() != null && rr.getStatus() != OFF_JOB_STATUS) {
                                    StaffStatus ss = new StaffStatus();
                                    ss.setUserId(rr.getUserId());
                                    ss.setName(rr.getUserName());
                                    set.add(ss);
                                }
                            });
                }
            }
        });
        return set;
    }

    @Override
    public void autoOff() {

        String time = LocalDateTime.now().plusHours(-2).toString();

        List<StaffStatus> list = staffStatusDao.getStaffsByStatusAndTime(Lists.newArrayList(StaffStatusEnum.online.getType(), StaffStatusEnum.stop.getType())
                , time);

        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        list.forEach(r -> {
            Integer orderType = Integer.valueOf(r.getOrderType());
            if (WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType)) {
                return;
            }
            if (orderType == WorkOrderType.nei_shen_group.getType()) {
                return;
            }
            Set<Integer> groupSet = orderGroupService.getGroupSet();
            if (groupSet.contains(orderType)) {
                return;
            }
            if (!offlineOrderTypes.contains(orderType)) {
                //离线
                r.setStaffStatus(StaffStatusEnum.offline.getType());
                //系统离线
                r.setOperType(9);
                this.changeStatus(r);
            }
        });
    }

    @Override
    public void reportAutoOff() {
        List<StaffStatus> list = staffStatusDao.queryStaffsByStatusAndType(Lists.newArrayList(StaffStatusEnum.online.getType()), WorkOrderType.REPORT_TYPES);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.stream().forEach(r -> {
            //离线
            r.setStaffStatus(StaffStatusEnum.offline.getType());
            //系统离线
            r.setOperType(9);
            this.changeStatus(r);
        });
    }

    @Override
    public Boolean isOffline(long userId, int orderType) {
        StaffStatus staffStatus = getStaffStatus(userId, orderType);
        if (staffStatus == null) {
            return null;
        }
        int staffStatusValue = staffStatus.getStaffStatus();
        StaffStatusEnum staffStatusEnum = StaffStatusEnum.getStaffStatusEnumFromType(staffStatusValue);
        return staffStatusEnum == StaffStatusEnum.offline;
    }

    /**
     * 查询总量
     *
     * @param types
     * @param operatorId
     * @return
     */
    private OpResult<PageResult<StaffStatus>> getAllByTypes(List<Integer> types, long operatorId) {

        PageResult<StaffStatus> pageResult = new PageResult<>();
        pageResult.setHasNext(false);

        List<StaffStatus> list = staffStatusDao.getOnJobStaffStatusList(types, 4000, WorkOrderConfig.next_paging, 0, operatorId);

        //按照用户分组
        Map<Long, List<StaffStatus>> map = list.stream().collect(Collectors.groupingBy(StaffStatus::getUserId));

        List<StaffStatus> l = Lists.newArrayList();

        List<Long> userIds = list.stream().map(r -> r.getUserId()).distinct().collect(Collectors.toList());

        List<AuthUserDto> models = userFeignClient.getAuthUserByIds(userIds).getData();


        if (CollectionUtils.isNotEmpty(models)) {

            Map<Long, AuthUserDto> userMap = models.stream().collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity(), (o1, o2) -> o2));

            for (long u : map.keySet()) {

                List<StaffStatus> statuses = map.get(u);
                //按照在线状态分组
                Map<Integer, List<StaffStatus>> statusMap = statuses.stream().collect(Collectors.groupingBy(StaffStatus::getStaffStatus));

                StaffStatus ss = new StaffStatus();

                ss.setAssignTime(getMaxAssignTime(statuses));
                ss.setUserId(u);
                if (userMap.get(u) != null) {
                    ss.setName(userMap.get(u).getUserName());
                }
                ss.setStaffStatus(StaffStatusEnum.offline.getType());
                ss.setAllowAssignOrdertype(statuses.get(0).getAllowAssignOrdertype());

                List<StaffStatus> ol = statusMap.get(StaffStatusEnum.online.getType());
                //如果有在线的就是在线
                if (CollectionUtils.isNotEmpty(ol)) {
                    StringBuilder sb = new StringBuilder();
                    ol.stream().forEach(r -> {
                        sb.append(r.getOrderType()).append(",");
                    });
                    if (StringUtils.isNotEmpty(sb)) {
                        ss.setStaffStatus(StaffStatusEnum.online.getType());
                        String onlineType = sb.toString();
                        ss.setAllOnlineType(onlineType.substring(0, onlineType.length() - 1));
                        l.add(ss);
                        continue;
                    }
                }

                List<StaffStatus> sp = statusMap.get(StaffStatusEnum.stop.getType());
                //如果是暂停
                if (CollectionUtils.isNotEmpty(sp)) {
                    ss.setStaffStatus(StaffStatusEnum.stop.getType());
                    l.add(ss);
                    continue;
                }

                l.add(ss);
                continue;
            }

        }

        sortListStaff(l);

        pageResult.setPageList(l);

        return OpResult.createSucResult(pageResult);
    }

    private Date getMaxAssignTime(List<StaffStatus> statusList) {
        if (CollectionUtils.isEmpty(statusList)) {
            return null;
        }

        Date maxAssignTime = null;
        for (StaffStatus staff : statusList) {
            if (staff != null && staff.getAssignTime() != null &&
                    (maxAssignTime == null || staff.getAssignTime().after(maxAssignTime))) {
                maxAssignTime = staff.getAssignTime();
            }
        }

        return maxAssignTime;
    }

    @Override
    public int countOfflineTimesToday(@NonNull StaffStatus staffStatus) {
        String beginDate = new DateTime().dayOfYear().roundFloorCopy().toString("yyyy-MM-dd HH:mm:ss");
        //0:本人操作 1:组长操作 9:系统操作
        return staffStatusDao.countOfflineTodayRecord(staffStatus.getUserId(), staffStatus.getOrderType(),
                StaffStatusEnum.offline.getType(), 0, beginDate);
    }

    @Override
    public List<StaffStatus> getStaffStatusByOrderType(String orderType) {
        List<Integer> types = Arrays.stream(orderType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<StaffStatus> staffStatusList = staffStatusDao.getAllOnJobByTypes(types);
        //根据userId去重
        ArrayList<StaffStatus> staffStatusArrayList = staffStatusList.stream().collect(collectingAndThen(
                toCollection(() -> new TreeSet<>(comparingLong(StaffStatus::getUserId))), ArrayList::new));
        //获取用户信息
        List<Long> userIds = staffStatusArrayList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        List<AuthUserDto> models = userFeignClient.getAuthUserByIds(userIds).getData();
        Map<Long, AuthUserDto> adminUserAccountModelMap = models.stream().collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity()));
        for (StaffStatus staffStatus : staffStatusArrayList) {
            AuthUserDto adminUserAccountModel = adminUserAccountModelMap.get(staffStatus.getUserId());
            if (Objects.nonNull(adminUserAccountModel)) {
                staffStatus.setName(adminUserAccountModel.getUserName());
            }
        }

        sortListStaff(staffStatusList);

        return staffStatusArrayList;
    }

    @Override
    public void clearOffJobUsers() {

        log.info("清除离职的员工");
        List<Integer> allOnJobUsers = staffStatusDao.selectOnJobUsers();

        List<Integer> offJobUserIds = Lists.newArrayList();

        Lists.partition(allOnJobUsers, 50).stream().forEach(
                userIds -> {
                    List<Long> userIdLongList = userIds.stream().map(Long::valueOf).collect(Collectors.toList());
                    Response<List<AuthUserDto>> authRpcResponse = userFeignClient.getAuthUserByIds(userIdLongList);
                    log.info("查询用户的在职状态 userId:{} result:{}", userIds, authRpcResponse);

                    if (authRpcResponse == null || CollectionUtils.isEmpty(authRpcResponse.getData())) {
                        return;
                    }

                    Set<Integer> userIdResult = new HashSet<>();
                    for (AuthUserDto model : authRpcResponse.getData()) {

                        if (model.getStatus() == OFF_JOB_STATUS) {
                            offJobUserIds.add(model.getUserId().intValue());
                        }
                        userIdResult.add(model.getUserId().intValue());
                    }

                    for (Integer user : userIds) {
                        if (!userIdResult.contains(user)) {
                            offJobUserIds.add(user);
                        }
                    }
                }
        );


        if (CollectionUtils.isNotEmpty(offJobUserIds)) {
            log.info("工单系统中用户已离职. offJobUserIds:{}", offJobUserIds);
            alarmClient.sendByUser(Lists.newArrayList("gaoruiyi"),
                    "自动清除离职的人员：" + JSON.toJSONString(offJobUserIds));

            staffStatusDao.updateLeaveOfficeStatus(offJobUserIds, 1);

            for (Integer userId : offJobUserIds) {
                List<Integer> allowOrderTypes = staffStatusDao.selectOrderTypeByUser(userId);

                for (int orderType : allowOrderTypes) {
                    log.info("用户离职，释放用户名下的工单.userId:{} orderType:{}", userId, orderType);
                    workOrderService.freeWorkOrder(userId, orderType);
                }
            }
        }

        //招募离职处理
        prStaffStatusService.workOrderReleaseOnDimission(allOnJobUsers.stream().map(Integer::longValue).collect(Collectors.toList()));
    }

    @Override
    public void clearAutoOfflineStatus() {
        log.info("自动清除允许自动在线状态");
        staffStatusDao.clearAutoOfflineStatus();
    }

    @Override
    public void changeAutoOnLineStatus(StaffStatus staffStatus) {

        long userId = staffStatus.getUserId();
        int orderType = staffStatus.getOrderType();
        StaffStatus dbStaff = staffStatusDao.getStaffStatus(userId, orderType);
        if (dbStaff == null) {
            log.info("不能找到用户的数据. userId:{}", userId);
            return;
        }

        staffStatusDao.updateAutoOnlineStatus(Long.valueOf(staffStatus.getUserId()).intValue(), staffStatus.getOrderType(), staffStatus.getAllowAutoOnline());

        dbStaff.setOperationId(staffStatus.getOperationId());
        dbStaff.setAllowAutoOnline(staffStatus.getAllowAutoOnline());

        //存储操作记录
        staffStatusDao.saveStaffStatusRecord(dbStaff);
    }

    private void sortListStaff(List<StaffStatus> allStaffList) {
        if (CollectionUtils.isEmpty(allStaffList)) {
            return;
        }

        Collections.sort(allStaffList, StaffStatus.LIST_COMPARATOR);
    }

    @Override
    public boolean canSelfOnline(StaffStatus staffStatus) {
        if (staffStatus == null || staffStatus.getOperationId() != staffStatus.getUserId()
                || staffStatus.getStaffStatus() != StaffStatusEnum.online.getType()) {
            return true;
        }

        StaffStatus dbStaff = staffStatusDao.getStaffStatus(staffStatus.getUserId(), staffStatus.getOrderType());

        return dbStaff == null || dbStaff.getAllowAutoOnline() == 0;
    }

    @Override
    public boolean canBothOnline(StaffStatus staffStatus) {
        if (orderTypeBothOnLineOn == 0 || staffStatus == null || staffStatus.getStaffStatus() != StaffStatusEnum.online.getType()) {
            return true;
        }

        Integer oneLevelOrder = workOrderTypeService.getOneFromTwo(staffStatus.getOrderType());
        if (oneLevelOrder == null) {
            log.error("通过二级工单不能找到一级工单. twoOrderType:{}", staffStatus.getOrderType());
            return true;
        }

        // 如果是公共一级类型不检查
        if (oneLevelOrder == OneTypeEnum.common.getType()) {
            return true;
        }

        Set<Integer> groupSet = orderGroupService.getGroupSet();

        List<WorkTypeProperty> canBothOnLineTypes = workPropertyService.selectByPropertyTypes(WorkTypeProperty.FIRST_WORK_TYPE, Lists.newArrayList(oneLevelOrder),
                Lists.newArrayList(WorkTypeProperty.PropertyType.BATH_ON_LINE_ORDER_TYPE.getCode()));
        Set<Integer> allowOnLineTypes = canBothOnLineTypes.stream().map(WorkTypeProperty::getPropertyValue)
                .map(Integer::valueOf).collect(Collectors.toSet());

        List<Integer> allTwoOrderTypes = workOrderTypeService.getByOneLevel(oneLevelOrder);

        // 组分配的工单类型排除同时在线逻辑
        allTwoOrderTypes = allTwoOrderTypes.stream().filter(v -> !groupSet.contains(v)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allTwoOrderTypes)) {
            log.error("一级工单下没有挂二级工单", oneLevelOrder);
            return true;
        }

        return dealWithOnLine(allowOnLineTypes,
                getCurOnLineOrderTypes(staffStatus.getUserId(), allTwoOrderTypes),
                staffStatus);
    }

    @Override
    public void publishEvent(int orderType) {
        //发送分配工单event
        publisher.publishEvent(new AssignateWorkOrderEvent(this, orderType));
    }

    @Override
    public void allowAssign(long userId, String allowAssign) {
        staffStatusDao.allowAssign(userId, allowAssign);
    }

    @Override
    public int updateThresholdByType(int orderType, int threshold) {
        int a = staffStatusDao.updateThresholdByType(orderType, threshold);
        //设置阈值需要触发工单的分配
        publisher.publishEvent(new AssignateWorkOrderEvent(this, orderType));

        return a;
    }

    @Override
    public int freeByUserId(List<Long> userIds, int orderType, Date startTime, Date endTime) {

        userIds.stream().forEach(userId -> workOrderService.freeWorkOrderWithTime(userId, orderType, startTime, endTime));

        return 0;
    }

    @Override
    public StaffStatus getThresholdByType(int orderType, Long userId) {
        return staffStatusDao.getThresholdByType(orderType, userId);
    }

    @Override
    public List<StaffStat> getStaffStatusStatByPermissions(long userId, List<String> prPermissions) {
        log.info("userClassify userId={}", userId);
        PermissionParam permissionParam = new PermissionParam();
        permissionParam.setPermissions(prPermissions);
        permissionParam.setUserId(userId);
        permissionParam.setAppCode(AuthSaasContext.getAuthAppCode());
        Set<String> permissions = permissionFeignClient.validUserPermissions(permissionParam).getData();
        return getStaffStats(permissions);
    }

    @NotNull
    private List<StaffStat> getStaffStats(Set<String> permissions) {
        log.info("userClassify Permissions={}", permissions);
        List<StaffStat> result = Lists.newArrayList();

        if (CollectionUtils.isEmpty(permissions)) {
            return result;
        }

        Map<Integer, List<Integer>> classifyMap = workOrderTypeService.getClassiyByPermissions(permissions);

        if (MapUtils.isEmpty(classifyMap)) {
            return result;
        }

        for (Integer one : classifyMap.keySet()) {

            List<Integer> orderType = classifyMap.get(one);
            List<StaffStatusNum> staffStatusStats = staffStatusDao.getOnJobStaffStatusStat(orderType, Lists.newArrayList(StaffStatusEnum.offline.getType(), StaffStatusEnum.stop.getType(), StaffStatusEnum.online.getType()));
            //二级分类
            Map<Integer, List<StaffStatusNum>> map = staffStatusStats.stream().collect(Collectors.groupingBy(StaffStatusNum::getOrderType));

            StaffStat ss = new StaffStat();
            ss.setType(one);

            LinkedList<StaffStatusStat> status = Lists.newLinkedList();

            int undoingNum = 0;

            for (Integer key : map.keySet()) {

                List<StaffStatusNum> l = map.get(key);

                Map<Integer, StaffStatusNum> statusMap = l.stream().collect(Collectors.toMap(StaffStatusNum::getStaffStatus, Function.identity()));
                int ol = getCount(statusMap, StaffStatusEnum.online);
                int ofl = getCount(statusMap, StaffStatusEnum.offline);
                int sp = getCount(statusMap, StaffStatusEnum.stop);
                int n = workOrderDao.getAllCountByHandleResult(key, HandleResultEnum.undoing.getType());

                StaffStatusStat sss = new StaffStatusStat(key, ol, ofl, sp, n);
                undoingNum += n;
                status.add(sss);
            }

            StaffStatusStat all = getAll(orderType, undoingNum);
            status.addFirst(all);
            if (one == OneTypeEnum.report.getType()
                    || one == OneTypeEnum.qc.getType()
                    || one == OneTypeEnum.qc_wx1v1.getType()) {
                status = status.stream().sorted(comparing(StaffStatusStat::getOrderType)).collect(Collectors.toCollection(Lists::newLinkedList));
            }
            ss.setStatusStats(status);

            result.add(ss);
        }

        return result;
    }

    private boolean dealWithOnLine(Set<Integer> allowOnLineTypes,
                                   List<Integer> curOnLineTypes,
                                   StaffStatus staffStatus) {

        if (CollectionUtils.isEmpty(curOnLineTypes)) {
            log.info("当前用户没有在线的工单类型，这次上线操作可以被允许.staffStatus:{}", staffStatus);
            return true;
        }

        if (CollectionUtils.isEmpty(allowOnLineTypes)) {
            log.info("没有被允许的同时在线的工单类型.staffStatus:{}", staffStatus);
            return false;
        }

        curOnLineTypes.add(staffStatus.getOrderType());

        log.info("判断用户是否可以同时在线 allowOnLineTypes:{} curOnLineTypes:{} staffStatus:{}", allowOnLineTypes,
                curOnLineTypes, staffStatus);

        return allowOnLineTypes.containsAll(curOnLineTypes);
    }

    private List<Integer> getCurOnLineOrderTypes(long userId, List<Integer> allOrderTypes) {

        List<Integer> curOnLineTypes = Lists.newArrayList();

        List<StaffStatus> allStaffList = staffStatusDao.getStaffStatusByTypes(userId, allOrderTypes);
        if (CollectionUtils.isEmpty(allStaffList)) {
            return curOnLineTypes;
        }

        for (StaffStatus staff : allStaffList) {
            if (staff.getStaffStatus() == StaffStatusEnum.online.getType()) {
                curOnLineTypes.add(staff.getOrderType());
            }
        }

        return curOnLineTypes;
    }

    @Override
    public long calcTodayPauseDuration(StaffStatus staffStatus, long userId, int orderType) {
        Date now = new Date();
        Date zeroPoint = DateUtils.truncate(now, Calendar.DATE);
        List<StaffStatusRecord> records = staffStatusDao.getStaffStatusRecords(userId, orderType, zeroPoint);
        StaffStatusRecord lastOneLtToday = staffStatusDao.getLastOneLtCreateTime(userId, orderType, zeroPoint);
        long pauseDuration = 0;
        if (CollectionUtils.isEmpty(records)) {
            int status = staffStatus.getStaffStatus();
            Date updateTime = staffStatus.getUpdateTime();

            if (status == StaffStatusEnum.stop.getType()) {
                pauseDuration = updateTime.before(zeroPoint) ? now.getTime() - zeroPoint.getTime() : now.getTime() - updateTime.getTime();
            }
        } else {
            Date stopStartTime = zeroPoint;
            int i = 0;
            int size = records.size();
            boolean findStop = false;
            for (StaffStatusRecord record : records) {
                i++;

                if (i == 1 && record.getStaffStatus() != StaffStatusEnum.stop.getType() && lastOneLtToday != null && lastOneLtToday.getStaffStatus() == StaffStatusEnum.stop.getType()) {
                    pauseDuration += (record.getCreateTime().getTime() - stopStartTime.getTime());
                    continue;
                }

                if (record.getStaffStatus() == StaffStatusEnum.stop.getType()) {
                    stopStartTime = record.getCreateTime();
                    findStop = true;
                }

                if (record.getStaffStatus() != StaffStatusEnum.stop.getType() && findStop) {
                    pauseDuration += (record.getCreateTime().getTime() - stopStartTime.getTime());
                    findStop = false;
                }

                if (i == size && findStop) {
                    pauseDuration += (now.getTime() - stopStartTime.getTime());
                }
            }
        }

        return pauseDuration / 1000;
    }

    private long calcTodayTargetStatusDuration(StaffStatus staffStatus, long userId, int orderType, List<StaffStatusRecord> records, StaffStatusEnum targetStatusEnum) {
        if (targetStatusEnum == StaffStatusEnum.online) {
            // 查询上班时长要仅保留上下班的记录
            records = records.stream()
                    .filter(v -> v.getStaffStatus() == StaffStatusEnum.online.getType() || v.getStaffStatus() == StaffStatusEnum.offline.getType())
                    .collect(Collectors.toList());
        }
        Date now = new Date();
        Date zeroPoint = DateUtils.truncate(now, Calendar.DATE);
        StaffStatusRecord lastOneLtToday = staffStatusDao.getLastOneLtCreateTime(userId, orderType, zeroPoint);
        long pauseDuration = 0;

        // 今天没有任何状态操作
        int targetStatus = targetStatusEnum.getType();

        Date targetStartTime = zeroPoint;
        boolean findTarget = false;
        // 若从昨天开始就是目标状态，计算一次0点到第一条记录的持续时间
        if (lastOneLtToday != null && lastOneLtToday.getStaffStatus() == targetStatus){
            findTarget = true;
        }
        // 若从前天或更早开始到现在就没操作过则检查当前状态
        int status = staffStatus.getStaffStatus();
        if (CollectionUtils.isEmpty(records) && status == targetStatus) {
            findTarget = true;
        }
        for (StaffStatusRecord record : records) {

            if (record.getStaffStatus() == targetStatus && !findTarget) {
                targetStartTime = record.getCreateTime();
                findTarget = true;
            }

            if (record.getStaffStatus() != targetStatus && findTarget) {
                pauseDuration += (record.getCreateTime().getTime() - targetStartTime.getTime());
                findTarget = false;
            }

        }
        // 若最后一次保持目标状态，计算最后一次时间到当前时间持续时间
        if (findTarget) {
            pauseDuration += (now.getTime() - targetStartTime.getTime());
        }

        return pauseDuration / 1000;
    }

    @Override
    public StaffStatusStatVO getStatusStatV2(StaffStatus user, long userId, int orderType) {
        StaffStatusStatVO vo = new StaffStatusStatVO();

        Date now = new Date();
        Date zeroPoint = DateUtils.truncate(now, Calendar.DATE);
        List<StaffStatusRecord> records = staffStatusDao.getStaffStatusRecords(userId, orderType, zeroPoint);
        StaffStatusRecord firstOnlineRecord = null;
        StaffStatusRecord lastOfflineRecord = null;

        for (StaffStatusRecord record : records) {
            if (firstOnlineRecord == null) {
                // 查找第一次上班
                if (record.getStaffStatus() == StaffStatusEnum.online.getType()) {
                    firstOnlineRecord = record;
                }
            }

            // 查找最后一次下班
            if (record.getStaffStatus() == StaffStatusEnum.offline.getType()) {
                lastOfflineRecord = record;
            }
        }

        /*
            上线时间：员工今天首次操作“上班”的时间
            下班时间：员工今天最后一次操作“下班”的时间，如果没有操作下班，按当前时间
         */
        Date firstOnlineTime = firstOnlineRecord == null ? null : firstOnlineRecord.getCreateTime();
        Date lastOfflineTime = lastOfflineRecord == null ? null : lastOfflineRecord.getCreateTime();

        /*
            计算上班时长 上班总时长=（下班时间-上班时间）累加（保留几时几分）
            若没下班用当前时间计算
         */
        long onlineSecond = calcTodayTargetStatusDuration(user, userId, orderType, records, StaffStatusEnum.online);

        /*
            休息总时长=当天累计（操作上班的时间-操作休息的时间）
         */
        long stopSecond = calcTodayTargetStatusDuration(user, userId, orderType, records, StaffStatusEnum.stop);

        /*
            空闲总时长=员工工作状态为“上班”，但是没有”处理中“&”稍后处理“的工单的时间累计
         */
        long freeSecond = staffFreeStatService.getTodayGroupFreeTime(userId);

        vo.setFirstOnlineTime(firstOnlineTime);
        vo.setLastOfflineTime(lastOfflineTime);
        vo.setOnlineSecond(Math.toIntExact(onlineSecond));
        vo.setStopSecond(Math.toIntExact(stopSecond));
        vo.setFreeSecond(Math.toIntExact(freeSecond));
        return vo;
    }

    @Async(WorkOrderConfig.Async.AUTO_CHECK)
    @Override
    public void onOrderCreate(WorkOrderBase wordOrder) {
        //将操作人信息插入staff_status表中

        if(wordOrder.getOperatorId() <= 0){
            return;
        }
        //工单类型不为举报类型不插入
        if(!WorkOrderType.REPORT_TYPES.contains(wordOrder.getOrderType())){
            return;
        }
        StaffStatus staffInfo = staffStatusDao.getStaffStatus(wordOrder.getOperatorId(),wordOrder.getOrderType());
        //如果已存在就不插入
        if(staffInfo != null){
            return;
        }
        StaffStatus staffStatus = new StaffStatus();
        staffStatus.setUserId(wordOrder.getOperatorId());
        staffStatus.setOrderType(wordOrder.getOrderType());
        staffStatus.setStaffStatus(3);
        changeStatus(staffStatus);
    }

    @Override
    public Response<List<StaffSelectVO>> getAllStaffOfSelect(String orderType) {
        List<Integer> types = Arrays.stream(orderType.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        final List<Long> userIdList = staffStatusDao.getAllUserIdOnJobByTypes(types);

        //获取用户信息
        List<AuthUserDto> models = userFeignClient.getAuthUserByIds(userIdList).getData();
        Map<Long, AuthUserDto> adminUserAccountModelMap = models.stream().collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity()));
        final List<StaffSelectVO> result = userIdList.stream()
                .map(userId -> {
                    final StaffSelectVO v = new StaffSelectVO();
                    v.setUserId(userId);
                    final AuthUserDto authUserDto = adminUserAccountModelMap.get(userId);
                    v.setName(authUserDto.getUserName());
                    v.setLoginName(authUserDto.getLoginName());
                    return v;
                }).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public int getDistinctUserOnlineCount(List<Integer> orderTypes, List<Long> userIds) {
        if (CollectionUtils.isEmpty(orderTypes) || CollectionUtils.isEmpty(userIds)) {
            return 0;
        }
        return staffStatusDao.getDistinctUserOnlineCount(orderTypes, userIds);
    }
}
