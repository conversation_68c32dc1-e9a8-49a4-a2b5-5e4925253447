package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.clewtrack.param.ChannelRefineDTO;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2020/8/26
 */
@Service("twWorkOrderService")
@Slf4j
public class TwWorkOrderServiceImpl extends WorkOrderFacade<TwWorkOrder, TwHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public OpResult vlidate(TwWorkOrder wordOrder) {

        List<Integer> types = workOrderTypeService.getByOneLevel(OneTypeEnum.tw.getType());

        Set<Integer> integers = types.stream().collect(Collectors.toSet());

        if (!integers.contains(wordOrder.getOrderType())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                types, HandleResultEnum.unDoResult());

        if (workOrderBase != null){
            log.info(" tw vlidate caseId={} repeat workOrderBase:{}", wordOrder.getCaseId(), workOrderBase);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(TwWorkOrder wordOrder) {

        wordOrder.setOrderlevel(OrderLevel.D.getType());

        FeignResponse<CrowdfundingInfo> feignResponse =  client.getCaseInfoById(wordOrder.getCaseId());

        CrowdfundingInfo c = feignResponse.getData();

        if(c == null){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> m = baseWorkOrderService.getChannel(Lists.newArrayList(c));
        if (ChannelRefine.ChannelRefineResuleEnum.XIANXIA_BD.equals(m.get(c.getId()))){
            wordOrder.setOrderlevel(OrderLevel.B.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);
        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(TwHandleOrderParam param) {

        int result = workOrderDao.handle(param.getWorkOrderId(),param.getHandleResult());
        if (result > 0){

            orderExtService.saveIfNotEmpty(param.getWorkOrderId(), OrderExtName.CallStatus, param.getCallStatus());


            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    @Override
    public OpResult<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param) {
        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize+1;

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(),results,p,param.getWorkOrderId(),
                paging,param.getCaseId(),param.getStartTime(),param.getEndTime(),param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }


        List<Integer> caseIds = list.stream().map(r->r.getCaseId()).collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        Map<Integer,ChannelRefine.ChannelRefineResuleEnum> recordMap =baseWorkOrderService.getChannel(feignResponse);

        List<Long> workOrderId = list.stream().map(r->r.getId()).collect(Collectors.toList());
        List<WorkOrderExt> extList = orderExtService.getWorkOrderExts(workOrderId,OrderExtName.CallStatus.getName());
        Map<Long,WorkOrderExt> extMap = extList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,Function.identity()));


        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{

            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            workOrderVO.setTitle(c.getTitle());

            ChannelRefine.ChannelRefineResuleEnum resuleEnum = recordMap.get(r.getCaseId());

            if (resuleEnum != null){
                workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
                workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
            }

            WorkOrderExt ext = extMap.get(r.getId());
            if(ext != null){
                workOrderVO.setCallStatus(ext.getExtValue());
            }

            workOrderVO.setCaseUuid(c.getInfoId());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());

            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }
}
