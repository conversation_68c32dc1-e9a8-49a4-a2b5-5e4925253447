package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-04-22 20:52
 **/
@Service
public class WorkOrderExtServiceImpl implements WorkOrderExtService {
    @Autowired
    private WorkOrderDaoExt workOrderDaoExt;

    @Override
    public void addByList(List<WorkOrderExtVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        ArrayList<WorkOrderExt> workOrderExts = Lists.newArrayList();
        for (WorkOrderExtVO vo : list) {
            WorkOrderExt workOrderExt = new WorkOrderExt();
            workOrderExt.setWorkOrderId(vo.getWorkOrderId());
            workOrderExt.setExtName(vo.getName());
            workOrderExt.setExtValue(vo.getValue());
            workOrderExts.add(workOrderExt);
        }
        workOrderDaoExt.createWorkOrderExt(workOrderExts);
    }

    @Override
    public void markDeleteByName(long workOrderId, String name) {
        workOrderDaoExt.delete(workOrderId, name);
    }

    @Override
    public void updateByNameValue(long workOrderId, String name, String value) {
        workOrderDaoExt.updateByNameValue(workOrderId, name, value);
    }

    @Override
    public WorkOrderExtVO getLastByName(long workOrderId, String name) {
        WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderId, name);
        WorkOrderExtVO vo = convert(workOrderExt);
        return vo;
    }

    @Override
    public List<WorkOrderExtVO> getListByName(long workOrderId, String name) {
        List<WorkOrderExt> orderExts = workOrderDaoExt.getWorkOrderExtNoDelete(workOrderId, name);
        ArrayList<WorkOrderExtVO> voList = Lists.newArrayList();
        for (WorkOrderExt workOrderExt : orderExts) {
            WorkOrderExtVO vo = convert(workOrderExt);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<WorkOrderExtVO> getListByNameAndIdList(List<Long> workOrderIdList, String name) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return Collections.emptyList();
        }
        List<WorkOrderExtVO> voList = Lists.newArrayList();
        List<WorkOrderExt> workOrderExts = workOrderDaoExt.getWorkOrderExts(workOrderIdList, name);
        for (WorkOrderExt workOrderExt : workOrderExts) {
            WorkOrderExtVO vo = convert(workOrderExt);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public WorkOrderExtVO addByNameValue(long workOrderId, String name, String value) {
        WorkOrderExt workOrderExt = new WorkOrderExt();
        workOrderExt.setExtValue(value);
        workOrderExt.setExtName(name);
        workOrderExt.setWorkOrderId(workOrderId);
        int res = workOrderDaoExt.insertWorkOrderExtGetId(workOrderExt);
        if (res <= 0 ){
            return null;
        }
        WorkOrderExtVO vo = convert(workOrderExt);
        return vo;
    }

    @Override
    public List<WorkOrderExt> getWorkOrderExt(long workOrderId) {
        return workOrderDaoExt.getWorkOrderExtByWorkOrderId(workOrderId);
    }

    @Override
    public List<WorkOrderExt> getWorkOrderExt(List<Long> ids) {
        return workOrderDaoExt.getWorkOrderExtByWorkOrderIds(ids);
    }

    @Override
    public Map<String, String> getExtMapByOrderId(Long id) {
        List<WorkOrderExt> list = getWorkOrderExt(id);
        Map<String, String> map = Maps.newHashMap();
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (WorkOrderExt e : list) {
            map.put(e.getExtName(), e.getExtValue());
        }
        return map;
    }

    public WorkOrderExtVO convert(WorkOrderExt workOrderExt){
        if (workOrderExt == null) {
            return null;
        }
        WorkOrderExtVO vo = new WorkOrderExtVO();
        vo.setWorkOrderId(workOrderExt.getWorkOrderId());
        vo.setName(workOrderExt.getExtName());
        vo.setValue(workOrderExt.getExtValue());
        vo.setCreateTime(workOrderExt.getCreateTime());
        vo.setId(workOrderExt.getId());
        return vo;
    }
}
