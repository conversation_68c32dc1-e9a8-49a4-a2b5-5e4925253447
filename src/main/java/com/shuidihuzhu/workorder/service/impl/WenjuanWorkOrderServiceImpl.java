package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/12/11
 */
@Service("wenjuanWorkOrderService")
@Slf4j
public class WenjuanWorkOrderServiceImpl extends WorkOrderFacade<WenjuanWorkOrder, HandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(WenjuanWorkOrder wordOrder) {
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(WenjuanWorkOrder wordOrder) {

        if (wordOrder.getOrderlevel() == 0){
            wordOrder.setOrderlevel(OrderLevel.edium.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(HandleOrderParam param) {

        int result = workOrderDao.handle(param.getWorkOrderId(),param.getHandleResult());

        if (result > 0){
            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    @Override
    public OpResult getOrderList(WorkOrderListParam param) {

        if (param.getUserId() == 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize+1;

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(),results,p,param.getWorkOrderId(),
                paging,param.getCaseId(),param.getStartTime(),param.getEndTime(),param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Long> orderIds =  list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

        Map<Long,String> mobileMap = getExt(orderIds, OrderExtName.mobile.getName());

        Map<Long,String> recordIdMap = getExt(orderIds, OrderExtName.recordId.getName());

        Map<Long,String> channelMap = getExt(orderIds, OrderExtName.channel.getName());


        List<WorkOrderVO> voList = list.stream().map(r->{

            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setMobile(mobileMap.get(r.getId()));
            workOrderVO.setRecordId(recordIdMap.get(r.getId()));
            workOrderVO.setChannel(channelMap.get(r.getId()));

            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }
}
