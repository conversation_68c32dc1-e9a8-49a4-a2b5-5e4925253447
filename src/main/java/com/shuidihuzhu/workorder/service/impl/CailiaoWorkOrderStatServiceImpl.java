package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.CaiLiaoWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.AuditStatus;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.WorkOrderStatDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;

/**
 * <AUTHOR>
 * @DATE 2019/4/1
 */
@Service("cailiaoWorkOrderStatServiceImpl")
public class CailiaoWorkOrderStatServiceImpl implements WorkOrderStatService<CaiLiaoWorkOrderStat> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderStatDao workOrderStatDao;

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.exception_done.getType(),
            HandleResultEnum.manual_lock.getType(),
            HandleResultEnum.audit_pass.getType(),
            HandleResultEnum.audit_reject.getType());

    @Override
    public OpResult<List<CaiLiaoWorkOrderStat>> getWorkOrderStatList(int one, String two, long userId) {

        if (one<=0){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        //如果二级没选
       if (StringUtils.isEmpty(two)){
           return getOne(one,userId);
       }else {
           List<Integer> twoList = Arrays.stream(two.split(",")).map(Integer::valueOf).collect(Collectors.toList());
           return getTwo(one,twoList,userId);
       }
    }

    public OpResult<List<CaiLiaoWorkOrderStat>> getTwo(int oneLevel ,List<Integer> twoLevel ,long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getTwoAndUser(oneLevel,twoLevel,userId));
        }else {
            return OpResult.createSucResult(getOnlyTwo(oneLevel,twoLevel));
        }
    }

    public OpResult<List<CaiLiaoWorkOrderStat>> getOne(int oneLevel ,long userId) {

        //选人了
        if (userId >= 0){
            return OpResult.createSucResult(getOneAndUser(oneLevel,userId));
        }else {
           return OpResult.createSucResult(getOnlyOne(oneLevel));
        }
    }

    public List<CaiLiaoWorkOrderStat> getOneAndUser(int oneLevel,long userId){

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        List<Integer>  twoList = workOrderTypeService.getByOneLevel(oneLevel);
        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().distinct().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getOneAndUser(oneLevel,twoList,users,doneList);

        //手动
        List<WorkOrderStat> manualList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.manual_lock.getType(),today);
        Map<Long,Integer> manualMap = manualList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //自动
        List<WorkOrderStat> autoList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.exception_done.getType(),today);
        Map<Long,Integer> autoMap = autoList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> passList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_pass.getType(),today);
        Map<Long,Integer> passMap = passList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> refuseList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_reject.getType(),today);
        Map<Long,Integer> refuseMap = refuseList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return list.stream()
                .map(r->{
                    CaiLiaoWorkOrderStat cws = new CaiLiaoWorkOrderStat();

                    BeanUtils.copyProperties(r,cws);

                    if (manualMap.get(r.getUserId()) != null){
                        cws.setManualLockAmount(manualMap.get(r.getUserId()));
                    }

                    if (autoMap.get(r.getUserId()) != null){
                        cws.setAutoLockAmount(autoMap.get(r.getUserId()));
                    }

                    if (passMap.get(r.getUserId()) != null){
                        cws.setPassAmount(passMap.get(r.getUserId()));
                    }

                    if (refuseMap.get(r.getUserId()) != null){
                        cws.setRejectAmount(refuseMap.get(r.getUserId()));
                    }

                    return cws;

                }).collect(Collectors.toList());


    }



    public List<CaiLiaoWorkOrderStat> getOnlyOne(int oneLevel) {

        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        //手动
        List<WorkOrderStat> manualList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.manual_lock.getType(),today);
        int manualNum = manualList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();

        //自动
        List<WorkOrderStat> autoList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.exception_done.getType(),today);
        int autoNum = autoList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();

        List<WorkOrderStat> passList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.audit_pass.getType(),today);
        int passNum = passList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();

        List<WorkOrderStat> refuseList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.audit_reject.getType(),today);
        int refuseNum = refuseList.stream().mapToInt(WorkOrderStat::getQueryNum).sum();


        CaiLiaoWorkOrderStat cws = new CaiLiaoWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,cws);

        cws.setManualLockAmount(manualNum);
        cws.setAutoLockAmount(autoNum);
        cws.setPassAmount(passNum);
        cws.setRejectAmount(refuseNum);

        return Lists.newArrayList(cws);

    }


    public List<CaiLiaoWorkOrderStat> getOnlyTwo(int one ,List<Integer> twoList) {

        String today = LocalDate.now() + " 00:00:00";

        List<WorkOrderStat> stats = baseStatService.getTwoALL(one,twoList,doneList);

        //手动
        List<WorkOrderStat> manualList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.manual_lock.getType(),today);
        Map<Integer,WorkOrderStat> manualMap = manualList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), Function.identity()));

        //自动
        List<WorkOrderStat> autoList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.exception_done.getType(),today);
        Map<Integer,WorkOrderStat> autoMap = autoList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), Function.identity()));

        List<WorkOrderStat> passList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.audit_pass.getType(),today);
        Map<Integer,WorkOrderStat> passMap = passList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), Function.identity()));

        List<WorkOrderStat> refuseList = workOrderStatDao.getResultStat(twoList, Lists.newArrayList(), HandleResultEnum.audit_reject.getType(),today);
        Map<Integer,WorkOrderStat> refuseMap = refuseList.stream().collect(Collectors.toMap(rr->rr.getTwoLevel(), Function.identity()));


        return stats.stream()
                .map(r->{
                    CaiLiaoWorkOrderStat cws = new CaiLiaoWorkOrderStat();

                    BeanUtils.copyProperties(r,cws);

                    if (manualMap.get(r.getTwoLevel()) != null){
                        cws.setManualLockAmount(manualMap.get(r.getTwoLevel()).getQueryNum());
                    }

                    if (autoMap.get(r.getTwoLevel()) != null){
                        cws.setAutoLockAmount(autoMap.get(r.getTwoLevel()).getQueryNum());
                    }

                    if (passMap.get(r.getTwoLevel()) != null){
                        cws.setPassAmount(passMap.get(r.getTwoLevel()).getQueryNum());
                    }

                    if (refuseMap.get(r.getTwoLevel()) != null){
                        cws.setRejectAmount(refuseMap.get(r.getTwoLevel()).getQueryNum());
                    }

                    return cws;

                }).collect(Collectors.toList());
    }


    public List<CaiLiaoWorkOrderStat> getTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        if (twoLevel.size()>1){
            return getAllTwoAndUser(oneLevel,twoLevel,userId);
        }

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        //0代表全部用户
        if(userId == 0){
            users = staffStatusDao.getUserIdByTypes(twoList).stream().collect(Collectors.toList());
        }

        List<WorkOrderStat> list = baseStatService.getTwoAndUser(oneLevel,twoLevel.get(0),users,doneList);

        //手动
        List<WorkOrderStat> manualList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.manual_lock.getType(),today);
        Map<Long,Integer> manualMap = manualList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //自动
        List<WorkOrderStat> autoList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.exception_done.getType(),today);
        Map<Long,Integer> autoMap = autoList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> passList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_pass.getType(),today);
        Map<Long,Integer> passMap = passList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> refuseList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_reject.getType(),today);
        Map<Long,Integer> refuseMap = refuseList.stream().collect(Collectors.groupingBy(WorkOrderStat::getUserId,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return list.stream()
                .map(r->{
                    CaiLiaoWorkOrderStat cws = new CaiLiaoWorkOrderStat();

                    BeanUtils.copyProperties(r,cws);

                    if (manualMap.get(r.getUserId()) != null){
                        cws.setManualLockAmount(manualMap.get(r.getUserId()));
                    }

                    if (autoMap.get(r.getUserId()) != null){
                        cws.setAutoLockAmount(autoMap.get(r.getUserId()));
                    }

                    if (passMap.get(r.getUserId()) != null){
                        cws.setPassAmount(passMap.get(r.getUserId()));
                    }

                    if (refuseMap.get(r.getUserId()) != null){
                        cws.setRejectAmount(refuseMap.get(r.getUserId()));
                    }

                    return cws;

                }).collect(Collectors.toList());
    }


    public List<CaiLiaoWorkOrderStat> getAllTwoAndUser(int oneLevel,List<Integer> twoLevel,long userId){

        String today = LocalDate.now() + " 00:00:00";

        List<Long> users = Lists.newArrayList(userId);
        //二级分类只有一个   直接可以按照人员分组
        List<Integer>  twoList = Lists.newArrayList(twoLevel);

        List<WorkOrderStat> list = baseStatService.getAllTwoAndUser(oneLevel,twoLevel,userId,doneList);

        //手动
        List<WorkOrderStat> manualList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.manual_lock.getType(),today);
        Map<Integer,Integer> manualMap = manualList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        //自动
        List<WorkOrderStat> autoList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.exception_done.getType(),today);
        Map<Integer,Integer> autoMap = autoList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> passList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_pass.getType(),today);
        Map<Integer,Integer> passMap = passList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));

        List<WorkOrderStat> refuseList = workOrderStatDao.getResultStat(twoList, users, HandleResultEnum.audit_reject.getType(),today);
        Map<Integer,Integer> refuseMap = refuseList.stream().collect(Collectors.groupingBy(WorkOrderStat::getTwoLevel,Collectors.summingInt(WorkOrderStat::getQueryNum)));


        return list.stream()
                .map(r->{
                    CaiLiaoWorkOrderStat cws = new CaiLiaoWorkOrderStat();

                    BeanUtils.copyProperties(r,cws);

                    if (manualMap.get(r.getTwoLevel()) != null){
                        cws.setManualLockAmount(manualMap.get(r.getTwoLevel()));
                    }

                    if (autoMap.get(r.getTwoLevel()) != null){
                        cws.setAutoLockAmount(autoMap.get(r.getTwoLevel()));
                    }

                    if (passMap.get(r.getTwoLevel()) != null){
                        cws.setPassAmount(passMap.get(r.getTwoLevel()));
                    }

                    if (refuseMap.get(r.getTwoLevel()) != null){
                        cws.setRejectAmount(refuseMap.get(r.getTwoLevel()));
                    }

                    return cws;

                }).collect(Collectors.toList());
    }

}
