package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.UgcWorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat;
import com.shuidihuzhu.client.cf.workorder.model.enums.ClassifyTypeEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatFacadeService;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.util.MapUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/11
 */
@Service("ugcWorkOrderStatService")
public class UgcWorkOrderStatServiceImpl extends WorkOrderStatFacadeService<UgcWorkOrderStat> implements WorkOrderStatService<UgcWorkOrderStat> {


    @Autowired
    private WorkOrderBaseStatService baseStatService;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;


    private static List<Integer> doneList = Lists.newArrayList(HandleResultEnum.done.getType(),
            HandleResultEnum.pass_show.getType(),
            HandleResultEnum.only_self.getType());


    @Override
    public List<Integer> getDoneList() {
        return doneList;
    }

    @Override
    public List<UgcWorkOrderStat> getOnlyOneLevel(int oneLevel) {
        String today = LocalDate.now() + " 00:00:00";

        List<Integer> twoList = workOrderTypeService.getByOneLevel(oneLevel);
        WorkOrderStat workOrderStat = baseStatService.getOneALL(oneLevel,twoList,doneList);

        List<Long> users = Lists.newArrayList();

        int showNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.pass_show,today);

        int selfNum = baseStatService.getStatusInt(twoList,users,HandleResultEnum.only_self,today);

        UgcWorkOrderStat s = new UgcWorkOrderStat();

        BeanUtils.copyProperties(workOrderStat,s);
        s.setPassShowAmount(showNum);
        s.setOnlySelfAmount(selfNum);

        return Lists.newArrayList(s);
    }

    @Override
    public List<UgcWorkOrderStat> getTypeStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer,Map<Integer,Integer>> result = baseStatService.getTypeStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{

                    UgcWorkOrderStat c = new UgcWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setPassShowAmount(MapUtil.getFromMap(result.get(HandleResultEnum.pass_show.getType()),r.getTwoLevel()));
                    c.setOnlySelfAmount(MapUtil.getFromMap(result.get(HandleResultEnum.only_self.getType()),r.getTwoLevel()));

                    return c;

                }).collect(Collectors.toList());

    }

    @Override
    public List<UgcWorkOrderStat> getUserStat(List<WorkOrderStat> list,List<Long> users,List<Integer> twoList){

        Map<Integer, Map<Long,Integer>> result = baseStatService.getUserStatBase(users,twoList,doneList);

        return list.stream()
                .map(r->{
                    UgcWorkOrderStat c = new UgcWorkOrderStat();

                    BeanUtils.copyProperties(r,c);

                    c.setPassShowAmount(MapUtil.getFromMap(result.get(HandleResultEnum.pass_show.getType()),r.getUserId()));
                    c.setOnlySelfAmount(MapUtil.getFromMap(result.get(HandleResultEnum.only_self.getType()),r.getUserId()));

                    return c;

                }).collect(Collectors.toList());
    }

}
