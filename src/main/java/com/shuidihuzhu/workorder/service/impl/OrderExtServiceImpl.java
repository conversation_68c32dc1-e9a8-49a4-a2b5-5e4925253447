package com.shuidihuzhu.workorder.service.impl;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderDataStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/4/18
 */
@Service
@Slf4j
public class OrderExtServiceImpl implements OrderExtService {


    @Autowired
    private WorkOrderDaoExt daoExt;

    @Autowired
    private WorkOrderDataStatisticsService workOrderDataStatisticsService;

    @Resource(name = "cf2RedissonHandler")
    protected RedissonHandler cf2RedissonHandler;

    private void insertOrUpdate(long workOrderId, String extName, String extValue) {

        WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(workOrderId, extName);

        if (workOrderExt != null) {
            daoExt.delete(workOrderId, extName);
        }
        daoExt.insertWorkOrderExt(workOrderId, extName, extValue);

        //处理数据统计部分，内部有异常处理，不影响主流程
        workOrderDataStatisticsService.handleExtInfo(workOrderId, extName,extValue);
    }

    @Override
    public void saveIfNotEmpty(long workOrderId, OrderExtName extName, String extValue) {
        if (StringUtils.isEmpty(extValue)) {
            return;
        }
        insertOrUpdate(workOrderId, extName.getName(), extValue);
    }

    @Override
    public int replaceExt(long workOrderId, String extName, String extValue) {
        String key = "work_order_ext_replace_"+workOrderId;
        String identity = null;
        try {
            identity = cf2RedissonHandler.tryLock(key, 100, RedissonHandler.ONE_MINUTE);
            if (StringUtils.isNotBlank(identity)) {
                WorkOrderExt workOrderExt = daoExt.getWorkOrderExt(workOrderId, extName);
                if (workOrderExt != null) {
                    daoExt.updateByNameValue(workOrderId, extName, extValue);
                } else {
                    daoExt.insertWorkOrderExt(workOrderId, extName, extValue);
                }

                return 1;
            }
        } catch (InterruptedException e) {
            log.error("", e);
        } finally {
            if (StringUtils.isNotBlank(identity)) {
                cf2RedissonHandler.unLock(key, identity);
            }
        }

        return 0;
    }


    @Override
    public int createWorkOrderExt(List<WorkOrderExt> workOrderExts) {
        if (CollectionUtils.isEmpty(workOrderExts)) {
            return 0;
        }
       int count = daoExt.createWorkOrderExt(workOrderExts);

        //处理数据统计部分，内部有异常处理，不影响主流程
        workOrderDataStatisticsService.handleExtInfo(workOrderExts);

        return count;
    }


    @Override
    public List<WorkOrderExt> getWorkOrderExts(List<Long> workOrderId, String extName) {
        return daoExt.getWorkOrderExts(workOrderId, extName);
    }

    @Override
    public Map<Long, WorkOrderExt> getExtMap(List<Long> workOrderId, OrderExtName extName) {
        List<WorkOrderExt> workOrderExts = getWorkOrderExts(workOrderId, extName.getName());
        return workOrderExts.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity(),(o1, o2)->o2));
    }

    @Override
    public List<WorkOrderExt> getWorkOrderExtList(List<Long> workOrderId, String extName, String extVaule) {
        return daoExt.getWorkOrderExtList(workOrderId, extName, extVaule);
    }

    @Override
    public List<WorkOrderExt> getAllOrderExtIgnoreDelete(long workOrderId, List<String> extNames) {

        return daoExt.getAllOrderExtIgnoreDelete(workOrderId, extNames);
    }

    @Override
    public List<WorkOrderExt> listOrderExtByIdsAndExtNames(List<Long> workOrderIds, List<String> extNames) {
        if (CollectionUtils.isEmpty(workOrderIds) || CollectionUtils.isEmpty(extNames)) {
            return Collections.emptyList();
        }
        return daoExt.listOrderExtByIdsAndExtNames(workOrderIds, extNames);
    }

    @Override
    public WorkOrderExt getByOrderId(long workOrderId, OrderExtName extName) {
        return daoExt.getWorkOrderExt(workOrderId,extName.getName());
    }
}
