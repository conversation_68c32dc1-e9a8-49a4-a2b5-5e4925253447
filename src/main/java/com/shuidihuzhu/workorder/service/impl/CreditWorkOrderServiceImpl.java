package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-12-25 18:02
 **/
@Service("creditWorkOrderService")
@Slf4j
public class CreditWorkOrderServiceImpl extends WorkOrderFacade<CreditWorkOrder, CreditHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private WorkTypePropertyService orderTypePropertyService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    private List<Integer> creditWorkOrder = Lists.newArrayList();

    @PostConstruct
    public void init() {
        creditWorkOrder = workOrderTypeService.getByOneLevel(OneTypeEnum.zengxin.getType());
    }

    @Override
    public OpResult vlidate(CreditWorkOrder wordOrder) {
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                creditWorkOrder, HandleResultEnum.unDoResult());
        if (workOrderBase != null) {
            log.info("重复创建增信工单,wordOrder:{}", wordOrder);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(CreditWorkOrder wordOrder) {
        if (wordOrder.getOrderlevel() == 0) {
            wordOrder.setOrderlevel(OrderLevel.edium.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(CreditHandleOrderParam param) {

        //如果审核和回访工单和电话审核稍后处理判断一下数量
        if (creditWorkOrder.contains(param.getOrderType())
                && param.getHandleResult() == HandleResultEnum.later_doing.getType()) {

            int count = baseWorkOrderService.getCountByHandleResult(param.getUserId(), param.getOrderType(), param.getHandleResult() + "");

            if (count >= orderTypePropertyService.getMaxDelayCount(param.getOrderType())) {
                return OpResult.createFailResult(ErrorCode.BUSI_YANHOU_COUNT);
            }
        }

        int result = workOrderDao.handle(param.getWorkOrderId(), param.getHandleResult());
        if (result > 0) {
            return OpResult.createSucResult(result);
        }
        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    @Override
    public OpResult getOrderList(WorkOrderListParam param) {
        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize + 1;

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(), results, p, param.getWorkOrderId(),
                paging, param.getCaseId(), param.getStartTime(), param.getEndTime(), param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());

        List<Long> orderIds = list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        Map<Integer, CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (o1, o2) -> o2));

        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> recordMap = baseWorkOrderService.getChannel(Lists.newArrayList(map.values()));

        List<WorkOrderExt> extList = orderExtService.getWorkOrderExts(orderIds, OrderExtName.CallStatus.getName());
        Map<Long, WorkOrderExt> extMap = extList.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId, Function.identity()));

        List<WorkOrderVO> voList = list.stream().filter(r -> {
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())) {
                return true;
            }
            log.error("creditOrder getOrderList caseInfo=null caseId={}", r.getCaseId());
            return false;

        }).map(r -> {
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUuid(c.getInfoId());

            ChannelRefine.ChannelRefineResuleEnum resuleEnum = recordMap.get(r.getCaseId());

            if (resuleEnum != null) {
                workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
                workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
            }

            WorkOrderExt ext = extMap.get(r.getId());
            if (ext != null) {
                workOrderVO.setCallStatus(ext.getExtValue());
            }

            return workOrderVO;

        }).collect(Collectors.toList());

        //如果是处理中和稍后处理   按照优先级排序一下
        if (results.contains(HandleResultEnum.doing.getType()) ||
                results.contains(HandleResultEnum.later_doing.getType())) {
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId)).collect(Collectors.toList());
            voList = voList.stream().sorted(Comparator.comparing(WorkOrderVO::getOrderLevel).reversed()).collect(Collectors.toList());
        }


        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }
}
