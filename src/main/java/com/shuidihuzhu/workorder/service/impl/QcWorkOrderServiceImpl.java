package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-06-12 15:12
 **/
@Service("qcWorkOrderServiceImpl")
public class QcWorkOrderServiceImpl extends WorkOrderFacade<QcWorkOrder, QcHandleOrderParam, WorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private OrderExtService orderExtService;
    @Autowired
    private WorkOrderDaoExt workOrderDaoExt;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(QcWorkOrder wordOrder) {
        if (!WorkOrderType.QC_WORK_ORDER_LIST.contains(wordOrder.getOrderType())) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(QcWorkOrder wordOrder) {
        int type;
        if (wordOrder.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()
                || wordOrder.getOrderType() == WorkOrderType.qc_common_repeat.getType()) {
            type = OrderLevel.D.getType();
        } else {
            type = OrderLevel.C.getType();
        }
        wordOrder.setOrderlevel(type);
        if (wordOrder.getAssignStatus() == AssignTypeEnum.ASSIGN.getCode()) {
            wordOrder.setHandleResult(HandleResultEnum.not_auto_assign.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(QcHandleOrderParam param) {
        final int handle = param.getHandleResult();
        if (handle != HandleResultEnum.later_doing.getType()
                && handle != HandleResultEnum.done.getType()
                && handle != HandleResultEnum.manual_lock.getType()) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());
        if (Objects.isNull(workOrderBase)) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_ERROR);
        }
        int handleResult = workOrderBase.getHandleResult();
        if (handleResult != HandleResultEnum.doing.getType() && handleResult != HandleResultEnum.later_doing.getType()) {
            return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }
        int result = workOrderDao.reportHandle(param.getWorkOrderId(), param.getHandleResult());
        if (result > 0) {
            WorkOrderExt ext = new WorkOrderExt(param.getWorkOrderId(), param.getOperComment(),
                    DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            orderExtService.createWorkOrderExt(Lists.newArrayList(ext));
            return OpResult.createSucResult(result);
        }
        return OpResult.createFailResult(ErrorCode.WORK_ORDER_STATUS_ERROR);
    }


    @Override
    public OpResult getOrderList(WorkOrderListParam param) {
        if (param.getUserId() <= 0 || StringUtils.isEmpty(param.getHandleResult())) {
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize() + 1;

        List<Integer> results = Arrays.stream(param.getHandleResult().split(","))
                .map(Integer::valueOf).collect(Collectors.toList());

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(param.getUserId()),
                param.getOrderType(), results, p, param.getWorkOrderId(), param.getPaging(), 0,
                param.getStartTime(), param.getEndTime(), 0);

        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)) {
            pageResult.setPageList(Collections.emptyList());
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }
        /**
         * 获取案例信息
         */
        Map<Integer, CrowdfundingInfo> infoMap = Maps.newHashMap();
        if (param.getOrderType() != WorkOrderType.qc_wx_1v1.getType() &&
                param.getOrderType() != WorkOrderType.qc_call.getType()) {
            List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).collect(Collectors.toList());
            FeignResponse<List<CrowdfundingInfo>> crowdfundingResponse = crowdfundingFeignClient.getCrowdfundingListById(caseIds);
            if (crowdfundingResponse.ok() && CollectionUtils.isNotEmpty(crowdfundingResponse.getData())) {
                List<CrowdfundingInfo> crowdfundingInfos = crowdfundingResponse.getData();
                infoMap = crowdfundingInfos.stream()
                        .collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (k1, k2) -> k2));
            }
        }

        Map<Integer, CrowdfundingInfo> finalInfoMap = infoMap;
        List<WorkOrderVO> voList = list.stream().map(r -> {
            WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(r.getId(), OrderExtName.qcId.getName());
            WorkOrderVO workOrderVO = new WorkOrderVO();
            workOrderVO.setWorkOrderId(r.getId());
            if (r.getOrderType() != WorkOrderType.qc_wx_1v1.getType()
                    && r.getOrderType() != WorkOrderType.qc_call.getType()
                    && r.getOrderType() != WorkOrderType.qc_wx_1v1_repeat.getType()
                    && r.getOrderType() != WorkOrderType.qc_hospital_dept.getType()
            ) {
                workOrderVO.setCaseId(r.getCaseId());
                CrowdfundingInfo crowdfundingInfo = finalInfoMap.get(r.getCaseId());
                if (Objects.nonNull(crowdfundingInfo)) {
                    workOrderVO.setCaseUuid(crowdfundingInfo.getInfoId());
                    workOrderVO.setTitle(crowdfundingInfo.getTitle());
                    workOrderVO.setCaseUserId(crowdfundingInfo.getUserId());
                }
            }
            if (r.getOrderType() == WorkOrderType.qc_wx_1v1.getType()
                    || r.getOrderType() == WorkOrderType.qc_call.getType()
                    || r.getOrderType() == WorkOrderType.qc_wx_1v1_repeat.getType()) {
                //微信1v1工单的caseId保存的是1v1服务任务的id 不是案例id
                workOrderVO.setTaskId(r.getCaseId());
            }
            workOrderVO.setOrderType(r.getOrderType());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setOperatorId(r.getOperatorId());

            workOrderVO.setQcId(Long.parseLong(workOrderExt.getExtValue()));
            return workOrderVO;
        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }
}
