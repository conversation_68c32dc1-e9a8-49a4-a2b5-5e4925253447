package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.UgcWorkOrderDao;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/10/10
 */
@Service("ugcWorkOrderService")
@Slf4j
public class UgcWorkOrderServiceImpl extends WorkOrderFacade<UgcWorkOrder, UgcHandleOrderParam, WorkOrderListParam> {


    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private CrowdfundingFeignClient client;

    @Resource
    private UserOperationRecordDao recordDao;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private UgcWorkOrderDao ugcWorkOrderDao;

    @Resource
    private OrderExtService orderExtService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;


    @Override
    public OpResult vlidate(UgcWorkOrder wordOrder) {

        return OpResult.createSucResult();
    }

    @Override
    public OpResult<Long> create(UgcWorkOrder wordOrder) {

        if (wordOrder.getOrderlevel() == 0){
            wordOrder.setOrderlevel(OrderLevel.edium.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(UgcHandleOrderParam param) {

        if (param.getOrderType() == WorkOrderType.xiafaprogress.getType()) {
            int result = ugcWorkOrderDao.handle(param.getWorkOrderId(),param.getUserId(),param.getHandleResult());
            return OpResult.createSucResult(result);
        }

        int result = workOrderDao.handleBatch(param.getWorkOrderIds(),param.getHandleResult());

        if (result > 0){

            List<WorkOrderRecord> workOrderRecords = param.getWorkOrderIds().stream().map(v->
                 WorkOrderRecord.create(0, v, param.getOrderType(),param.getUserId(),param.getOperComment(), OperateMode.handle)
            ).collect(Collectors.toList());

            recordDao.saveRecordList(workOrderRecords);

            param.getWorkOrderIds().stream().forEach(r->{
                sendMsg(r,param.getHandleResult());
            });


            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);
    }

    /**
     * ugc 工单批量处理 得但是发送处理消息
     * @param workOrderId
     * @param handleResult
     */
    private void sendMsg(long workOrderId,int handleResult){
        WorkOrderResultChangeEvent e = new WorkOrderResultChangeEvent();
        e.setHandleResult(handleResult);
        e.setWorkOrderId(workOrderId);
        Message<WorkOrderResultChangeEvent> message = MessageBuilder.createWithPayload(e)
                .setTags(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE)
                .addKey(CfClientMQTagCons.WORK_ORDER_STATUS_CHANGE, workOrderId)
                .setDelayLevel(DelayLevel.S1)
                .build();
        producer.send(message);
    }

    @Override
    public OpResult<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param) {

        if (param.getUserId() == 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize+1;

        List<WorkOrderBase> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(param.getCaseIds())) {
            list = ugcWorkOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(),results,p,param.getWorkOrderId(),
                    paging,param.getCaseId(),param.getStartTime(),param.getEndTime(),param.getOrderLevel(),param.getReasonCode()>0?""+param.getReasonCode():null);
        } else {
            list = workOrderDao.getWorkOrderListByCaseIds(Lists.newArrayList(userId), param.getOrderType(), results, p, param.getWorkOrderId(),
                    paging, param.getCaseIds(), param.getStartTime(), param.getEndTime(), param.getOrderLevel());
        }

        //多查询一次  判断是否有下一页
        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(r->r.getCaseId()).distinct().collect(Collectors.toList());

        List<Long> orderIds =  list.stream().map(WorkOrderBase::getId).collect(Collectors.toList());

        FeignResponse<List<CrowdfundingInfo>> caseResponse = client.getCrowdfundingListById(caseIds);
        if (caseResponse == null || caseResponse.notOk()) {
            log.error("getOrderList feign getCrowdfundingListById error resp={}", caseResponse);
            return OpResult.createFailResult(ErrorCode.SYSTEM_ERROR);
        }
        List<CrowdfundingInfo> feignResponse = caseResponse.getData();

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(),(o1,o2)->o2));

        Map<Long,String> extIdMap = getExt(orderIds,OrderExtName.extId.getName());

        Map<Long,String> wordIdMap = getExt(orderIds,OrderExtName.wordId.getName());

        Map<Long,String> contentTypeMap = getExt(orderIds,OrderExtName.contentType.getName());

        Map<Long, String> supplyProgressMap = getExt(orderIds, OrderExtName.supplyProgressId.getName());

        Map<Long, String> actionMap = getExt(orderIds, OrderExtName.supplyActionId.getName());

        Map<Long, String> headImageUrlMap = getExt(orderIds, OrderExtName.headImageUrl.getName());

        Map<Long,String> hopeTreeMap = getExt(orderIds,OrderExtName.hopeTreeStateId.getName());

        Map<Long,String> verificationMap = getExt(orderIds,OrderExtName.verificationId.getName());

        Map<Long,String> medicalStatusMap = getExt(orderIds,OrderExtName.medicalStatus.getName());

        Map<Long,String> medicalWorkTypeMap = getExt(orderIds,OrderExtName.medicalWorkType.getName());

        Map<Long,String> consultEvaluationMap = getExt(orderIds,OrderExtName.consultantEvaluationComment.getName());

        Map<Long,String> volunteerName = getExt(orderIds,OrderExtName.volunteerName.getName());


        List<WorkOrderVO> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            //希望树动态不过滤案例
            if(WorkOrderType.ugcprogress.getType() == r.getOrderType() && hopeTreeMap.containsKey(r.getId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setOrderType(r.getOrderType());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(),"yyyy-MM-dd HH:mm:ss"));
            if(c != null){
                workOrderVO.setTitle(c.getTitle());
                workOrderVO.setCaseUuid(c.getInfoId());
            }

            workOrderVO.setExtId(extIdMap.get(r.getId()));
            workOrderVO.setWordId(wordIdMap.get(r.getId()));
            workOrderVO.setContentType(contentTypeMap.get(r.getId()));
            String supplyProgressIdString = supplyProgressMap.get(r.getId());
            if (StringUtils.isNotBlank(supplyProgressIdString)) {
                workOrderVO.setSupplyProgressId(Long.parseLong(supplyProgressIdString));
            }
            workOrderVO.setSupplyActionId(Optional.ofNullable(actionMap.get(r.getId())).map(Long::valueOf).orElse(0l));
            workOrderVO.setHeadImageUrl(headImageUrlMap.get(r.getId()));
            workOrderVO.setHopeTreeStateId(hopeTreeMap.get(r.getId()));
            workOrderVO.setVerificationId(verificationMap.get(r.getId()));
            workOrderVO.setMedicalStatus(medicalStatusMap.get(r.getId()));
            workOrderVO.setMedicalWorkType(medicalWorkTypeMap.get(r.getId()));
            workOrderVO.setConsultantEvaluationComment(consultEvaluationMap.get(r.getId()));
            workOrderVO.setVolunteerName(volunteerName.get(r.getId()));
            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);

    }


    public List<WorkOrderVO> getOrderListByIds(List<Long> ids){

        List<WorkOrderBase> list = workOrderDao.listById(ids);

        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

        Map<Long,String> wordIdMap = getExt(ids,OrderExtName.wordId.getName());
        Map<Long,String> contentTypeMap = getExt(ids,OrderExtName.contentType.getName());
        Map<Long, String> headImageUrlMap = getExt(ids, OrderExtName.headImageUrl.getName());
        Map<Long, String> verificationIdMap = getExt(ids, OrderExtName.verificationId.getName());
        Map<Long, String> medicalStatus = getExt(ids, OrderExtName.medicalStatus.getName());
        Map<Long, String> medicalWorkTypeMap = getExt(ids, OrderExtName.medicalWorkType.getName());
        Map<Long,String> consultEvaluationMap = getExt(ids,OrderExtName.consultantEvaluationComment.getName());
        Map<Long,String> volunteerName = getExt(ids,OrderExtName.volunteerName.getName());

        List<WorkOrderVO> voList = list.stream().map(r->{
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            workOrderVO.setWordId(wordIdMap.get(r.getId()));
            workOrderVO.setContentType(contentTypeMap.get(r.getId()));
            workOrderVO.setHeadImageUrl(headImageUrlMap.get(r.getId()));
            workOrderVO.setVerificationId(verificationIdMap.get(r.getId()));
            workOrderVO.setMedicalStatus(medicalStatus.get(r.getId()));
            workOrderVO.setMedicalWorkType(medicalWorkTypeMap.get(r.getId()));
            workOrderVO.setConsultantEvaluationComment(consultEvaluationMap.get(r.getId()));
            workOrderVO.setVolunteerName(volunteerName.get(r.getId()));
            return workOrderVO;

        }).collect(Collectors.toList());

        return voList;
    }


    public int ugcCreateWorkOrderExt(UgcWorkOrder workOrder) {
        return orderExtService.createWorkOrderExt(workOrder.getWorkOrderExt());
    }
}
