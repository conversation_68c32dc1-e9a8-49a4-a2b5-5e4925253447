package com.shuidihuzhu.workorder.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.clewtrack.model.ChannelRefine;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderLevel;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.DianhuaWorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.model.vo.GenjinModel;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.mq.producer.DianhuaCreateMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2019/8/14
 */
@Service("dianhuaWorkOrderService")
@Slf4j
public class DianhuaWorkOrderServiceImpl extends WorkOrderFacade<DianhuaWorkOrder, DianhuaHandleOrderParam, DianhuaWorkOrderListParam> {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private DianhuaCreateMq dianhuaCreateMq;

    @Autowired
    private DianhuaWorkOrderDao dianhuaWorkOrderDao;

    @Autowired
    private CrowdfundingFeignClient client;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private OrderExtService extService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    @Override
    public OpResult vlidate(DianhuaWorkOrder wordOrder) {

        // 如果创建的是核实工单  直接关闭跟进工单
        // 如果创建的是跟进工单 关闭之前的跟进工单
        int orderType = wordOrder.getOrderType();
        if (WorkOrderType.heshi.getType() == orderType) {
            closeGenjin(wordOrder, "创建核实工单，关闭跟进工单");
        }
        if (WorkOrderType.genjin.getType() == orderType) {
            closeGenjin(wordOrder, "创建新的跟进工单，关闭老的跟进工单");
        }

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderBycaseIdAndTypes(wordOrder.getCaseId(),
                Lists.newArrayList(WorkOrderType.heshi.getType()),
                HandleResultEnum.unDoResult());

        if (workOrderBase != null){
            log.info("dianhua vlidate caseId={} repeat",wordOrder.getCaseId());
            return OpResult.createFailResult(ErrorCode.SYSTEM_ORDER_REPEAT);
        }

        return OpResult.createSucResult();
    }

    private void closeGenjin(DianhuaWorkOrder wordOrder, String msg) {
        DianhuaHandleOrderParam param = new DianhuaHandleOrderParam();
        param.setCaseId(wordOrder.getCaseId());
        param.setOrderType(WorkOrderType.genjin.getType());
        param.setHandleResult(HandleResultEnum.exception_done.getType());
        param.setOperComment(msg);
        super.doHandle(param);
    }

    @Override
    public OpResult<Long> create(DianhuaWorkOrder wordOrder) {

        //电话跟进工单  延时生成
        if (WorkOrderType.genjin.getType() == wordOrder.getOrderType() && !wordOrder.isCreateNow()){
            GenjinModel genjinModel = new GenjinModel();
            genjinModel.setCaseId(wordOrder.getCaseId());
            genjinModel.setTime(LocalDateTime.now().toString());
            genjinModel.setOperation(GenjinModel.xiafa);
            genjinModel.setYiyuanHeshiId(wordOrder.getYiyuanHeshiId());
            genjinModel.setYiyuanHeshiTime(wordOrder.getYiyuanHeshiTime());
            dianhuaCreateMq.sendGenjinMq(genjinModel);
            //不保存信息
            wordOrder.setYiyuanHeshiTime("");
            wordOrder.setYiyuanHeshiId("");
            wordOrder.setOperComment("");
            return OpResult.createSucResult(0L);
        }
        if (WorkOrderType.heshi.getType() == wordOrder.getOrderType()){
            wordOrder.setOrderlevel(OrderLevel.edium.getType());
        }

        orderOperationFacade.createWorkOrder(wordOrder);

        return OpResult.createSucResult(wordOrder.getId());
    }

    @Override
    public OpResult handle(DianhuaHandleOrderParam param) {

        if (param.getHandleResult() == HandleResultEnum.audit_reject.getType()
            && param.getOrderType() == WorkOrderType.heshi.getType()){
            GenjinModel genjinModel = new GenjinModel();
            genjinModel.setCaseId(param.getCaseId());
            genjinModel.setTime(LocalDateTime.now().toString());
            genjinModel.setOperation(GenjinModel.bohui);
            genjinModel.setYiyuanHeshiId(param.getYiyuanHeshiId());
            genjinModel.setYiyuanHeshiTime(param.getYiyuanHeshiTime());

            dianhuaCreateMq.sendGenjinMqForReject(genjinModel);
        }

        //有工单id 直接按照工单处理
        if (param.getWorkOrderId() > 0){

            int result = workOrderDao.handle(param.getWorkOrderId(),param.getHandleResult());
            if (result > 0){
                return OpResult.createSucResult(result);
            }
        }

        //只有案例id  直接按照案例id直接处理
        if (param.getWorkOrderId() == 0 && param.getCaseId() > 0 && param.getHandleResult() != HandleResultEnum.later_doing.getType()){
            if (param.getOrderType() == 0){
                param.setOrderType(WorkOrderType.heshi.getType());
            }
            int result = workOrderDao.closeWorkOrderByCaseId(param.getCaseId(),Lists.newArrayList(param.getOrderType()),HandleResultEnum.exception_done.getType());
            return OpResult.createSucResult(result);
        }

        return OpResult.createFailResult(ErrorCode.SYSTEM_HANDLE_ERROR);

    }

    @Override
    public OpResult<PageResult<DianhuaWorkOrderVo>> getOrderList(DianhuaWorkOrderListParam param) {

        if (param.getUserId() == 0 || StringUtils.isEmpty(param.getHandleResult())){
            return OpResult.createFailResult(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        PageResult<DianhuaWorkOrderVo> pageResult = new PageResult<>();

        //多查询一次  判断是否有下一页
        int p = param.getPageSize()+1;

        int limit = param.getPage() * param.getPageSize();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).map(Integer::valueOf).collect(Collectors.toList());

        List<WorkOrderBaseVo> list = dianhuaWorkOrderDao.getWorkorderList(param.getUserId(),
                param.getOrderType(),p,limit,param.getCaseId(),param.getOrderLevel(),param.getStartTime(),
                param.getEndTime(),results);


        if (list.size() == p){
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size()-1);
        }

        //如果不存在列表
        if (CollectionUtils.isEmpty(list)){
            return OpResult.createSucResult(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(param.getPaging())){
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId)).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(WorkOrderBaseVo::getCaseId).distinct().collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        if (CollectionUtils.isEmpty(feignResponse)){
            return OpResult.createFailResult(ErrorCode.SYSTEM_CASE_ERROR);
        }

        Map<Integer,CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity()));

        Map<Integer, ChannelRefine.ChannelRefineResuleEnum> recordMap =baseWorkOrderService.getChannel(feignResponse);

        Map<Long,String> timeMap = getTimeMap(list);

        List<DianhuaWorkOrderVo> voList = list.stream().filter(r->{
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())){
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}",r.getCaseId());
            return false;

        }).map(r->{

            DianhuaWorkOrderVo workOrderVO = new DianhuaWorkOrderVo();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setOrderType(r.getOrderType());

            CrowdfundingInfo c = map.get(r.getCaseId());

            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUuid(c.getInfoId());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setHandleResult(r.getHandleResult());

            ChannelRefine.ChannelRefineResuleEnum resuleEnum = recordMap.get(r.getCaseId());
            if (resuleEnum != null){
                workOrderVO.setChannel(resuleEnum.getOuterTimeliness());
                workOrderVO.setChannelStr(resuleEnum.getChannelDesc());
            }

            workOrderVO.setCaseUserId(c.getUserId());
            workOrderVO.setHeshiTime(timeMap.get(workOrderVO.getWorkOrderId()));

            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);

        return OpResult.createSucResult(pageResult);
    }


    private Map<Long,String> getTimeMap(List<WorkOrderBaseVo> list){

        Map<Long,String> map = Maps.newHashMap();

        List<Long> workOrderId = list.stream().map(WorkOrderBaseVo::getId).distinct().collect(Collectors.toList());

        List<WorkOrderExt> exts = extService.getWorkOrderExts(workOrderId, OrderExtName.yiyuanHeshiTime.getName());

        if (CollectionUtils.isEmpty(exts)){
            return map;
        }

       return exts.stream().collect(Collectors.toMap(WorkOrderExt::getWorkOrderId,WorkOrderExt::getExtValue));

    }
}
