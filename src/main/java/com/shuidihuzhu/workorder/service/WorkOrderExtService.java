package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2021-04-22 20:51
 **/
public interface WorkOrderExtService {

    void addByList(List<WorkOrderExtVO> list);

    void markDeleteByName(long workOrderId, String name);

    void updateByNameValue(long workOrderId, String name, String value);

    WorkOrderExtVO getLastByName(long workOrderId, String name);

    List<WorkOrderExtVO> getListByName(long workOrderId, String name);

    List<WorkOrderExtVO> getListByNameAndIdList(List<Long> workOrderIdList, String name);

    WorkOrderExtVO addByNameValue(long workOrderId, String name, String value);

    List<WorkOrderExt> getWorkOrderExt(long workOrderId);

    List<WorkOrderExt> getWorkOrderExt(List<Long> ids);

    Map<String, String> getExtMapByOrderId(Long id);
}
