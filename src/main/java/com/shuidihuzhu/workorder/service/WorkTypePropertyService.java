package com.shuidihuzhu.workorder.service;

import com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty;

import java.util.List;

public interface WorkTypePropertyService {

    WorkTypeProperty.UpdatePropertyParam selectByPropertyList(int firstWorkOrder);

    void updateWorkOrderProperty(WorkTypeProperty.UpdatePropertyParam param);

    List<WorkTypeProperty> selectByPropertyTypes(int orderLevel, List<Integer> orderTypes, List<Integer> propertyType);

    int getMaxDelayCount(int orderType);

    int getSecondMaxDelayCount(int orderType, WorkTypeProperty.PropertyType propertyType);

    List<WorkTypeProperty.UpdatePropertyParam> selectByType(int orderLevel, List<Integer> orderTypes, List<Integer> propertyType);

    void updateSecondWorkOrderProperty(WorkTypeProperty.UpdatePropertyParam param);

}
