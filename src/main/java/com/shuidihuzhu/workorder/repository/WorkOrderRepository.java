package com.shuidihuzhu.workorder.repository;

import com.shuidihuzhu.workorder.dao.tdsql.TdWorkOrderDao;
import com.shuidihuzhu.workorder.model.order.VonDoingCountTypeVO;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:27
 */
@Service
@RefreshScope
public class WorkOrderRepository {

    @Autowired
    private TdWorkOrderDao tdWorkOrderDao;


    public int getAllWorkorderCount(long operatorId, List<Integer> orderType, int handleResult, long workOrderId,
                                    List<Integer> caseIds, String startCreateTime, String endCreateTime, String startHandleTime,
                                    String endHandleTime, String startDoneTime, String endDoneTime, int outerUser, long belonger) {
        return tdWorkOrderDao.getAllWorkorderCount(operatorId, orderType, handleResult, workOrderId, caseIds,
                startCreateTime, endCreateTime, startHandleTime, endHandleTime, startDoneTime, endDoneTime, outerUser, belonger);
    }

    public Long getMinIdQcByBatchV3(int orderType, Integer handleResult, String finishStartTime, String finishEndTime) {
        return tdWorkOrderDao.getMinIdQcByBatchV3(orderType, handleResult, finishStartTime, finishEndTime);
    }

    public List<WorkOrderDoingCount> staffHandleCount(int orderType, int handleResult) {
        return tdWorkOrderDao.staffHandleCount(orderType, handleResult);
    }

    public List<VonDoingCountTypeVO> getTodayTypeWithCountByUserAndHandleResult(long operatorId, List<Integer> orderTypes, List<Integer> handleResults) {
        return tdWorkOrderDao.getTodayTypeWithCountByUserAndHandleResult(operatorId, orderTypes, handleResults);
    }

}
