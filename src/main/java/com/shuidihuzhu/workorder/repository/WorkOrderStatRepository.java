package com.shuidihuzhu.workorder.repository;

import com.shuidihuzhu.workorder.dao.tdsql.TdWorkOrderStatDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/27  14:27
 */
@Service
@RefreshScope
public class WorkOrderStatRepository {

    @Autowired
    private TdWorkOrderStatDao tdWorkOrderStatDao;

    public int getResultAmount(List<Integer> types, List<Integer> result, String time, long userId) {
        return tdWorkOrderStatDao.getResultAmount(types, result, time, userId);
    }

}
