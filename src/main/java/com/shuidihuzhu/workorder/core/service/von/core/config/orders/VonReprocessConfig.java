package com.shuidihuzhu.workorder.core.service.von.core.config.orders;

import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@VonConfig("reprocess")
public class VonReprocessConfig implements IVonConfig {

    @ApiModelProperty("工单是否可以重新审核")
    private Boolean workOrderReprocess;

    @ApiModelProperty("工单的哪些审核结果可以重新审核")
    private String workOrderHandleResult;

    @Override
    public void promoteDefault(int orderType) {
        if (workOrderReprocess == null) {
            workOrderReprocess = false;
        }
    }
}
