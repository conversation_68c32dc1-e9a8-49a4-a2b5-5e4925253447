package com.shuidihuzhu.workorder.core.service.von.core.config.orders;

import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@VonConfig("handle")
public class VonHandleConfig implements IVonConfig {

    @ApiModelProperty("工单是否要本人处理")
    private Boolean workOrderSelfHandle;

    @Override
    public void promoteDefault(int orderType) {
        if (workOrderSelfHandle == null) {
            workOrderSelfHandle = true;
        }
    }
}
