package com.shuidihuzhu.workorder.core.service;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-06-15  13:41
 */
@Service
public class EnvService implements EnvironmentAware {

    private static final Profiles ENV_PRODUCTION = Profiles.of("production");

    @Setter
    @Getter
    private Environment environment;

    public boolean isDevelopment() {
        return !isProduction();
    }

    public boolean isProduction() {
        return environment.acceptsProfiles(ENV_PRODUCTION);
    }
}
