package com.shuidihuzhu.workorder.core.service.core.notice;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.delegate.sea.SeaAuthHelper;
import com.shuidihuzhu.workorder.core.service.core.notice.AssignNoticeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssignNoticeServiceImpl implements AssignNoticeService {

    @Autowired
    private WorkOrderDao workOrderDao;

    private Map<Integer, Notice> orderTypeNoticeMap = Maps.newHashMap();

    private static final String KEY_GROUP_CONFIG = "apollo.von.assign-notice.config";

    @PostConstruct
    public void init(){
        loadConfig();
        ConfigService.getAppConfig().addChangeListener(changeEvent -> loadConfig(), Sets.newHashSet(KEY_GROUP_CONFIG));
    }

    private void loadConfig() {
        String groupConfig = ConfigService.getAppConfig().getProperty(KEY_GROUP_CONFIG, "{}");
        NoticeConfig c = JSON.parseObject(groupConfig, NoticeConfig.class);
        if (c == null) {
            return ;
        }
        List<Notice> notice = c.getNotice();
        if (CollectionUtils.isEmpty(notice)) {
            return ;
        }
        orderTypeNoticeMap = notice.stream().collect(Collectors.toMap(Notice::getOrderType, Function.identity()));
    }

    @Async(WorkOrderConfig.Async.NOTICE)
    @Override
    public void onOrderAssign(WorkOrderVO v) {
        long workOrderId = v.getWorkOrderId();
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        int orderType = workOrder.getOrderType();

        final Notice notice = orderTypeNoticeMap.get(orderType);
        if (notice == null) {
            return;
        }

        send(notice, workOrder);
    }


    @Override
    public void send(INotice notice, WorkOrderBase workOrder) {
        final long workOrderId = workOrder.getId();
        final long operatorId = workOrder.getOperatorId();
        // 发送通知
        Response<AuthUserDto> userResp = SeaAuthHelper.delegate().getUserFeignClient().getAuthUserById(operatorId);
        AuthUserDto userInfo = userResp.getData();
        String userName = userInfo.getUserName();
        String loginName = userInfo.getLoginName();
        String nowTime = DateUtil.getCurrentDateTimeStr();

        String content = replaceVar(notice.getTemplate(), "caseId", workOrder.getCaseId());
        content = replaceVar(content, "orderId", workOrderId);
        content = replaceVar(content, "nowTime", nowTime);
        content = replaceVar(content, "orderCreateTime", DateUtil.formatDateTime(workOrder.getCreateTime()));
        content = replaceVar(content, "orderStatusMsg", HandleResultEnum.getFromType(workOrder.getHandleResult()).getShowMsg());
        content = replaceVar(content, "operatorName", userName);
        List<String> noticeUsers = Lists.newArrayList();
        noticeUsers.add(loginName);
        CollectionUtils.addAll(noticeUsers, notice.getAdditionNoticeUser());
        String[] mentionedList = noticeUsers.toArray(new String[0]);
        AlarmBotService.sentText(notice.getNoticeKey(), content, mentionedList, null);
        log.info("发送领单通知 operatorId {}, orderId {}, key {}, content {}, noticeUsers {}",
                operatorId, workOrderId, notice.getNoticeKey(), content, mentionedList);
    }

    @Nullable
    private String replaceVar(String template, String searchString, Object value) {
        return StringUtils.replace(template, "##" + searchString + "##", String.valueOf(value));
    }

    @Data
    public static class NoticeConfig {
        private List<Notice> notice;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class Notice extends BaseNotice{
        @ApiModelProperty("要发通知的工单类型")
        private int orderType;
    }
}
