package com.shuidihuzhu.workorder.core.service.core.impl;

import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonConst;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecord;
import com.shuidihuzhu.cf.client.ugc.wonrecord.WonRecordClient;
import com.shuidihuzhu.cf.client.ugc.wonrecord.model.WonExtUpdateByIdParam;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockHelper;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.StaffFreeStatService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.order.VonDoingCountTypeVO;
import com.shuidihuzhu.workorder.core.model.view.VonFreeCalcModel;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 员工每日空闲时长统计
 * 把won记录服务当成redis的k-v存储一样来使用,因为不需要那么高性能
 * 日key + value数据
 * 最好redis加锁
 * <AUTHOR>
 */
@Service
@Slf4j
public class StaffFreeStatServiceImpl implements StaffFreeStatService {

    private static final long ACTION_FREE = 107L;

    @Autowired
    private WonRecordClient wonRecordClient;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private OrderGroupService orderGroupService;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Override
    public Boolean isFree(long operatorId) {
        StaffStatus staffStatus = staffStatusService.getStaffStatus(operatorId, WorkOrderType.nei_shen_group.getType());
        if (staffStatus == null) {
            log.debug("非内审组 或需上班一次");
            return null;
        }
        List<Integer> groupList = orderGroupService.getGroupList();
        if (CollectionUtils.isEmpty(groupList)) {
            return null;
        }
        List<VonDoingCountTypeVO> doingCountTypeList = workOrderDao.getTypeWithCountByUserAndHandleResult(operatorId,
                groupList, HandleResultEnum.doing.getType());
        List<VonDoingCountTypeVO> laterDoingCountTypeList = workOrderDao.getTypeWithCountByUserAndHandleResult(operatorId,
                groupList, HandleResultEnum.later_doing.getType());
        boolean busy = CollectionUtils.isNotEmpty(doingCountTypeList) || CollectionUtils.isNotEmpty(laterDoingCountTypeList);
        return staffStatus.getStaffStatus() == StaffStatusEnum.online.getType()  && !busy;
    }

    @Override
    public void onOrderStatusChange(long workOrderId) {
        if (workOrderId <= 0) {
            log.warn("onOrderStatusChange workOrderId empty");
            return;
        }
        syncByWorkOrderId(workOrderId);
    }

    @Override
    public void syncByWorkOrderId(Long workOrderId) {
        WorkOrderBase order = workOrderDao.getWorkOrderById(workOrderId);
        long operatorId = order.getOperatorId();
        if (operatorId <= 0) {
            log.debug("工单刚创建");
            return;
        }
        syncByOperatorId(operatorId);
    }

    @Override
    public void syncByOperatorId(long operatorId) {
        String key = "von.staff-free." + operatorId;
        Response<Object> resp = RedisLockHelper.callWithLock(cf2RedissonHandler, () -> {
            syncByOperatorIdActual(operatorId);
            return null;
        }, key, 3000);
        log.debug("syncByOperatorId resp {}", resp);
    }

    private void syncByOperatorIdActual(long operatorId) {
        if (operatorId <= 0) {
            log.debug("用户id为空");
            return;
        }
        Boolean free = isFree(operatorId);
        if (free == null) {
            log.debug("非内审组 或需上班一次");
            return;
        }
        long now = System.currentTimeMillis();
        VonFreeCalcModel vonFreeCalcModel = getPreStat(operatorId);
        if (vonFreeCalcModel == null) {
            return;
        }

        // 状态无变化
        if (vonFreeCalcModel.isFree() == free){
            return;
        }

        // 状态有变化 更新状态
        vonFreeCalcModel.setFree(free);
        if (free) {
            // 空闲开始 记录空闲开始时间与状态
            vonFreeCalcModel.setFreeStartTime(now);
        } else {
            // 空闲结束 累加总计空闲时长
            long totalFreeDuration = vonFreeCalcModel.getTotalFreeDuration() + (now - vonFreeCalcModel.getFreeStartTime());
            vonFreeCalcModel.setTotalFreeDuration(totalFreeDuration);
        }
        saveStatCalc(operatorId, vonFreeCalcModel);
    }

    private void saveStatCalc(long operatorId, VonFreeCalcModel vonFreeCalcModel) {
        log.info("saveStatCalc operatorId {}, data {}", operatorId, vonFreeCalcModel);
        long recordId = vonFreeCalcModel.getRecordId();
        if (recordId <= 0) {
            wonRecordClient.create()
                    .buildBasic(getBizId(operatorId), ACTION_FREE)
                    .buildData(vonFreeCalcModel)
                    .save();
            log.info("首次保存 operatorId {}", operatorId);
            return;
        }
        WonExtUpdateByIdParam param = WonExtUpdateByIdParam.create(recordId, WonConst.KEY_DATA, vonFreeCalcModel);
        wonRecordClient.updateExtByRecordId(param);
    }

    /**
     * 默认非空闲
     * @param operatorId
     * @return
     */
    @Nullable
    private VonFreeCalcModel getPreStat(long operatorId) {
        String bizId = getBizId(operatorId);
        OperationResult<WonRecord> lastRecordResp = wonRecordClient.getLastByBizId(bizId, ACTION_FREE);
        if (lastRecordResp == null || lastRecordResp.isFail()) {
            return null;
        }
        WonRecord lastRecord = lastRecordResp.getData();
        if (lastRecord == null) {
            VonFreeCalcModel v = new VonFreeCalcModel();
            v.setFree(false);
            return v;
        }
        VonFreeCalcModel data = lastRecord.getData(VonFreeCalcModel.class);
        data.setRecordId(lastRecord.getId());
        return data;
    }

    @NotNull
    private String getBizId(long operatorId) {
        String currentDateStr = DateUtil.getCurrentDateStr();
        return ACTION_FREE + "-" + operatorId + "-" + currentDateStr;
    }

    @Override
    public long getTodayGroupFreeTime(long userId) {
        VonFreeCalcModel preStat = getPreStat(userId);
        if (preStat == null) {
            return 0;
        }

        // 查询实时空闲需要吧上次空闲到当前时间的加上
        long totalFreeDuration = preStat.getTotalFreeDuration();
        if (preStat.isFree()) {
            totalFreeDuration += System.currentTimeMillis() - preStat.getFreeStartTime();
        }
        return totalFreeDuration / 1000L;
    }
}
