package com.shuidihuzhu.workorder.core.service.von.core.config.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeConstants;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.EnvService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.dao.VonOrderConfigDAO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigService;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.*;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.VonConfigKey;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 若有需代码处理的则 在写时做逻辑
 * 拉直接取
 */
@Slf4j
@RefreshScope
@Service
public class VonConfigFacadeImpl implements VonConfigFacade, InitializingBean, ApplicationContextAware {

    @Setter
    private ApplicationContext applicationContext;

    private Collection<Class<? extends IVonConfig>> configs = Lists.newArrayList(
            VonAssignConfig.class
            , VonAutoFreeConfig.class
            , VonHandleConfig.class
            , VonReprocessConfig.class
    );

    @Autowired
    private VonConfigService vonConfigService;

    @Autowired
    private VonOrderConfigDAO vonOrderConfigDAO;

    @Autowired
    private EnvService envService;

    @Value("${apollo.von.config.default:{}}")
    private String defaultConfigJson;

    @Resource(name = WorkOrderConfig.Async.LocalCache)
    private Executor defaultExecutor;

    private LoadingCache<Integer, String> orderTypeConfigCache;

    @PostConstruct
    public void init() {
//        orderTypeConfigCache = CacheBuilder.newBuilder()
//                .refreshAfterWrite(10, TimeUnit.SECONDS)
//                .build(CacheLoader.asyncReloading(CacheLoader.from(this::loadValue), defaultExecutor));

        for (WorkOrderType orderType : WorkOrderType.values()) {
            doneListMap.put(orderType.getType(), getDoneList(orderType.getType()));
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Data
    public static class TempProcessVO {
        VonAssignConfig assign;
        VonHandleConfig handle;
        VonReprocessConfig reprocess;
        VonAutoFreeConfig autoFree;

    }

    public static void main(String[] args) {
        final Field[] declaredFields = VonAssignConfig.class.getDeclaredFields();
        final List<String> list = Arrays.stream(declaredFields).map(v -> v.getName()).collect(Collectors.toList());
        System.out.println("JSON.toJSONString(list) = " + JSON.toJSONString(list));
        System.out.println("JSON.toJSONString(declaredFields) = " + JSON.toJSONString(declaredFields));

    }

    @Override
    public Response<Void> process() {
        for (WorkOrderType orderType : WorkOrderType.values()) {
            final String orderConfigJson = vonOrderConfigDAO.getByOrderType(orderType.getType());
            final TempProcessVO tempProcessVO = JSON.parseObject(orderConfigJson, TempProcessVO.class);
            if (tempProcessVO == null) {
                continue;
            }
            processByClass(orderType.getType(), tempProcessVO.getAssign(), VonAssignConfig.class);
            processByClass(orderType.getType(), tempProcessVO.getHandle(), VonHandleConfig.class);
            processByClass(orderType.getType(), tempProcessVO.getReprocess(), VonReprocessConfig.class);
            processByClass(orderType.getType(), tempProcessVO.getAutoFree(), VonAutoFreeConfig.class);
        }

        return NewResponseUtil.makeSuccess();
    }

    private void processByClass(int orderType, Object config, Class clazz) {
        if (config == null) {
            return;
        }
        final Field[] declaredFields = clazz.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            final String name = declaredField.getName();
            Object property = null;
            try {
                property = PropertyUtils.getProperty(config, name);
            } catch (Exception e) {
                log.error("读取错误", e);
            }
            tempSave(orderType, "assign." + name, property);
        }
    }

    private void tempSave(int orderType, String key, Object value) {
        if (value == null) {
            return;
        }
        saveConfig(orderType, key, String.valueOf(value));
    }

    @Override
    public String getProperty(int orderType, String key) {
        return vonConfigService.getProperty(String.valueOf(orderType), key);
    }

    @Override
    public Boolean getBooleanProperty(int orderType, String key) {
        return vonConfigService.getBooleanProperty(String.valueOf(orderType), key);
    }

    @Override
    public Integer getIntegerProperty(int orderType, String key) {
        return vonConfigService.getIntProperty(String.valueOf(orderType), key);
    }

    @Override
    public VonAssignConfig getAssignConfig(int orderType) {
        final VonAssignConfig v = new VonAssignConfig();
        final Boolean isUseOldAssigner = getBooleanProperty(orderType, "assign.isUseOldAssigner");
        v.setIsUseOldAssigner(isUseOldAssigner);
        final Boolean notAssignDoingOperator = getBooleanProperty(orderType, "assign.notAssignDoingOperator");
        v.setNotAssignDoingOperator(notAssignDoingOperator);
        final Boolean priorAssignLastAssign = getBooleanProperty(orderType, "assign.priorAssignLastAssign");
        v.setPriorAssignLastAssign(priorAssignLastAssign);
        final Boolean autoAssign = getBooleanProperty(orderType, "assign.autoAssign");
        v.setAutoAssign(autoAssign);
        final Boolean priorAssignerEnable = getBooleanProperty(orderType, "assign.priorAssignerEnable");
        v.setPriorAssignerEnable(priorAssignerEnable);
        final Boolean downWhenGroupNoOnlineUser = getBooleanProperty(orderType, "assign.downWhenGroupNoOnlineUser");
        v.setDownWhenGroupNoOnlineUser(downWhenGroupNoOnlineUser);

        final Integer assignLimitCount = getIntegerProperty(orderType, "assign.assignLimitCount");
        v.setAssignLimitCount(assignLimitCount);
        final Integer assignCountPearOperator = getIntegerProperty(orderType, "assign.assignCountPearOperator");
        v.setAssignCountPearOperator(assignCountPearOperator);
        final Integer minAssignOrderCount = getIntegerProperty(orderType, "assign.minAssignOrderCount");
        v.setMinAssignOrderCount(minAssignOrderCount);

        final Boolean dayAssignLimitEnable = getBooleanProperty(orderType, "assign.dayAssignLimitEnable");
        v.setDayAssignLimitEnable(dayAssignLimitEnable);

        Boolean groupAssignCheckOrderPermission = getBooleanProperty(orderType, "assign.groupAssignCheckOrderPermission");
        v.setGroupAssignCheckOrderPermission(groupAssignCheckOrderPermission);

        /**
         * 高风险工单配置，除了这些，其他的都是默认值
         config_key                     config_value
         assign.isUseOldAssigner	    false
         assign.assignLimitCount	    1
         assign.priorAssignLastAssign	true
         assign.workOrderReprocess   	true
         assign.workOrderReminder	    true
         下面使用默认值：
         notAssignDoingOperator = false
         assignCountPearOperator = 1
         autoAssign = true
         priorAssignerEnable = false
         downWhenGroupNoOnlineUser = false
         dayAssignLimitEnable = false
         groupAssignCheckOrderPermission = true
         */
        /**
         * 默认配置
         *
         * isUseOldAssigner：如果 orderType <= 65 && WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType) 为 true isUseOldAssigner = true
         *
         * assignLimitCount = 5
         *
         * notAssignDoingOperator = false
         *
         * assignCountPearOperator = 1
         *
         * priorAssignLastAssign = false
         *
         * autoAssign = true
         *
         * priorAssignerEnable = false
         *
         * downWhenGroupNoOnlineUser = false
         *
         * dayAssignLimitEnable = false
         *
         * groupAssignCheckOrderPermission = true
         */
        v.promoteDefault(orderType);
        return v;
    }

    @Override
    public VonHandleConfig getHandleConfig(int orderType) {
        final VonHandleConfig v = new VonHandleConfig();
        final Boolean workOrderSelfHandle = getBooleanProperty(orderType, "assign.workOrderSelfHandle");
        v.setWorkOrderSelfHandle(workOrderSelfHandle);
        v.promoteDefault(orderType);
        return v;
    }

    @Override
    public VonAutoFreeConfig getAutoFreeConfig(int orderType) {
        final VonAutoFreeConfig v = new VonAutoFreeConfig();
        final Boolean enableLaterDoAutoFree = getBooleanProperty(orderType, "assign.enableLaterDoAutoFree");
        v.setEnableLaterDoAutoFree(enableLaterDoAutoFree);
        final Boolean enableDoingAutoFree = getBooleanProperty(orderType, "assign.enableDoingAutoFree");
        v.setEnableDoingAutoFree(enableDoingAutoFree);

        v.promoteDefault(orderType);
        return v;
    }

    @Override
    public VonReprocessConfig getReprocessConfig(int orderType) {
        final VonReprocessConfig v = new VonReprocessConfig();
        final Boolean workOrderReprocess = getBooleanProperty(orderType, "assign.workOrderReprocess");
        v.setWorkOrderReprocess(workOrderReprocess);
        final String workOrderHandleResult = getProperty(orderType, "assign.workOrderHandleResult");
        v.setWorkOrderHandleResult(workOrderHandleResult);

        v.promoteDefault(orderType);
        return v;
    }

    @Override
    public VonReminderConfig getReminderConfig(int orderType) {
        final VonReminderConfig v = new VonReminderConfig();
        final Boolean workOrderReprocess = getBooleanProperty(orderType, VonConfigKey.WORK_ORDER_REMINDER.getConfigKey());
        v.setWorkOrderReminder(workOrderReprocess);
        v.promoteDefault(orderType);
        return v;
    }

    @NotNull
    private <T extends IVonConfig> String getNameByClass(Class<T> clazz) {
        boolean annotationPresent = clazz.isAnnotationPresent(VonConfig.class);
        if (!annotationPresent) {
            throw new RuntimeException("配置代码不正确 需打VonPlugin注解 class " + clazz.getSimpleName());
        }
        VonConfig vonPlugin = clazz.getDeclaredAnnotation(VonConfig.class);
        return vonPlugin.value();
    }

    @Override
    public List<VonConfigDO> getAllByOrderType(int orderType) {
        return vonConfigService.getAllByNamespace(orderType + "");
    }

    @Override
    public void saveConfig(int orderType, String key, String value) {
        vonConfigService.save("" + orderType, key, value);
    }

    @Override
    public void saveByMap(int orderType, Map<String, String> configMap) {
        if (MapUtils.isEmpty(configMap)) {
            return;
        }
        for (String key : configMap.keySet()) {
            saveConfig(orderType, key, configMap.get(key));
        }
    }

    @Override
    public void saveByList(List<VonConfigDO> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        for (VonConfigDO configDO : configList) {
            vonConfigService.save(configDO.getNamespace(), configDO.getConfigKey(), configDO.getConfigValue());
        }
    }

    private Map<Integer, List<Integer>> doneListMap = Maps.newHashMap();

    @Override
    public Response<Map<Integer, List<Integer>>> getDoneListConfig() {
        return NewResponseUtil.makeSuccess(doneListMap);
    }

    @Override
    public Map<Integer, List<Integer>> getDoneListConfigMap() {
        return doneListMap;
    }

    // TODO 拆分完成字段
    @Override
    public List<Integer> getAllDoneHandleResultList() {
        HashSet<Integer> unDoSet = Sets.newHashSet(HandleResultEnum.unDoResult());
        return Arrays.stream(HandleResultEnum.values())
                .map(HandleResultEnum::getType)
                .filter(v -> !unDoSet.contains(v))
                .collect(Collectors.toList());
    }

    /**
     * 获取已完成的状态列表
     *
     * @param orderType
     * @return
     */
    private List<Integer> getDoneList(int orderType) {

        final String doneListStr = getProperty(orderType, "handleResultMapper.doneList");
        if (StringUtils.isNotBlank(doneListStr)) {
            return Splitter.on(",").splitToList(doneListStr).stream().map(Integer::parseInt).collect(Collectors.toList());
        }

        List<Integer> list = Lists.newArrayList();

        switch (orderType) {
            case WorkOrderTypeConstants.YANHOU:
            case WorkOrderTypeConstants.CAILIAO_0:
            case WorkOrderTypeConstants.CAILIAO_1:
            case WorkOrderTypeConstants.CAILIAO_3:
            case WorkOrderTypeConstants.CAILIAO_4:
            case WorkOrderTypeConstants.CAILIAO_5:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.YILEI:
            case WorkOrderTypeConstants.ERLEI:
            case WorkOrderTypeConstants.SANLEI:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                break;
            case WorkOrderTypeConstants.BOHUI:
            case WorkOrderTypeConstants.BU_CHONG_YI_YUAN_XIN_XI:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                break;
            case WorkOrderTypeConstants.CAILIAO_ZHU_DONG_FU_WU:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.SHENHE:
            case WorkOrderTypeConstants.DIANHUA_SHENHE:
            case WorkOrderTypeConstants.YILIAO_SHENHE:
            case WorkOrderTypeConstants.HIGH_RISK_SHENHE:
            case WorkOrderTypeConstants.TARGET_AMOUNT_REASONABLE_AUDIT:
            case WorkOrderTypeConstants.AI_ERCI:
                list.add(HandleResultEnum.return_call.getType());
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.smart_audit_pass.getType());
                break;
            case WorkOrderTypeConstants.HUIFANG:
            case WorkOrderTypeConstants.XIAFA_PROGRESS:
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.HESHI:
                list.add(HandleResultEnum.cancel.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.GENJIN:
                list.add(HandleResultEnum.cancel.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.done.getType());
                break;
            case WorkOrderTypeConstants.UGC_PINGLUN:
                list.add(HandleResultEnum.pass_show.getType());
                list.add(HandleResultEnum.only_self.getType());
                break;
            case WorkOrderTypeConstants.UGC_PROGRESS:
                list.add(HandleResultEnum.pass_show.getType());
                list.add(HandleResultEnum.modify.getType());
                list.add(HandleResultEnum.delete.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.WENJUAN:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.FUND_USE_RISK:
            case WorkOrderTypeConstants.FUND_USE_SHENHE:
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.report_intervene.getType());
                break;
            case WorkOrderTypeConstants.CASE_FIRST_REPORT:
            case WorkOrderTypeConstants.CASE_HISTORY_REPORT:
            case WorkOrderTypeConstants.UP_GRADE_SECOND:
            case WorkOrderTypeConstants.LOST_REPORT:
                list.add(HandleResultEnum.noneed_deal.getType());
                list.add(HandleResultEnum.end_deal.getType());
                list.add(HandleResultEnum.end_deal_upgrade.getType());
                list.add(HandleResultEnum.end_deal_lost.getType());
                break;
            case WorkOrderTypeConstants.ZENGXIN_NORMAL:
            case WorkOrderTypeConstants.ZENGXIN_RISK:
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.D0_1V1:
            case WorkOrderTypeConstants.D0_1V1_1:
            case WorkOrderTypeConstants.D1_1V1:
            case WorkOrderTypeConstants.D2_1V1:
            case WorkOrderTypeConstants.D0_1V1_TZB:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.exception_done.getType());
                break;
            case WorkOrderTypeConstants.QC_COMMON:
            case WorkOrderTypeConstants.QC_COMPLAINT:
            case WorkOrderTypeConstants.QC_WX_1V1:
            case WorkOrderTypeConstants.QC_CALL:
            case WorkOrderTypeConstants.QC_MATERIAL_AUDIT:
            case WorkOrderTypeConstants.QC_SECOND_COMPLAINT:
            case WorkOrderTypeConstants.QC_SERIOUS_COMPLAINT:
            case WorkOrderTypeConstants.QC_HOSPITAL_DEPT:
            case WorkOrderTypeConstants.QC_ZHU_DONG:
            case WorkOrderTypeConstants.RISK_LABEL_MARK:
                list.add(HandleResultEnum.done.getType());
                break;
            case WorkOrderTypeConstants.QC_WX_1V1_REPEAT:
            case WorkOrderTypeConstants.QC_COMMON_REPEAT:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.CONTENT:
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.CAILIAO_FUWU:
                list.add(HandleResultEnum.manual_lock.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.handle_manual_lock.getType());
                list.add(HandleResultEnum.done.getType());
                break;
            case WorkOrderTypeConstants.AI_PHOTO:
            case WorkOrderTypeConstants.AI_CONTENT:
                list.add(HandleResultEnum.stop_case.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.manual_lock.getType());
                break;
            case WorkOrderTypeConstants.CONSULTANT_EVALUATION:
                list.add(HandleResultEnum.pass_show.getType());
                list.add(HandleResultEnum.only_self.getType());
                break;
            case WorkOrderTypeConstants.PROMOTE_RAISER_UPLOAD_BILL:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.exception_done.getType());
                break;
            case WorkOrderTypeConstants.UGC_COMPLAINT_VERIFY:
                list.add(HandleResultEnum.pass_show.getType());
                list.add(HandleResultEnum.only_self.getType());
                break;
            case WorkOrderTypeConstants.FXJ_PUBLICITY_MSG_AUDIT:
            case WorkOrderTypeConstants.FXJ_COMMENT_MSG_AUDIT:
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.PICTURE_PUBLICITY_REVIEW:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.exception_done.getType());
                list.add(HandleResultEnum.overdue_auto_close.getType());
                break;
            case WorkOrderTypeConstants.QC_HIGH_RISK_QUALITY_INSPECTION:
                list.add(HandleResultEnum.done.getType());
                list.add(HandleResultEnum.overdue_auto_close.getType());
                break;
            case WorkOrderTypeConstants.REPORT_INSTEAD_INPUT:
            case WorkOrderTypeConstants.REPORT_SPLIT_DRAW:
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                break;
            case WorkOrderTypeConstants.FUND_USE_AMOUNT_REASONABLE:
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.not_to_c_show.getType());
                break;
            case WorkOrderTypeConstants.SERVICE_CHARGE_SUBSIDY:
                list.add(HandleResultEnum.audit_pass.getType());
                list.add(HandleResultEnum.audit_reject.getType());
                list.add(HandleResultEnum.exception_done.getType());
                break;
        }

        if (WorkOrderType.DELAY_FINANCE_WORK_ORDER_LIST.contains(orderType)) {
            list.add(HandleResultEnum.done.getType());
        }

        return list;
    }

}
