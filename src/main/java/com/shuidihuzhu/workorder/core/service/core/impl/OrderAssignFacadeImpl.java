package com.shuidihuzhu.workorder.core.service.core.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockHelper;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeConstants;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.service.AssignateWorkOrderFacade;
import com.shuidihuzhu.workorder.service.IAssignService;
import com.shuidihuzhu.workorder.service.assignate.BaseAssignService;
import com.shuidihuzhu.workorder.service.assignate.DefaultAssignService;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderAssignFacadeImpl implements OrderAssignFacade, InitializingBean, ApplicationContextAware {

    @Resource(name = "reportWorkOrderAssignateImpl")
    private AssignateWorkOrderFacade reportWorkOrderAssignateImpl;

    @Resource(name = "juanzhuanWorkOrderAssignate")
    private AssignateWorkOrderFacade juanzhuanWorkOrderAssignate;

    @Resource(name = "qcWorkOrderAssignateImpl")
    private AssignateWorkOrderFacade qcWorkOrderAssignateImpl;

    @Autowired(required = false)
    private Producer producer;

    @Resource(name = "caiLiaoWorkOrderAssignate")
    private AssignateWorkOrderFacade caiLiaoWorkOrderAssignate;

    @Resource(name = "prFirstAuditWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prFirstAuditWorkOrderAssign;
    @Resource(name = "prSecondLineAuditWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prSecondLineAuditWorkOrderAssign;
    @Resource(name = "prSecondAuditWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prSecondAuditWorkOrderAssign;
    @Resource(name = "prSupplementWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prSupplementWorkOrderAssign;
    @Resource(name = "prNotOnlineWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prReturnVisitWorkOrderAssign;
    @Resource(name = "prOnlineServiceWorkOrderAssignImpl")
    private AssignateWorkOrderFacade prOnlineServiceWorkOrderAssign;
    @Resource(name = "prSubmitAuditWorkOrderAssign")
    private AssignateWorkOrderFacade prSubmitAuditWorkOrderAssign;
    @Resource(name = "prSubmitAuditRejectWorkOrderAssign")
    private AssignateWorkOrderFacade prSubmitAuditRejectWorkOrderAssign;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Autowired
    private DefaultAssignService defaultAssignService;

    @Autowired
    private OrderGroupService orderGroupService;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Setter
    private ApplicationContext applicationContext;

    private final Map<Integer, BaseAssignService> typeAssignServiceMap = Maps.newHashMap();

    /**
     * 线上工单自动分配逻辑
     */
    @Override
    public void onAssignEvent(int orderTypeCode) {
        // 根据工单类型去 shuidi_cf_admin.work_order_type 表去查询工单类型信息
//        select *
//        from work_order_type
//        where
//        `order_type_code` = ?
//        and
//        `is_delete` = 0
        WorkOrderTypeDO workOrderType = workOrderTypeService.getFromOrderTypeCode(orderTypeCode);
        // 工单类型不存在直接返回
        if (workOrderType == null) {
            return;
        }

        //质检工单异步去处理分配事件
        if (WorkOrderType.QC_WORK_ORDER_LIST.contains(orderTypeCode)) {
            MessageResult messageResultNew = producer.send(new Message<>(MQTopicCons.CF, MQTag.QC_WORK_ORDER_ASSIGN_EVENT_V2,
                    MQTag.QC_WORK_ORDER_ASSIGN_EVENT_V2 + "_" + System.currentTimeMillis(), orderTypeCode, DelayLevel.S1));
            log.info("AssignateWorkOrderListener qc send assign, orderType:{}, messageResult:{}", orderTypeCode, JSON.toJSONString(messageResultNew));
            return;
        }

        //招募工单异步去处理分配事件
        if (WorkOrderType.PR_WORK_ORDER_LIST.contains(orderTypeCode)) {
            MessageResult messageResultNew = producer.send(new Message<>(MQTopicCons.CF, MQTag.PR_WORK_ORDER_ASSIGN_EVENT_V2,
                    MQTag.PR_WORK_ORDER_ASSIGN_EVENT_V2 + "_" + System.currentTimeMillis(), orderTypeCode, DelayLevel.S1));
            log.info("AssignateWorkOrderListener pr send assign, orderType:{}, messageResult:{}", orderTypeCode, JSON.toJSONString(messageResultNew));
            return;
        }

        // 其他类型工单
        Response<Void> resp = doAssignate(orderTypeCode);
        log.info("AssignateWorkOrderListener workOrderType:{},result:{}", orderTypeCode, resp);
    }

    /**
     * 工单分配，核心逻辑
     */
    @Override
    public Response<Void> doAssignate(int orderType) {
        /*
        组内工单触发不分配
        组类型的工单触发才分配组
        非组内正常走逻辑
         */

        /**
         * 这些工单是组工单
         * WorkOrderType.highriskshenhe.getType(),
         * WorkOrderType.ai_erci.getType(),
         * WorkOrderType.ai_content.getType(),
         * WorkOrderType.ai_photo.getType(),
         * WorkOrderType.content.getType(),
         * WorkOrderType.ugcprogress.getType(),
         * WorkOrderType.ugcpinglun.getType(),
         * WorkOrderType.ugc_complaint_verify.getType(),
         * WorkOrderType.consultant_evaluation.getType()
         */
        boolean inGroup = checkUseGroup(orderType);
        // 组类型工单分配
        if (inGroup) {
            MaliMQComponent.builder()
                    .setTags(MQTag.VON_ORDER_ASSIGN_GROUP)
                    .setPayload("assign")
                    .send();
            log.debug("group assign");
            return NewResponseUtil.makeSuccess(null);
        }
        // 内审组工单分配逻辑，走的也是组类型工单分配逻辑
        if (orderType == WorkOrderType.nei_shen_group.getType()) {
            MaliMQComponent.builder()
                    .setTags(MQTag.VON_ORDER_ASSIGN_GROUP)
                    .setPayload("assign")
                    .send();
            log.debug("group assign");
            return NewResponseUtil.makeSuccess(null);
        }

        // 非组分配走老逻辑
        // 对每种类型的工单，都路由到指定的 IAssignService实现类 然后调用 doAssign 方法
        // config.getIsUseOldAssigner(使用老版工单分配) 为true，就走 AssignateWorkOrderFacade 的 doAssign 方法
        // 否则就走 BaseAssignService 这个抽象类的 doAssign 方法
        return route(orderType).doAssign(orderType);
    }

    @Override
    public Response<Void> doAssignGroupWrap() {
        Response<Response<Void>> lockResp = RedisLockHelper.callWithLock(cf2RedissonHandler,
                this::doAssignGroup, "order-assign-group-lock", 0);
        if (lockResp.notOk()) {
            return NewResponseUtil.makeSuccess(null);
        }
        return lockResp.getData();
    }

    /**
     * 检查是否使用组分配
     */
    private boolean checkUseGroup(int orderType) {
        return getGroupSet().contains(orderType);
    }

    private Set<Integer> getGroupSet() {
        return orderGroupService.getGroupSet();
    }

    private List<Integer> getGroupList() {
        return orderGroupService.getGroupList();
    }

    /**
     * 组类型工单分配逻辑
     */
    private Response<Void> doAssignGroup() {
        StopWatch stopWatch = StopWatch.createStarted();
        // 清空上次分配中过滤的用户
        orderGroupService.cleanFilterUser();
        log.info("group assign start");
        List<Integer> types = getGroupList();
        /**
         * 遍历下面所有的组
         *                 WorkOrderType.highriskshenhe.getType(),
         *                 WorkOrderType.ai_erci.getType(),
         *                 WorkOrderType.ai_content.getType(),
         *                 WorkOrderType.ai_photo.getType(),
         *                 WorkOrderType.content.getType(),
         *                 WorkOrderType.ugcprogress.getType(),
         *                 WorkOrderType.ugcpinglun.getType(),
         *                 WorkOrderType.ugc_complaint_verify.getType(),
         *                 WorkOrderType.consultant_evaluation.getType()
         *
         */
        for (Integer type : types) {
            // 对每种类型的工单，都路由到指定的 IAssignService实现类 然后调用 doAssign 方法
            // config.getIsUseOldAssigner(使用老版工单分配) 为true，就走 AssignateWorkOrderFacade 的 doAssign 方法
            // 否则就走 BaseAssignService 这个抽象类的 doAssign 方法
            route(type).doAssign(type);
        }
        stopWatch.stop();
        log.info("group assign end time {}", stopWatch.getTime());
        return NewResponseUtil.makeSuccess(null);
    }

    /**
     * 默认增加默认分配规则
     *
     * @param orderType
     * @return
     */
    private IAssignService route(int orderType) {

        // 去 von_config 表中根据 orderType 以及 key去查询该工单的一些配置，如果没有配置，则使用默认配置
//        select *
//        from von_config
//        where namespace = orderType
//        and config_key = ？
        /**
         * 默认配置
         *
         * isUseOldAssigner：如果 orderType <= 65 && WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType) 为 true isUseOldAssigner = true
         *
         * assignLimitCount = 5
         *
         * notAssignDoingOperator = false
         *
         * assignCountPearOperator = 1
         *
         * priorAssignLastAssign = false
         *
         * autoAssign = true
         *
         * priorAssignerEnable = false
         *
         * downWhenGroupNoOnlineUser = false
         *
         * dayAssignLimitEnable = false
         *
         * groupAssignCheckOrderPermission = true
         */
        VonAssignConfig config = vonConfigFacade.getAssignConfig(orderType);
        Boolean useOld = config.getIsUseOldAssigner();
        // 使用老版工单分配
        if (useOld) {
            // 根据 工单类型，返回不同的工单分配实现类，使用的是简单工厂模式
            // 返回的是 AssignateWorkOrderFacade 的实现类，也就是调用 AssignateWorkOrderFacade 的 doAssign 方法
            return getByOld(orderType);
        }

        // 新版工单分配，如果map中没有指定类型，就返回defaultAssignService，但是都是走 BaseAssignService 这个抽象类的 doAssign 方法
        return typeAssignServiceMap.getOrDefault(orderType, defaultAssignService);
    }

    private IAssignService getByOld(int orderType) {

        // 如果是质检工单，并且不是医院科室质检工单，就返回 qcWorkOrderAssignateImpl
        if (WorkOrderType.QC_WORK_ORDER_LIST.contains(orderType) && orderType != WorkOrderType.qc_hospital_dept.getType()) {
            return qcWorkOrderAssignateImpl;
        }
        /**
         * 根据orderType，查询工单类型数据
         *         select *
         *         from work_order_type
         *         where
         *         `order_type_code` = #{orderType}
         *         and
         *         `is_delete` = 0
         */
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if (workOrderTypeDO == null) {
            log.info("workOrderTypeDO is null, orderType:{}", orderType);
            return null;
        }
        // 根据不同的工单类型，返回不同的工单分配实现类
        switch (workOrderTypeDO.getOrderTypeCode()) {
            case WorkOrderTypeConstants.CASE_FIRST_REPORT:
            case WorkOrderTypeConstants.CASE_HISTORY_REPORT:
            case WorkOrderTypeConstants.UP_GRADE_SECOND:
            case WorkOrderTypeConstants.LOST_REPORT:
                return reportWorkOrderAssignateImpl;
            case WorkOrderTypeConstants.D0_1V1:
            case WorkOrderTypeConstants.D0_1V1_1:
            case WorkOrderTypeConstants.D1_1V1:
            case WorkOrderTypeConstants.D2_1V1:
            case WorkOrderTypeConstants.D0_1V1_TZB:
                return juanzhuanWorkOrderAssignate;
            case WorkOrderTypeConstants.QC_COMMON:
            case WorkOrderTypeConstants.QC_WX_1V1:
                return qcWorkOrderAssignateImpl;
            case WorkOrderTypeConstants.CAILIAO_FUWU:
                return caiLiaoWorkOrderAssignate;
            case WorkOrderTypeConstants.PR_SUPPLEMENT_MATERIAL:
                return prSupplementWorkOrderAssign;
            case WorkOrderTypeConstants.PR_FIRST_SCREEN:
                return prFirstAuditWorkOrderAssign;
            case WorkOrderTypeConstants.PR_2LINE_FIRST_SCREEN_SERVICE:
                return prSecondLineAuditWorkOrderAssign;
            case WorkOrderTypeConstants.PR_SECOND_SCREEN:
                return prSecondAuditWorkOrderAssign;
            case WorkOrderTypeConstants.PR_RETURN_VISIT_SERVICE:
            case WorkOrderTypeConstants.PR_FIRST_SCREEN_REJECT_SERVICE:
            case WorkOrderTypeConstants.PR_SUPPLEMENT_MATERIAL_SERVICE:
                return prReturnVisitWorkOrderAssign;
            case WorkOrderTypeConstants.PR_ONLINE_SERVICE:
                return prOnlineServiceWorkOrderAssign;
            case WorkOrderTypeConstants.PR_SUBMIT_AUDIT:
                return prSubmitAuditWorkOrderAssign;
            case WorkOrderTypeConstants.PR_SUBMIT_AUDIT_REJECT:
                return prSubmitAuditRejectWorkOrderAssign;
            default:
                return defaultAssignService;
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 获取所有实现（或继承）BaseAssignService 接口（或类）的 Bean
        Map<String, BaseAssignService> beansOfTypeMap = applicationContext.getBeansOfType(BaseAssignService.class);
        if (MapUtils.isEmpty(beansOfTypeMap)) {
            return;
        }
        beansOfTypeMap.values()
                .forEach(v -> {
                    List<Integer> types = v.getOrderTypeCodeList();
                    if (CollectionUtils.isNotEmpty(types)) {
                        for (Integer t : types) {
                            // map 下面的几个工单
                            // qc_high_risk_quality_inspection(75, "高风险质检工单", "qc:qc_internal_audit", true) QcHighRiskOrderAssignImpl

                            // report_instead_input(76, "代录入审核工单", "report:report_instead_input", false) ReportInsteadInputOrderAssignImpl
                            // report_split_draw(77, "分批票据审核工单", "report:report_split_draw", false) ReportInsteadInputOrderAssignImpl

                            // xiafaprogress(25, "下发动态", "ugcprogress:xiafaprogress", true) XiaFaOrderAssignImpl

                            // cailiao_fuwu(47, "材料服务工单", "cailiaoshenhe:fuwu", false) ZhuDongOrderAssignImpl
                            typeAssignServiceMap.put(t, v);
                        }
                    }
                });
    }
}
