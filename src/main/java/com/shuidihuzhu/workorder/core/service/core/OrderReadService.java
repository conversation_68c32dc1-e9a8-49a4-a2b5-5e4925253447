package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderReadService {

    Response<PaginationListVO<BasicWorkOrder>> search(OrderSearchParam param);

    Response<List<BasicWorkOrder>> getListByOrderIdList(List<Long> workOrderIdList);

    Response<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param);
}
