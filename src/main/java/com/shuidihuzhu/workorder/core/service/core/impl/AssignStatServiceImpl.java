package com.shuidihuzhu.workorder.core.service.core.impl;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.workorder.core.service.core.AssignStatService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssignStatServiceImpl implements AssignStatService {

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Resource
    private VonConfigFacade vonConfigFacade;

    @Resource
    private WorkOrderDao workOrderDao;

    @Override
    public int getCurrentDayAssignCount(int orderType, long userId) {
        final String key = getKey(orderType, userId);
        final Integer c = cf2RedissonHandler.get(key, Integer.class);
        return c == null ? 0 : c;
    }

    private void onOrderAssign(long orderId, int orderType, long operatorId) {
        final VonAssignConfig assignConfig = vonConfigFacade.getAssignConfig(orderType);
        if (!assignConfig.getDayAssignLimitEnable()) {
            return;
        }
        String key = getKey(orderType, operatorId);
        final long count = cf2RedissonHandler.incr(key, TimeUnit.DAYS.toMillis(1));
        log.debug("领单统计自增 orderType {}, key {}", key, count);
    }

    @Override
    public void onOrderAssign(List<Long> orderIds, int orderType, Long operatorId) {
        for (Long orderId : orderIds) {
            onOrderAssign(orderId, orderType, operatorId);
        }
    }

    private String getKey(int orderType, long operatorId) {
        return DateUtil.getCurrentDateStr() + "-" + orderType + "-" + operatorId;
    }

}
