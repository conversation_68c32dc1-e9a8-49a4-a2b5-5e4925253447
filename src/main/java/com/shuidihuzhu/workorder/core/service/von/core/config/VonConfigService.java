package com.shuidihuzhu.workorder.core.service.von.core.config;

import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface VonConfigService {

    String getProperty(String namespace, String key);

    Boolean getBooleanProperty(String namespace, String key);

    Integer getIntProperty(String namespace, String key);

    Response<Void> save(String namespace, String key, String value);

    List<VonConfigDO> getAllByNamespace(String namespace);
}
