package com.shuidihuzhu.workorder.core.service.core.impl;

import com.google.common.base.Function;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.auth.saas.util.AuthSaasContext;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.dao.VonPermissionDisableDAO;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.core.model.view.OrderPermissionInfoVO;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import com.shuidihuzhu.workorder.util.LogHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VonPermissionServiceImpl implements VonPermissionService {

    @Resource
    private VonPermissionDisableDAO vonPermissionDisableDAO;

    @Resource
    private PermissionFeignClient permissionFeignClient;

    @Resource
    private OrderGroupService orderGroupService;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private AssignGroupService assignGroupService;

    @Resource(name = WorkOrderConfig.Async.LocalCache)
    private Executor executor;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    private LoadingCache<Integer, List<Long>> orderTypeUserListCache;

    private LoadingCache<String, List<Long>> orderTypeAssignGroupUserListCache;

    @PostConstruct
    public void init(){
        orderTypeUserListCache = CacheBuilder.newBuilder()
                .maximumSize(WorkOrderType.values().length)
                .expireAfterWrite(3, TimeUnit.MINUTES)
                .refreshAfterWrite(3, TimeUnit.SECONDS)
                .recordStats()
                .build(CacheLoader.asyncReloading(CacheLoader.from(this::getUserListByOrderType), executor));

        orderTypeAssignGroupUserListCache = CacheBuilder.newBuilder()
                .maximumSize(2000)
                .expireAfterWrite(3, TimeUnit.MINUTES)
                .refreshAfterWrite(3, TimeUnit.SECONDS)
                .recordStats()
                .build(CacheLoader.asyncReloading(CacheLoader.from(new Function<>() {
                    @Override
                    public @org.checkerframework.checker.nullness.qual.Nullable List<Long> apply(@org.checkerframework.checker.nullness.qual.Nullable String input) {
                        if (input == null) {
                            return Lists.newArrayList();
                        }
                        String[] split = StringUtils.split(input, ",");
                        String orderTypeStr = split[0];
                        String assignGroupIdStr = split[1];
                        return getUserListByOrderTypeAndAssignGroupId(Integer.parseInt(orderTypeStr), Long.parseLong(assignGroupIdStr));
                    }
                }), executor));
    }

    private boolean isDisable(long operatorId, int orderType) {
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        String permission = workOrderTypeDO.getPermission();
        return vonPermissionDisableDAO.isDisable(operatorId, permission);
    }

    @Override
    public Response<List<OrderPermissionInfoVO>> getInfoByUser(long operatorId) {
        List<String> permissions = getPermissionOrderList().stream()
                .map(workOrderTypeService::getFromOrderTypeCode)
                .map(WorkOrderTypeDO::getPermission)
                .collect(Collectors.toList());
        PermissionParam validPermissionParam = PermissionParam.builder()
                .appCode(AuthSaasContext.getAuthAppCode())
                .userId(operatorId)
                .permissions(permissions)
                .build();
        Response<Set<String>> permissionResp = permissionFeignClient.validUserPermissions(validPermissionParam);
        if (permissionResp == null || permissionResp.notOk()) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        Set<String> hasPermissions = permissionResp.getData();

        // 标记禁用权限
        List<String> disablePermissions = vonPermissionDisableDAO.getDisablePermissionByOperatorId(operatorId);
        HashSet<String> disablePermissionsSet = Sets.newHashSet(ListUtils.emptyIfNull(disablePermissions));
        List<OrderPermissionInfoVO> views = hasPermissions.stream()
                .map(v -> {
                    OrderPermissionInfoVO view = new OrderPermissionInfoVO();
                    view.setDisable(disablePermissionsSet.contains(v));
                    view.setOrderType(WorkOrderType.getFromPermission(v).getType());
                    return view;
                }).collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(views);
    }

    private List<Integer> getPermissionOrderList() {
        List<Integer> groupList = orderGroupService.getGroupListConst();
        ArrayList<Integer> list = Lists.newArrayList(groupList);
        list.add(WorkOrderType.bu_chong_yi_yuan_xin_xi.getType());
        return list;
    }

    @Override
    public Response<Void> saveDisableByUser(long operatorId, int orderType, boolean disable) {
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if (workOrderTypeDO == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        String permission = workOrderTypeDO.getPermission();
        if (disable) {
            vonPermissionDisableDAO.disable(operatorId, permission);
        } else {
            vonPermissionDisableDAO.removeDisable(operatorId, permission);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public List<Long> getUserListByOrderTypeWithCache(int workOrderType) {
        try {
            return orderTypeUserListCache.get(workOrderType);
        } catch (ExecutionException e) {
            log.error("getUserListByOrderTypeWithCache", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public List<Long> getUserListByOrderTypeWithCache(int orderType, long assignGroupId) {
        String key = orderType + "," + assignGroupId;
        try {
            return orderTypeAssignGroupUserListCache.get(key);
        } catch (ExecutionException e) {
            log.error("getUserListByOrderTypeWithCache", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean validPermissionByUserId(Long last, String permission) {
        PermissionParam validPermissionParam = PermissionParam.builder()
                .appCode(AuthSaasContext.getAuthAppCode())
                .userId(last)
                .permissions(Lists.newArrayList(permission))
                .build();
        Response<Boolean> permissionResp = permissionFeignClient.validUserPermission(validPermissionParam);
        if (permissionResp == null || permissionResp.notOk()) {
            return false;
        }
        Boolean data = permissionResp.getData();
        if (data == null) {
            LogHelper.error(log, "权限返回data null");
            return false;
        }
        return data;
    }

    @Override
    public Response<Set<String>> validUserPermissions(PermissionParam permissionParam) {
        return permissionFeignClient.validUserPermissions(permissionParam);
    }

    @Nullable
    private List<Long> getUserListByOrderTypeAndAssignGroupId(int orderType, long assignGroupId) {
        if (assignGroupId <= 0) {
            return getUserListByOrderType(orderType);
        }
        AssignGroupDO assignGroup = assignGroupService.getById(assignGroupId);
        String groupPermission = assignGroup.getPermission();
        return getUserListByPermission(groupPermission);
    }

    private List<Long> getUserListByPermission(String permission) {
        Response<List<AuthUserDto>> response = permissionFeignClient.getUsersByPermission(permission);
        if (response == null || response.notOk()) {
            log.info("queryOnlineStaff permission={} rpcRespons={}", permission, response);
            return Lists.newArrayList();
        }
        List<AuthUserDto> userAccountModelList = response.getData();

        if (CollectionUtils.isEmpty(userAccountModelList)) {
            log.info("queryOnlineStaff permission={} userAccountModelList=null", permission);
            return Lists.newArrayList();
        }

        List<Long> userIds = userAccountModelList.stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
        userIds = filterDisable(userIds, permission);
        return userIds;
    }


    @Nullable
    private List<Long> getUserListByOrderType(int workOrderType) {
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(workOrderType);
        if (workOrderTypeDO == null) {
            return Lists.newArrayList();
        }
        Response<List<AuthUserDto>> response;
        response = permissionFeignClient.getUsersByPermission(workOrderTypeDO.getPermission());
        if (response == null || response.notOk()) {
            log.info("queryOnlineStaff workOrderType={} rpcRespons={}", workOrderType, response);
            return Lists.newArrayList();
        }
        List<AuthUserDto> userAccountModelList = response.getData();

        if (CollectionUtils.isEmpty(userAccountModelList)) {
            log.info("queryOnlineStaff workOrderType={} userAccountModelList=null", workOrderType);
            return Lists.newArrayList();
        }

        List<Long> userIds = userAccountModelList.stream().map(AuthUserDto::getUserId).collect(Collectors.toList());
        userIds = filterDisable(userIds, workOrderType);
        return userIds;
    }

    private List<Long> filterDisable(List<Long> userIds, String permission) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<Long> disableList = vonPermissionDisableDAO.getDisableList(userIds, permission);
        HashSet<Long> disableSet = Sets.newHashSet(disableList);
        return userIds.stream().filter(v -> !disableSet.contains(v)).collect(Collectors.toList());
    }


    private List<Long> filterDisable(List<Long> userIds, int orderType) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        String permission = workOrderTypeDO.getPermission();
        return filterDisable(userIds, permission);
    }

    @Override
    public Response<Void> initStaffByOrderType(int orderType) {
        List<Long> users = getUserListByOrderTypeWithCache(orderType);
        if (users == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<StaffStatus> staffs = staffStatusService.getStaffs(users, orderType);
        Set<Long> hasInitUsers = staffs.stream().map(StaffStatus::getUserId).collect(Collectors.toSet());
        users.stream()
                .filter(v -> !hasInitUsers.contains(v))
                .forEach(v -> {
                    StaffStatus s = new StaffStatus();
                    s.setUserId(v);
                    s.setOrderType(orderType);
                    s.setStaffStatus(StaffStatusEnum.offline.getType());
                    staffStatusService.changeStatus(s);
                });
        return NewResponseUtil.makeSuccess(null);
    }

}
