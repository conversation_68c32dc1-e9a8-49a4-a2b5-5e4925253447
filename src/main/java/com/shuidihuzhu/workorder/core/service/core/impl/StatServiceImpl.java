package com.shuidihuzhu.workorder.core.service.core.impl;

import com.shuidihuzhu.client.cf.workorder.v2.model.view.WorkOrderStatVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.StatService;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.impl.CommonWorkOrderStatServiceImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class StatServiceImpl implements StatService {

    @Autowired
    private CommonWorkOrderStatServiceImpl commonWorkOrderStatService;

    @Override
    public Response<List<WorkOrderStatVO>> list(int oneLevel, String twoLevel, long userId) {
        OpResult<List<WorkOrderStatVO>> workOrderStatList = commonWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);
        return ResultUtils.transformOpResult2Response(workOrderStatList);
    }

}
