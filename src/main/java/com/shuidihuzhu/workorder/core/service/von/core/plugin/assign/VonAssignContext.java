package com.shuidihuzhu.workorder.core.service.von.core.plugin.assign;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonAssignConfig;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class VonAssignContext {

    private int orderTypeCode;

    private WorkOrderType orderType;

    private Response<?> response;

    // -------

    private VonAssignConfig config;

    private Boolean groupAssign;

    // single order

    private WorkOrderBase workOrder;

    private List<Long> users;

    private Long assignerUser;

    private Map<String, String> extMap;

    public VonAssignContext() {
    }

    public static VonAssignContext create(AssignateWorkOrderEvent workOrderEvent){
        return create(workOrderEvent.getOrderType());
    }

    public static VonAssignContext create(int orderType) {
        VonAssignContext v = new VonAssignContext();
        v.setOrderTypeCode(orderType);
        return v;
    }

    public void cleanAssigner() {
        extMap = null;
        assignerUser = null;
        users = null;
        workOrder = null;
    }
}
