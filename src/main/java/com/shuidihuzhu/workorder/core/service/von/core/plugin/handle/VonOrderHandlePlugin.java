package com.shuidihuzhu.workorder.core.service.von.core.plugin.handle;

import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.VonStatusManageService;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VonOrderHandlePlugin {

    @Autowired
    private UserOperationRecordDao recordDao;

    @Autowired
    AssignateWorkOrderPublisher publisher;

    @Resource
    private MeterRegistry meterRegistry;

    @Autowired
    private VonStatusManageService vonStatusManageService;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private OrderGroupService orderGroupService;

    public Response<Void> handle(HandleOrderParam param) {

        long workOrderId = param.getWorkOrderId();
        String operationMsg = OperateMode.handle.getMsg();
        Response<WorkOrderBase> result = vonStatusManageService.updateStatusOperate(param.getWorkOrderId(), param.getHandleResult(),
                param.getUserId(), OperateMode.handle, operationMsg, param.getOperComment());


        log.info("doHandle param={} result={}", param, result);

//        if (result.ok()) {
//            //工单操作记录
//            WorkOrderRecord workOrderRecord = WorkOrderRecord.handleRecord(param);
//            this.saveWorkOrderRecord(workOrderRecord);
//        }
        if (result.ok()) {
            orderGroupService.onOrderHandle(workOrderId);

            WorkOrderBase workOrderBase = result.getData();

            WorkOrderBaseVo base = new WorkOrderBaseVo();
            base.setId(workOrderId);
            base.setOrderType(workOrderBase.getOrderType());
            base.setHandleResult(param.getHandleResult());
            base.setCaseId(workOrderBase.getCaseId());

            //发送工单状态变化事件
            EventPublishHelper.sendOrderStatusChangeOld(this, base);

            //处理完成，发送分配工单event
            publisher.publishEvent(new AssignateWorkOrderEvent(this, workOrderBase.getOrderType()));

        }
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workOrderId);


        meterRegistry.counter(OperationStat.WOEKORDER_OPERATING_STAT,
                OperationStat.OPERATION, OperationStat.handle,
                OperationStat.ORDERTYPE, workOrderBase.getOrderType() + "",
                OperationStat.handle_result, param.getHandleResult() + ""
        ).increment();

        return EhResponseUtils.response(result.getCode(), result.getMsg(), null);
    }

    /**
     * 工单操作记录
     * @param workOrderRecord
     */
    private void saveWorkOrderRecord(WorkOrderRecord workOrderRecord) {
        if (workOrderRecord == null){
            return;
        }
        recordDao.saveRecord(workOrderRecord);
    }

}
