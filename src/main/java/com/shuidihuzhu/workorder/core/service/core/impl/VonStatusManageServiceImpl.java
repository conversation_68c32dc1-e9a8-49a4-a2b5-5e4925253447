package com.shuidihuzhu.workorder.core.service.core.impl;

import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.service.core.VonStatusManageService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.VonHandleConfig;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VonStatusManageServiceImpl implements VonStatusManageService {

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private EventPublishHelper eventPublishHelper;

    @Autowired
    private VonConfigFacade vonConfigFacade;

    //102是系统userid
    private static final long SYSTEM_USER_ID = 102;

    @Override
    public Response<WorkOrderBase> updateStatus(long workOrderId, int handleResult, long operatorId, String operationMsg, String comment) {
        OperateMode operate = OperateMode.common;
        return updateStatusOperate(workOrderId, handleResult, operatorId, operate, operationMsg, comment);
    }

    @Override
    public Response<WorkOrderBase> updateStatusOperate(long workOrderId, int handleResult, long operatorId, OperateMode operateMode, String operationMsg, String comment) {
        WorkOrderBase preWorkOrder = workOrderDao.getWorkOrderById(workOrderId);

        // 是否校验处理人为当前工单领取人
        Boolean selfHandleCheck = false;
        if (operatorId != SYSTEM_USER_ID && operatorId > 0) {
            VonHandleConfig config = vonConfigFacade.getHandleConfig(preWorkOrder.getOrderType());
            selfHandleCheck = config.getWorkOrderSelfHandle();
        }
        int result = workOrderDao.handleIsCheckStatus(workOrderId, handleResult, operatorId, selfHandleCheck);

        if (result <= 0){
            log.info("updateStatusOperate fail effect 0 orderId {}", workOrderId);
            return EhResponseUtils.fail(ErrorCode.SYSTEM_HANDLE_ERROR);
        }

        // 拼接操作备注
        String operateComment;
        operateComment = String.format("%s -> %s",
                HandleResultEnum.getShowMsgByType(preWorkOrder.getHandleResult()) + "_" + preWorkOrder.getHandleResult(),
                HandleResultEnum.getShowMsgByType(handleResult)) + "_" + handleResult;
        if (StringUtils.isNotEmpty(comment)){
            operateComment = operateComment + " 备注: " + comment;
        }

        // 发送操作事件通知
        eventPublishHelper.sendOrderStatusChange(this, workOrderId, preWorkOrder.getOrderType(),
                preWorkOrder.getHandleResult(), handleResult, operatorId, operateMode, operationMsg, operateComment);


        return EhResponseUtils.success(preWorkOrder);
    }

}
