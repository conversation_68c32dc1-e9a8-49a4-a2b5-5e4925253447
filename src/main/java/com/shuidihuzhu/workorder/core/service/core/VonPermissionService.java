package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.model.view.OrderPermissionInfoVO;
import org.w3c.dom.CharacterData;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface VonPermissionService {

    Response<List<OrderPermissionInfoVO>> getInfoByUser(long operatorId);

    Response<Void> saveDisableByUser(long operatorId, int orderType, boolean disable);

    List<Long> getUserListByOrderTypeWithCache(int orderType);

    Response<Void> initStaffByOrderType(int orderType);

    List<Long> getUserListByOrderTypeWithCache(int orderType, long assignGroupId);

    boolean validPermissionByUserId(Long last, String permission);

    Response<Set<String>> validUserPermissions(PermissionParam permissionParam);
}
