package com.shuidihuzhu.workorder.core.service.core.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfWorkOrderModel;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.CrowdfundingInfoTableParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.dao.WorkOrderReadDAO;
import com.shuidihuzhu.workorder.core.delegate.SDEncryptUtils;
import com.shuidihuzhu.workorder.core.delegate.UserInfoDelegate;
import com.shuidihuzhu.workorder.core.delegate.WorkOrderEsDelegate;
import com.shuidihuzhu.workorder.core.service.core.OrderReadService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.storage.StorageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderReadServiceImpl implements OrderReadService {

    @Autowired
    private WorkOrderEsDelegate workOrderEsDelegate;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Resource
    private WorkOrderReadDAO workOrderReadDAO;

    @Resource
    private StorageService storageService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private CrowdfundingFeignClient client;

    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Response<PaginationListVO<BasicWorkOrder>> search(OrderSearchParam param) {
        //如果是手机号搜索 直接验证用户是否存在
        UserInfoModel userInfoModel;
        long userId = 0;
        if (StringUtils.isNotBlank(param.getMobile())) {
            String aesEncryptMobile = SDEncryptUtils.encrypt(param.getMobile());
            userInfoModel = userInfoDelegate.getUserInfoByCryptoMobile(aesEncryptMobile);
            if (Objects.isNull(userInfoModel)) {
                return createEmptyResult();
            } else {
                userId = userInfoModel.getUserId();
            }
        }
        CfWorkOrderV2IndexSearchParam searchParam = buildOrderListEsParam(param, userId);
        Response<CfWorkOrderIndexSearchResult> esResponse = workOrderEsDelegate.getWorkOrderIdList(searchParam);
        CfWorkOrderIndexSearchResult esData = esResponse.getData();
        if (esResponse.notOk() || esData == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<CfWorkOrderModel> models = esData.getModels();
        if (CollectionUtils.isEmpty(models)) {
            return createEmptyResult();
        }
        List<Long> orderIdList = models.stream().map(CfWorkOrderModel::getWorkOrderId).collect(Collectors.toList());
        Response<List<BasicWorkOrder>> listByOrderIdList = getListByOrderIdList(orderIdList);
        if (listByOrderIdList.notOk()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<BasicWorkOrder> vos = listByOrderIdList.getData();
        PaginationListVO<BasicWorkOrder> vo = PaginationListVO.create(vos, param.getCurrent(), param.getPageSize(), esData.getTotal());
        return NewResponseUtil.makeSuccess(vo);

    }

    @Override
    public Response<List<BasicWorkOrder>> getListByOrderIdList(List<Long> workOrderIdList) {
        if (CollectionUtils.isEmpty(workOrderIdList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<BasicWorkOrder> list = workOrderReadDAO.getListByIds(workOrderIdList);
        list = storageService.fillStorages(list);
        return NewResponseUtil.makeSuccess(list);
    }

    @Override
    public Response<PageResult<WorkOrderVO>> getOrderList(WorkOrderListParam param) {
        String paging = param.getPaging();
        int pageSize = param.getPageSize();
        long userId = param.getUserId();

        PageResult<WorkOrderVO> pageResult = new PageResult<>();

        List<Integer> results = Arrays.stream(param.getHandleResult().split(",")).filter(org.apache.commons.lang3.StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList());

        int p = pageSize + 1;

        List<WorkOrderBase> list = workOrderDao.getWorkorderList(Lists.newArrayList(userId), param.getOrderType(), results, p, param.getWorkOrderId(),
                paging, param.getCaseId(), param.getStartTime(), param.getEndTime(), param.getOrderLevel());
        //多查询一次  判断是否有下一页
        if (list.size() == p) {
            pageResult.setHasNext(true);
            //删除多余的一条数据
            list.remove(list.size() - 1);
        }

        //如果不存在列表
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return NewResponseUtil.makeSuccess(pageResult);
        }

        //如果是前一页   需要重新排序  防止数据错乱
        if (WorkOrderConfig.pre_paging.equals(paging)) {
            //如果点击前一页  就肯定有后一页
            pageResult.setHasNext(true);
            list = list.stream().sorted(Comparator.comparing(WorkOrderBase::getId).reversed()).collect(Collectors.toList());
        }

        List<Integer> caseIds = list.stream().map(WorkOrderBase::getCaseId).distinct().collect(Collectors.toList());

        List<CrowdfundingInfo> feignResponse = client.getCrowdfundingListById(caseIds).getData();

        Map<Integer, CrowdfundingInfo> map = feignResponse.stream().collect(Collectors.toMap(CrowdfundingInfo::getId, Function.identity(), (o1, o2) -> o2));

        List<WorkOrderVO> voList = list.stream().filter(r -> {
            //过滤不存的案例
            if (map.containsKey(r.getCaseId())) {
                return true;
            }
            log.error("getOrderList CrowdfundingInfo=null caseId={}", r.getCaseId());
            return false;

        }).map(r -> {
            WorkOrderVO workOrderVO = new WorkOrderVO();

            workOrderVO.setWorkOrderId(r.getId());
            workOrderVO.setCaseId(r.getCaseId());
            workOrderVO.setOrderLevel(r.getOrderlevel());
            workOrderVO.setUpdateTime(r.getUpdateTime());
            workOrderVO.setHandleTime(r.getHandleTime());
            workOrderVO.setHandleResult(r.getHandleResult());
            CrowdfundingInfo c = map.get(r.getCaseId());
            workOrderVO.setCaseCreateTime(DateFormatUtils.format(r.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            workOrderVO.setTitle(c.getTitle());
            workOrderVO.setCaseUuid(c.getInfoId());
            workOrderVO.setCreateTime(r.getCreateTime());
            workOrderVO.setOrderType(r.getOrderType());
            return workOrderVO;

        }).collect(Collectors.toList());

        pageResult.setPageList(voList);
        return NewResponseUtil.makeSuccess(pageResult);
    }


    private CfWorkOrderV2IndexSearchParam buildOrderListEsParam(OrderSearchParam p, long userId) {
        List<Integer> types = Lists.newArrayList();
        if (StringUtils.isNotEmpty(p.getOrderType())) {
            types = Arrays.stream(p.getOrderType().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        List<Integer> orderLevels = Lists.newArrayList();
        if (StringUtils.isNotEmpty(p.getOrderLevel())) {
            orderLevels = Arrays.stream(p.getOrderLevel().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }
        List<Integer> handleResults = Lists.newArrayList();
        if (StringUtils.isNotEmpty(p.getHandleResults())) {
            handleResults = Arrays.stream(p.getHandleResults().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        if (p.getWorkOrderId() > 0) {
            woTableParam.setIds(Lists.newArrayList(p.getWorkOrderId()));
        }
        if (p.getCaseId() > 0) {
            long caseId = p.getCaseId();
            woTableParam.setCaseIds(Lists.newArrayList(caseId));
        }
        if (p.getOperId() > 0) {
            woTableParam.setOperatorIds(Lists.newArrayList(p.getOperId()));
        }
        if (p.getHandleResult() >= 0) {
            woTableParam.setHandleResult(Lists.newArrayList(p.getHandleResult()));
        }
        if (CollectionUtils.isNotEmpty(handleResults)) {
            woTableParam.setHandleResult(handleResults);
        }
        if (CollectionUtils.isNotEmpty(types)){
            woTableParam.setOrderTypes(types);
        }
        if (CollectionUtils.isNotEmpty(orderLevels)) {
            woTableParam.setOrderLevels(orderLevels);
        }
        long startCreateTime = promoteStrTime(p.getStartCreateTime());
        if (startCreateTime > 0) {
            woTableParam.setCreateStartTime(startCreateTime);
        }
        long endCreateTime = promoteStrTime(p.getEndCreateTime());
        if (endCreateTime > 0) {
            woTableParam.setCreateEndTime(endCreateTime);
        }
        long startHandleTime = promoteStrTime(p.getStartHandleTime());
        if (startHandleTime > 0) {
            woTableParam.setHandleStartTime(startHandleTime);
        }
        long endHandleTime = promoteStrTime(p.getEndHandleTime());
        if (endHandleTime > 0) {
            woTableParam.setHandleEndTime(endHandleTime);
        }
        long startDoneTime = promoteStrTime(p.getStartDoneTime());
        if (startDoneTime > 0) {
            woTableParam.setFinishStartTime(startDoneTime);
        }
        long endDoneTime = promoteStrTime(p.getEndDoneTime());
        if (endDoneTime > 0) {
            woTableParam.setFinishEndTime(endDoneTime);
        }
        long startUpdateTime = promoteStrTime(p.getStartUpdateTime());
        if (startUpdateTime > 0) {
            woTableParam.setUpdateStartTime(startUpdateTime);
        }
        long endUpdateTime = promoteStrTime(p.getEndUpdateTime());
        if (endUpdateTime > 0) {
            woTableParam.setUpdateEndTime(endUpdateTime);
        }
        CrowdfundingInfoTableParam crowdfundingInfoTableParam = new CrowdfundingInfoTableParam();
        if (userId > 0) {
            crowdfundingInfoTableParam.setUserId(userId);
        }

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        searchParam.setWorkOrderExtTableParamList(p.getWorkOrderExtTableParamList());
        searchParam.setCiTableParam(crowdfundingInfoTableParam);
        searchParam.setFrom((p.getCurrent() - 1) * p.getPageSize());
        searchParam.setSize(p.getPageSize());
        return searchParam;
    }


    private Response<PaginationListVO<BasicWorkOrder>> createEmptyResult() {
        PaginationListVO<BasicWorkOrder> result = PaginationListVO.createEmpty();
        return NewResponseUtil.makeSuccess(result);
    }

    public static void main(String[] args) {
        String a = "2021-09-15 12:15:04";
        long l = promoteStrTime(a);
        System.out.println("l = " + l);
    }

    private static long promoteStrTime(String str) {
        if (StringUtils.isBlank(str)) {
            return 0;
        }
        try {
            return dateTimeFormat.parse(str).getTime();
        } catch (Exception e) {
            log.error("promoteStrTime error str {}", str, e);
        }
        return 0;
    }

}
