package com.shuidihuzhu.workorder.core.service.von.core.config;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.von.core.config.orders.*;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface VonConfigFacade {

    Response<Map<Integer, List<Integer>>> getDoneListConfig();

    Map<Integer, List<Integer>> getDoneListConfigMap();

    List<Integer> getAllDoneHandleResultList();

    Response<Void> process();

    String getProperty(int orderType, String key);

    Boolean getBooleanProperty(int orderType, String key);

    Integer getIntegerProperty(int orderType, String key);

    VonAssignConfig getAssignConfig(int orderType);

    VonHandleConfig getHandleConfig(int orderType);

    VonAutoFreeConfig getAutoFreeConfig(int orderType);

    VonReprocessConfig getReprocessConfig(int orderType);

    VonReminderConfig getReminderConfig(int orderType);

    List<VonConfigDO> getAllByOrderType(int orderType);

    void saveConfig(int orderType, String key, String value);

    void saveByMap(int orderType, Map<String, String> configMap);

    void saveByList(List<VonConfigDO> configList);

}
