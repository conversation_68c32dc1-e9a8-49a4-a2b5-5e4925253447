package com.shuidihuzhu.workorder.core.service.von.core.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VonConfig {

    @ApiModelProperty("使用老版工单分配")
    private boolean isUseOldAssigner;

    @ApiModelProperty("领单限制数量")
    private int assignLimitCount;

    @ApiModelProperty("不给有工单处理中的人分配")
    private boolean notAssignDoingOperator;

    @ApiModelProperty("每查出一个领单人一次分多少工单")
    private int assignCountPearOperator;
}
