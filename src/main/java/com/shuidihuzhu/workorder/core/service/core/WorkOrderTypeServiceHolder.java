package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class WorkOrderTypeServiceHolder {

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    private static WorkOrderTypeService sWorkOrderTypeService;

    @PostConstruct
    public void init(){
        sWorkOrderTypeService = workOrderTypeService;
    }

    public static WorkOrderTypeService get(){
        return sWorkOrderTypeService;
    }

}