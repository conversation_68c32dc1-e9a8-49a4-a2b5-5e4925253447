package com.shuidihuzhu.workorder.core.service.core.notice;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BaseNotice implements INotice{
    @ApiModelProperty("通知模板内容")
    private String template;
    @ApiModelProperty("通知key 飞书机器人id")
    private String noticeKey;
    @ApiModelProperty("at人员列表")
    private List<String> additionNoticeUser;
}
