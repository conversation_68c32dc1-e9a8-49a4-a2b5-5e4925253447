package com.shuidihuzhu.workorder.core.service.von.core.config.orders;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeConstants;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@VonConfig("autoFree")
public class VonAutoFreeConfig implements IVonConfig {

    @ApiModelProperty("稍后处理是否支持离线自动回收")
    private Boolean enableLaterDoAutoFree;

    @ApiModelProperty("处理中是否支持离线自动回收")
    private Boolean enableDoingAutoFree;

    @Override
    public void promoteDefault(int orderType) {

        if(getEnableLaterDoAutoFree() == null){
            switch (orderType) {
                case WorkOrderTypeConstants.HUIFANG:
                case WorkOrderTypeConstants.SHENHE:
                case WorkOrderTypeConstants.CAILIAO_1:
                case WorkOrderTypeConstants.CAILIAO_0:
                case WorkOrderTypeConstants.CAILIAO_3:
                case WorkOrderTypeConstants.YANHOU:
                case WorkOrderTypeConstants.HESHI:
                case WorkOrderTypeConstants.DIANHUA_SHENHE:
                case WorkOrderTypeConstants.YILIAO_SHENHE:
                case WorkOrderTypeConstants.HIGH_RISK_SHENHE:
                case WorkOrderTypeConstants.ZENGXIN_NORMAL:
                case WorkOrderTypeConstants.ZENGXIN_RISK:
                case WorkOrderTypeConstants.AI_PHOTO:
                case WorkOrderTypeConstants.AI_CONTENT:
                case WorkOrderTypeConstants.AI_ERCI:
                    setEnableLaterDoAutoFree(true);
                    break;
                default:
                    setEnableLaterDoAutoFree(false);
                    break;
            }
        }

        if (enableLaterDoAutoFree == null) {
            enableLaterDoAutoFree = false;
        }
        if (enableDoingAutoFree == null) {
            enableDoingAutoFree = true;
        }
    }
}
