package com.shuidihuzhu.workorder.core.service.core.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.dao.WorkOrderOneTypeDAO;
import com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderTypeDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class WorkOrderTypeServiceImpl implements WorkOrderTypeService {

    @Resource
    private WorkOrderTypeDAO workOrderTypeDAO;
    @Resource
    private WorkOrderOneTypeDAO workOrderOneTypeDAO;

    // 二级工单类型缓存(key:二级工单类型typeCode)
    private LoadingCache<Integer, WorkOrderTypeDO> workOrderTypeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .refreshAfterWrite(1, TimeUnit.DAYS)
            .build(CacheLoader.from(this::workOrderTypeCacheLoader));

    // 一级工单对应所有二级工单列表缓存(key:一级工单类型typeCode)
    private LoadingCache<Integer, List<WorkOrderTypeDO>> oneMap = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .refreshAfterWrite(1, TimeUnit.DAYS)
            .build(CacheLoader.from(this::oneMapCacheLoader));

    private LoadingCache<String, WorkOrderTypeDO> permissionMap = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.DAYS)
            .refreshAfterWrite(1, TimeUnit.DAYS)
            .build(CacheLoader.from(this::permissionMapCacheLoader));

    @PostConstruct
    public void init() {
        List<WorkOrderTypeDO> workOrderTypeDOList = workOrderTypeDAO.getAllOrderType();
        if(CollectionUtils.isNotEmpty(workOrderTypeDOList)){
            oneMap.putAll(workOrderTypeDOList.stream().collect(Collectors.groupingBy(WorkOrderTypeDO::getRelateId)));
            workOrderTypeCache.putAll(workOrderTypeDOList.stream().collect(Collectors.toMap(WorkOrderTypeDO::getOrderTypeCode, Function.identity())));
            permissionMap.putAll(workOrderTypeDOList.stream().collect(Collectors.toMap(WorkOrderTypeDO::getPermission, Function.identity())));
        }
    }

    @Override
    public Response<Boolean> insertWorkOrder(WorkOrderTypeDO workOrderTypeDO) {
        log.debug("insertWorkOrder:{}", workOrderTypeDO);
        int result = workOrderTypeDAO.insertWorkOrder(workOrderTypeDO);
        if(result > 0){
            oneMap.refresh(workOrderTypeDO.getRelateId());
            workOrderTypeCache.refresh(workOrderTypeDO.getOrderTypeCode());
            permissionMap.refresh(workOrderTypeDO.getPermission());
        }
        return NewResponseUtil.makeSuccess(result > 0);
    }

    @Override
    public WorkOrderTypeDO getFromOrderTypeCode(int orderType) {
        log.debug("getWorkOrderTypeFromOrderTypeCode:{}", orderType);
        try {
            return workOrderTypeCache.get(orderType);
        } catch (Exception e) {
            log.warn("getWorkOrderTypeFromOrderTypeCode error", e);
        }
        return null;
    }

    @Override
    public List<WorkOrderTypeDO> getAllOrderType() {
        return workOrderTypeDAO.getAllOrderType();
    }

    @Override
    public List<Integer> getByOneLevel(int oneTypeCode) {
        try {
            return oneMap.get(oneTypeCode).stream().map(WorkOrderTypeDO::getOrderTypeCode).collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("getByOneLevel error", e);
        }
        return null;
    }

    @Override
    public Response<Boolean> insertWorkOrderOneType(WorkOrderOneTypeDO workOrderOneTypeDO) {
        log.debug("insertWorkOrderOneType:{}", workOrderOneTypeDO);
        int result = workOrderOneTypeDAO.insertWorkOrder(workOrderOneTypeDO);
        if(result > 0){
            oneMap.refresh(workOrderOneTypeDO.getOneTypeCode());
        }
        return NewResponseUtil.makeSuccess(result > 0);
    }

    @Override
    public Map<Integer, List<Integer>> getClassiyByPermissions(Set<String> permissions){

        List<WorkOrderTypeDO> list = permissions.stream().map(r-> {
            try {
                return permissionMap.get(r);
            } catch (ExecutionException e) {
                log.warn("getClassiyByPermissions error, permission:{}", e, r);
                return null;
            }
        }).filter(r -> r != null && r.getRelateId() != 0).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(list)){
            return Maps.newHashMap();
        }

        Map<Integer, List<Integer>> map = list.stream()
                .collect(Collectors.groupingBy(WorkOrderTypeDO::getRelateId,Collectors.mapping(r->r.getOrderTypeCode(),Collectors.toList())));

        return map;
    }

    @Override
    public List<String> getAllPermissions() {
        List<WorkOrderTypeDO> list = workOrderTypeDAO.getAllOrderType();
        if (CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        return list.stream().map(WorkOrderTypeDO::getPermission).collect(Collectors.toList());
    }

    @Override
    public List<WorkOrderOneTypeDO> getAllOneType() {
        List<WorkOrderOneTypeDO> result = workOrderOneTypeDAO.getAllOrderType();
        if(CollectionUtils.isEmpty(result)){
            log.info("getAllOneType result is null");
            return Lists.newArrayList();
        }
        return result;
    }

    @Override
    public Integer getOneFromTwo(Integer orderTypeCode) {
        try {
            return workOrderTypeCache.get(orderTypeCode).getRelateId();
        } catch (Exception e) {
            log.warn("getOneFromTwo error", e);
        }
        return 0;
    }

    @Override
    public String getPermissionFromOrderTypeCode(int orderTypeCode) {
        try {
            return workOrderTypeCache.get(orderTypeCode).getPermission();
        } catch (Exception e) {
            log.warn("getPermissionFromOrderTypeCode error", e);
        }
        return "";
    }

    private WorkOrderTypeDO workOrderTypeCacheLoader(int orderType) {
        /**
         *         select *
         *         from work_order_type
         *         where
         *         `order_type_code` = #{orderType}
         *         and
         *         `is_delete` = 0
         */
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeDAO.getByOrderTypeCode(orderType);
        if(workOrderTypeDO == null){
            log.info("workOrderTypeCacheLoader workOrderTypeDO is null orderType:{}", orderType);
            return new WorkOrderTypeDO();
        }
        return workOrderTypeDO;
    }

    private List<WorkOrderTypeDO> oneMapCacheLoader(int oneTypeCode){
        List<WorkOrderTypeDO> workOrderTypeDOList = workOrderTypeDAO.getAllOrderTypeByOneTypeCode(oneTypeCode);
        if(CollectionUtils.isEmpty(workOrderTypeDOList)){
            log.info("oneMapCacheLoader workOrderTypeDOList is null oneTypeCode:{}", oneTypeCode);
            return Lists.newArrayList();
        }
        return workOrderTypeDOList;
    }

    private WorkOrderTypeDO permissionMapCacheLoader(String permission){
        WorkOrderTypeDO workOrderTypeDO = workOrderTypeDAO.getByPermission(permission);
        if(workOrderTypeDO == null){
            log.info("permissionMapCacheLoader workOrderTypeDO is null permission:{}", permission);
            return new WorkOrderTypeDO();
        }
        return workOrderTypeDO;
    }
}
