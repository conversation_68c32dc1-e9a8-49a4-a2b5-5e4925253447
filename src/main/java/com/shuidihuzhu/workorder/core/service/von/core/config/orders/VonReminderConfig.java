package com.shuidihuzhu.workorder.core.service.von.core.config.orders;

import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: wangpeng
 * @Date: 2023/2/24 15:54
 * @Description:
 */
@Data
@VonConfig("reminder")
public class VonReminderConfig implements IVonConfig {
    @ApiModelProperty("工单是否可以催单")
    private Boolean workOrderReminder;

    @Override
    public void promoteDefault(int orderType) {
        if (workOrderReminder == null) {
            workOrderReminder = false;
        }
    }
}
