package com.shuidihuzhu.workorder.core.service.von.core.config.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.dao.VonConfigDAO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VonConfigServiceImpl implements VonConfigService {

    @Autowired
    private VonConfigDAO vonConfigDAO;

    private LoadingCache<String, Optional<String>> configCache;

    @Resource(name = WorkOrderConfig.Async.LocalCache)
    private Executor defaultExecutor;

    private static final String SPLIT = "##s##";

    @PostConstruct
    public void init(){
        configCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(10, TimeUnit.SECONDS)
                .expireAfterWrite(10, TimeUnit.MINUTES)
                .build(CacheLoader.asyncReloading(CacheLoader.from(this::getFromDB), defaultExecutor));
    }

    @Override
    public String getProperty(String namespace, String key) {
        try {
            return configCache.get(namespace + SPLIT + key).orElse(null);
        } catch (ExecutionException e) {
            log.error("缓存查询失败 读db");
            return getFromDB(namespace + SPLIT + key).orElse(null);
        }
    }

    @Override
    public Boolean getBooleanProperty(String namespace, String key) {
        final String property = getProperty(namespace, key);
        if (StringUtils.isEmpty(property)) {
            return null;
        }
        return Boolean.valueOf(property);
    }

    @Override
    public Integer getIntProperty(String namespace, String key) {
        final String property = getProperty(namespace, key);
        if (StringUtils.isEmpty(property)) {
            return null;
        }
        return Integer.valueOf(property);
    }

    @Override
    public Response<Void> save(String namespace, String key, String value) {
        vonConfigDAO.save(namespace, key, value);
        log.info("von-config 修改配置  namespace {}, key {}, value {}", namespace, key, value);
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public List<VonConfigDO> getAllByNamespace(String namespace) {
        return vonConfigDAO.getAllByNamespace(namespace);
    }

    private Optional<String> getFromDB(String namespaceAndKey) {
        final String[] split = StringUtils.splitByWholeSeparator(namespaceAndKey, SPLIT);
        return Optional.ofNullable(vonConfigDAO.get(split[0], split[1]));
    }

}
