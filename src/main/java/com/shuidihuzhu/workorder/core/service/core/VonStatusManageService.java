package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.enums.OperateMode;

/**
 * <AUTHOR>
 */
public interface VonStatusManageService {

    Response<WorkOrderBase> updateStatus(long workOrderId, int handleResult, long operatorId, String operationMsg, String comment);

    Response<WorkOrderBase> updateStatusOperate(long workOrderId, int handleResult, long operatorId, OperateMode operateMode, String operationMsg, String comment);
}
