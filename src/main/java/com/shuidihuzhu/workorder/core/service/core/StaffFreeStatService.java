package com.shuidihuzhu.workorder.core.service.core;

import org.jetbrains.annotations.Nullable;

/**
 * 员工空闲统计
 * <AUTHOR>
 */
public interface StaffFreeStatService {
    @Nullable
    Boolean isFree(long userId);

    void onOrderStatusChange(long workOrderId);

    void syncByWorkOrderId(Long workOrderId);

    void syncByOperatorId(long operatorId);

    long getTodayGroupFreeTime(long userId);
}
