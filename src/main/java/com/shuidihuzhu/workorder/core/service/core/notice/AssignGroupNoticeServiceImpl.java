package com.shuidihuzhu.workorder.core.service.core.notice;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.delegate.sea.SeaAuthHelper;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.core.service.core.notice.AssignGroupNoticeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssignGroupNoticeServiceImpl implements AssignGroupNoticeService {

    @Autowired
    private AssignGroupService assignGroupService;

    @Autowired
    private AssignNoticeServiceImpl assignNoticeService;

    @Autowired
    private WorkOrderDao workOrderDao;

    private Map<String, GroupNotice> permissionNoticeMap = Maps.newHashMap();

    private static final String KEY_GROUP_CONFIG = "apollo.von.assign-notice.group";

    @PostConstruct
    public void init(){
        loadConfig();
        ConfigService.getAppConfig().addChangeListener(changeEvent -> loadConfig(), Sets.newHashSet(KEY_GROUP_CONFIG));
    }

    private void loadConfig() {
        String groupConfig = ConfigService.getAppConfig().getProperty(KEY_GROUP_CONFIG, "{}");
        GroupNoticeConfig c = JSON.parseObject(groupConfig, GroupNoticeConfig.class);
        if (c == null) {
            return ;
        }
        List<GroupNotice> groupNotice = c.getGroupNotice();
        if (CollectionUtils.isEmpty(groupNotice)) {
            return ;
        }
        permissionNoticeMap = groupNotice.stream()
                .peek(v -> {
                    v.setNoticeKey(v.getGroupKey());
                }).collect(Collectors.toMap(GroupNotice::getPermission, Function.identity()));
    }

    @Async(WorkOrderConfig.Async.NOTICE)
    @Override
    public void onOrderAssign(WorkOrderVO v) {
        long workOrderId = v.getWorkOrderId();
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        int orderType = workOrder.getOrderType();
        List<AssignGroupDO> list = assignGroupService.getListByOrderType(orderType);
        if (CollectionUtils.isEmpty(list)) {
            return ;
        }
        List<String> permissions = list.stream().map(AssignGroupDO::getPermission).collect(Collectors.toList());
        long operatorId = workOrder.getOperatorId();

        PermissionParam validPermissionParam = PermissionParam.builder()
                .appCode(SeaAuthHelper.delegate().getAuthAppCode())
                .userId(operatorId)
                .permissions(permissions)
                .build();
        Response<Set<String>> permissionResp = SeaAuthHelper.delegate().permission().validUserPermissions(validPermissionParam);
        if (permissionResp == null || permissionResp.notOk()) {
            return ;
        }
        Set<String> hasPermissions = permissionResp.getData();
        String permission = selectPermission(permissions, hasPermissions);
        if (permission == null) {
            return;
        }
        GroupNotice groupNotice = permissionNoticeMap.get(permission);
        if (groupNotice == null) {
            return;
        }

        assignNoticeService.send(groupNotice, workOrder);
    }

    private String selectPermission(List<String> permissions, Set<String> hasPermissions) {
        if (CollectionUtils.isEmpty(hasPermissions)) {
            return null;
        }
        for (String p : permissions) {
            if (hasPermissions.contains(p)) {
                return p;
            }
        }
        return null;
    }

    @Data
    public static class GroupNoticeConfig {
        private List<GroupNotice> groupNotice;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class GroupNotice extends BaseNotice{

        @ApiModelProperty("根据权限区分通知组")
        private String permission;

        @ApiModelProperty("通知key")
        private String groupKey;

        @Override
        public String getNoticeKey() {
            return groupKey;
        }
    }
}
