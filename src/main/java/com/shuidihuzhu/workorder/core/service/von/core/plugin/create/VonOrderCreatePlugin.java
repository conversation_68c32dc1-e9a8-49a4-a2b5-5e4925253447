package com.shuidihuzhu.workorder.core.service.von.core.plugin.create;

import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.WorkOrderResultChangeEvent;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.model.von.VonOrderCreateContext;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.core.service.core.impl.OrderOperationFacadeImpl;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.OrganizationService;
import com.shuidihuzhu.workorder.service.event.AssignateWorkOrderPublisher;
import com.shuidihuzhu.workorder.util.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class VonOrderCreatePlugin {

    @Resource
    private UserOperationRecordDao recordDao;

    @Resource
    private OrderExtService orderExtService;

    @Resource
    AssignateWorkOrderPublisher publisher;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private OrganizationService organizationService;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private AssignGroupService assignGroupService;

    @Autowired
    private OrderOperationFacadeImpl orderOperationFacade;

    public Response<VonOrderCreateContext> create(WorkOrderCreateParam createParam) {
        return create(VonOrderCreateContext.createParam(createParam));
    }

    /**
     * 1.创建前检查
     * 2.加锁执行创建
     */
    public Response<VonOrderCreateContext> create(VonOrderCreateContext context) {

        WorkOrderCreateParam createParam = context.getCreateParam();
        // 创建前校验
        context.setValidPassed(true);

        if (createParam.getCaseId() == 0){
            return createActual(context);
        }
        // 创建逻辑
        RLock rLock = null;
        try {
            String lockName = getKey(context);
            rLock = cf2RedissonHandler.getLock(lockName);
            if (!rLock.tryLock()) {
                log.info("doCreate caseId={} ordertype={}", createParam.getCaseId(), createParam.getOrderType());
                return ResultUtils.fail(ErrorCode.SYSTEM_REDIS_LOCK_ERROR);
            }
            return createActual(context);
        } catch (Exception e) {
            log.error("doCreate", e);
        } finally {
            //isLocked():查询lock 是否被任意线程所持有。
            //isHeldByCurrentThread():查询当前线程是否保持此锁定
            if (rLock != null && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        // 创建后逻辑
        return ResultUtils.success(context);
    }

    private String getKey(VonOrderCreateContext wordOrder) {
        final WorkOrderCreateParam createParam = wordOrder.getCreateParam();
        final String uniqueId = createParam.getUniqueId();
        final String uniqueKey = StringUtils.isEmpty(uniqueId) ? String.valueOf(createParam.getCaseId()) : uniqueId;
        return "create_" + uniqueKey + "_" + createParam.getOrderType();
    }

    /**
     * 真正创建逻辑
     */
    @org.jetbrains.annotations.NotNull
    private @NotNull Response<VonOrderCreateContext> createActual(VonOrderCreateContext context) {
        WorkOrderCreateParam createParam = context.getCreateParam();
        int operatorOrgId = organizationService.getUserOrgId(createParam.getOperatorId());
        context.setOperatorOrgId(operatorOrgId);

        BasicWorkOrder basicWorkOrder = new BasicWorkOrder();
        basicWorkOrder.setCaseId(createParam.getCaseId());
        basicWorkOrder.setOperatorId(createParam.getOperatorId());
        basicWorkOrder.setOrderType(createParam.getOrderType());
        basicWorkOrder.setHandleResult(createParam.getHandleResult());
        basicWorkOrder.setOrderlevel(createParam.getOrderlevel());
        basicWorkOrder.setComment(createParam.getComment());
        basicWorkOrder.setOperatorOrgId(operatorOrgId);
        long assignGroupId = assignGroupService.getGroupIdByOrderTypeAndPermission(createParam.getOrderType(),
                createParam.getAssignGroupPermission());
        basicWorkOrder.setAssignGroupId(assignGroupId);
        //插入主表
        orderOperationFacade.createWorkOrder(basicWorkOrder);
        long workOrderId = basicWorkOrder.getId();
        context.setCreateOrderId(workOrderId);

        log.info("doCreate caseId {}, orderId {}, context={}", createParam.getCaseId(), context.getCreateOrderId(), context);

        //插入扩展表
        List<WorkOrderExt> extList = createParam.getWorkOrderExt();
        if (CollectionUtils.isNotEmpty(extList)) {
            extList.forEach(v -> v.setWorkOrderId(workOrderId));
            orderExtService.createWorkOrderExt(extList);
        }

        //工单操作记录
        WorkOrderRecord workOrderRecord = WorkOrderRecord.createRecord(basicWorkOrder);
        this.saveWorkOrderRecord(workOrderRecord);

        //发送工单状态变化事件
        EventPublishHelper.sendOrderStatusChangeOld(this, basicWorkOrder);

        //发送分配工单event
        EventPublishHelper.sendOrderAssign(this, basicWorkOrder.getOrderType());

        // 发送工单创建mq
        WorkOrderResultChangeEvent e = new WorkOrderResultChangeEvent();
        e.setHandleResult(basicWorkOrder.getHandleResult());
        e.setWorkOrderId(workOrderId);
        e.setOrderType(basicWorkOrder.getOrderType());
        Message message = Message.of(MQTag.CF,
                CfClientMQTagCons.WORK_ORDER_CREATE,
                String.valueOf(workOrderId),
                e);
        producer.send(message);

        return ResultUtils.success(context);
    }

    /**
     * 工单操作记录
     */
    private void saveWorkOrderRecord(WorkOrderRecord workOrderRecord) {
        if (workOrderRecord == null) {
            return;
        }
        recordDao.saveRecord(workOrderRecord);
    }

}
