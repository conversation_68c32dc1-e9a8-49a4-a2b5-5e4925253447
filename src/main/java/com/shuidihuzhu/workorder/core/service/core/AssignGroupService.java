package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AssignGroupService {

    @Nullable
    AssignGroupDO getGroupByUserId(long userId, int orderType);

    Response<Void> updateAssignGroupWithPermission(long workOrderId, int orderType, String permission, long operatorId, String remark);

    AssignGroupDO getById(long assignGroupId);

    long getGroupIdByOrderTypeAndPermission(int orderType, String assignGroupPermission);

    List<AssignGroupDO> getListByOrderType(int orderType);
}
