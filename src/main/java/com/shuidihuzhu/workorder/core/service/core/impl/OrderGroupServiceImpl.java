package com.shuidihuzhu.workorder.core.service.core.impl;

import brave.Tracing;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.event.EventPublishHelper;
import com.shuidihuzhu.workorder.core.model.view.GroupStatItemVO;
import com.shuidihuzhu.workorder.core.model.view.GroupStatUserVO;
import com.shuidihuzhu.workorder.core.model.view.StaffStatusStatVO;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.StaffFreeStatService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.VonDoingCountTypeVO;
import com.shuidihuzhu.workorder.repository.WorkOrderRepository;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class OrderGroupServiceImpl implements OrderGroupService {

    @Value("${apollo.von.group-assign.enable:false}")
    private boolean groupEnable;

    @Resource
    private PermissionFeignClient permissionFeignClient;

    @Resource
    private StaffStatusService staffStatusService;

    @Resource
    private BaseWorkOrderService baseWorkOrderService;

    @Resource
    private StaffFreeStatService staffFreeStatService;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private WorkOrderRepository workOrderRepository;

    @Resource
    private VonConfigFacade vonConfigFacade;

    @Autowired
    private Tracing tracing;

    @Value("${auth.saas.appCode:}")
    private String authAppCode;

    @Resource(name = WorkOrderConfig.Async.VON_GROUP_STAT)
    private ExecutorService executorService;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public Set<Integer> getGroupSet() {
        return Sets.newHashSet(getGroupList());
    }

    @Override
    public List<Integer> getGroupList() {
        if (!groupEnable) {
            return Lists.newArrayList();
        }
        int photoCount = workOrderDao.getAllCountByHandleResult(WorkOrderType.ai_photo.getType(), HandleResultEnum.undoing.getType());
        int contentCount = workOrderDao.getAllCountByHandleResult(WorkOrderType.ai_content.getType(), HandleResultEnum.undoing.getType());
        // 如果文章录入工单数量大于图片录入工单数量，则文章录入工单在图片录入工单的前面
        if (contentCount > photoCount) {
            return getGroupListConstB();
        } else {
            return getGroupListConstA();
        }
    }

    @Override
    public List<Integer> getGroupListConst() {
        return getGroupListConstA();
    }

    @NotNull
    private ArrayList<Integer> getGroupListConstA() {
        //            高风险工单>二次审核工单>图片录入工单>文章录入工单>图文审核工单>动态审核工单>下发动态审核工单>UGC工单>UGC投诉工单>顾问评价工单>资金用途审核工单>补充医院信息工单
        return Lists.newArrayList(
                WorkOrderType.highriskshenhe.getType(),
                WorkOrderType.ai_erci.getType(),
                WorkOrderType.ai_photo.getType(),
                WorkOrderType.ai_content.getType(),
                WorkOrderType.content.getType(),
                WorkOrderType.ugcprogress.getType(),
                WorkOrderType.ugcpinglun.getType(),
                WorkOrderType.ugc_complaint_verify.getType(),
                WorkOrderType.consultant_evaluation.getType()
        );
    }

    @NotNull
    private ArrayList<Integer> getGroupListConstB() {
//        图片录入工单<文章录入工单  对换
        return Lists.newArrayList(
                WorkOrderType.highriskshenhe.getType(),
                WorkOrderType.ai_erci.getType(),
                WorkOrderType.ai_content.getType(),
                WorkOrderType.ai_photo.getType(),
                WorkOrderType.content.getType(),
                WorkOrderType.ugcprogress.getType(),
                WorkOrderType.ugcpinglun.getType(),
                WorkOrderType.ugc_complaint_verify.getType(),
                WorkOrderType.consultant_evaluation.getType()
        );
    }

    /**
     * 员工内审组上线，其拥有权限的所有内审工单都上线
     * 下线,暂停亦然
     *
     * @param vonStatusChangeEvent
     */
    @Override
    public void onStaffStatusChange(VonStaffStatusChangePayload vonStatusChangeEvent) {
        int orderType = vonStatusChangeEvent.getOrderType();
        if (orderType != WorkOrderType.nei_shen_group.getType()) {
            return;
        }
        log.info("onStaffStatusChange {}", vonStatusChangeEvent);
        int groupStaffStatus = vonStatusChangeEvent.getStaffStatus();
        // 内审组状态变化
        long operatorId = vonStatusChangeEvent.getOperatorId();
        List<String> permissions = getGroupList().stream()
                .map(workOrderTypeService::getFromOrderTypeCode)
                .map(WorkOrderTypeDO::getPermission)
                .collect(Collectors.toList());
        PermissionParam validPermissionParam = PermissionParam.builder()
                .appCode(authAppCode)
//                .appCode(AuthSaasContext.getAuthAppCode())
                .userId(operatorId)
                .permissions(permissions)
                .build();
        Response<Set<String>> permissionResp = permissionFeignClient.validUserPermissions(validPermissionParam);
        if (permissionResp == null || permissionResp.notOk()) {
            log.warn("调用权限失败");
            return;
        }
        Set<String> validPermission = permissionResp.getData();
        for (String p : validPermission) {
            WorkOrderType t = WorkOrderType.getFromPermission(p);
            StaffStatus s = new StaffStatus();
            s.setUserId(operatorId);
            s.setOrderType(t.getType());
            s.setStaffStatus(groupStaffStatus);
            staffStatusService.changeStatus(s);
        }
        EventPublishHelper.sendOrderAssign(this, orderType);
    }

    @Override
    public Response<List<GroupStatItemVO>> getGroupStatInfo() {
        List<Integer> groupList = getGroupList();
        if (CollectionUtils.isEmpty(groupList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<GroupStatItemVO> result = Lists.newArrayList();
        Map<Integer, WorkOrderTypeDO> typeMap = workOrderTypeService.getAllOrderType().stream()
                .collect(Collectors.toMap(WorkOrderTypeDO::getOrderTypeCode, Function.identity()));
        for (Integer t : groupList) {
            int allCountByHandleResult = baseWorkOrderService.getAllCountByHandleResult(t, HandleResultEnum.undoing.getType(), 0);
            GroupStatItemVO vo = new GroupStatItemVO();
            WorkOrderTypeDO workOrderTypeDO = typeMap.get(t);
            vo.setOrderName(Optional.ofNullable(workOrderTypeDO).map(WorkOrderTypeDO::getMsg).orElse("未知"));
            vo.setOrderType(t);
            vo.setUndoingCount(allCountByHandleResult);
            result.add(vo);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<List<GroupStatUserVO>> getGroupStatUserList(long operatorId, Integer staffStatus, Boolean free) {
        // 筛选查询用户
        List<StaffStatus> users = getUserListFiltered(operatorId, staffStatus, free);
        return getGroupStatInfoByUserList(users);
    }

    @Override
    public Response<Integer> getGroupAutoJumpTab(long userId) {
        List<Integer> groupList = getGroupList();
        if (CollectionUtils.isEmpty(groupList)) {
            return NewResponseUtil.makeSuccess(-1);
        }
        List<VonDoingCountTypeVO> doingList = workOrderDao.getTypeWithCountByUserAndHandleResult(userId,
                groupList, HandleResultEnum.doing.getType());
        if (CollectionUtils.isEmpty(doingList)) {
            return NewResponseUtil.makeSuccess(-1);
        }
        if (CollectionUtils.size(doingList) > 1) {
            return NewResponseUtil.makeSuccess(-1);
        }
        VonDoingCountTypeVO doingCountTypeVO = doingList.get(0);
        log.info("getGroupAutoJumpTab userId {}, data {}", userId, doingCountTypeVO);
        return NewResponseUtil.makeSuccess(doingCountTypeVO.getOrderType());
    }

    @Override
    public List<Long> filterUser(List<Long> users) {
        // 开关关闭不过滤
        if (CollectionUtils.isEmpty(getGroupList())) {
            return users;
        }
        RSet<Long> filterUserSet = getFilterUserSet();
        return users.stream()
                .filter(v -> !filterUserSet.contains(v))
                .collect(Collectors.toList());
    }

    @Override
    public void cleanFilterUser() {
        getFilterUserSet().clear();
    }

    @Override
    public void onOrderHandle(long workOrderId) {
        WorkOrderBase order = workOrderDao.getWorkOrderById(workOrderId);
        if (order == null) {
            return;
        }
        int orderType = order.getOrderType();
        // 不是内审的工单
        if (!getGroupSet().contains(orderType)) {
            return;
        }
        addFilterUser(order.getOperatorId());
    }

    private void addFilterUser(long operatorId) {
        getFilterUserSet().addAsync(operatorId);
    }

    private RSet<Long> getFilterUserSet() {
        String setKey = getFilterUserKey();
        return cf2RedissonHandler.getRedissonClient().getSet(setKey);
    }

    private String getFilterUserKey() {
        return "von-group-assign-filter-users";
    }

    private List<StaffStatus> getUserListFiltered(long operatorId, Integer staffStatus, Boolean free) {
        if (operatorId > 0) {
            StaffStatus s = staffStatusService.getStaffStatus(operatorId, WorkOrderType.nei_shen_group.getType());
            List<StaffStatus> r = Lists.newArrayList();
            if (s != null) {
                r.add(s);
            }
            return r;
        }
        List<StaffStatus> staffs = staffStatusService.getStaffStatusByOrderType(String.valueOf(WorkOrderType.nei_shen_group.getType()));
        if (CollectionUtils.isEmpty(staffs)) {
            return Lists.newArrayList();
        }
        return staffs.stream().filter(v -> filterUser(v, staffStatus, free))
                .collect(Collectors.toList());
    }

    private boolean filterUser(StaffStatus staffStatus, Integer targetStaffStatus, Boolean targetFree) {
        if (targetStaffStatus != null) {
            // 上班状态不符合
            if (staffStatus.getStaffStatus() != targetStaffStatus) {
                return false;
            }
        }
        if (targetFree != null) {
            // 空闲状态不符合
            Boolean free = staffFreeStatService.isFree(staffStatus.getUserId());
            if (free != null && free != targetFree) {
                return false;
            }
        }
        return true;
    }

    private Response<List<GroupStatUserVO>> getGroupStatInfoByUserList(List<StaffStatus> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<Long> userIds = userList.stream().map(StaffStatus::getUserId).collect(Collectors.toList());
        Response<List<AuthUserDto>> userResp = userFeignClient.getAllUserById(userIds);
        if (userResp == null || userResp.notOk() || CollectionUtils.isEmpty(userResp.getData())) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }
        List<AuthUserDto> userInfos = userResp.getData();
        Map<Long, AuthUserDto> userInfoMap = userInfos.stream().collect(Collectors.toMap(AuthUserDto::getUserId, v -> v, (a, b) -> b));

        List<CompletableFuture<GroupStatUserVO>> futureList = userList.stream()
                .map(user -> CompletableFuture.supplyAsync(() -> getStatInfo(userInfoMap, user), executorService))
                .collect(Collectors.toList());
        List<GroupStatUserVO> vos = futureList.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(vos);
    }

    private GroupStatUserVO getStatInfo(Map<Long, AuthUserDto> userInfoMap, StaffStatus user) {
        GroupStatUserVO vo = new GroupStatUserVO();
        long userId = user.getUserId();
        vo.setOperatorId(userId);
        String userName = Optional.ofNullable(userInfoMap.get(userId))
                .map(AuthUserDto::getUserName)
                .orElse("未知");
        vo.setOperatorName(userName);
        vo.setStaffStatus(user.getStaffStatus());

        // 处理中工单
        List<GroupStatUserVO.OrderCountVO> doingOrderCountList = getOrderCountVOS(userId, HandleResultEnum.doing.getType());
        vo.setDoingOrderCountList(doingOrderCountList);

        // 稍后处理工单
        List<GroupStatUserVO.OrderCountVO> laterDoingOrderCountList = getOrderCountVOS(userId, HandleResultEnum.later_doing.getType());
        vo.setLaterDoingOrderCountList(laterDoingOrderCountList);

        // 处理完成工单
        List<GroupStatUserVO.OrderCountVO> doneOrderCountList = getOrderCountVOS(userId, HandleResultEnum.done.getType());
        vo.setDoneOrderCountList(doneOrderCountList);

        // 当前是否空闲
        boolean busy = CollectionUtils.isNotEmpty(vo.getDoingOrderCountList()) || CollectionUtils.isNotEmpty(vo.getLaterDoingOrderCountList());
        boolean free = !busy;
        vo.setFree(user.getStaffStatus() == StaffStatusEnum.online.getType() && free);

        StaffStatusStatVO staffStatusStatVO = staffStatusService.getStatusStatV2(user, userId, WorkOrderType.nei_shen_group.getType());
        // 上线时间
        vo.setFirstOnlineTime(staffStatusStatVO.getFirstOnlineTime());
        // 下班时间
        vo.setLastOfflineTime(staffStatusStatVO.getLastOfflineTime());
        // 上班总时长
        vo.setOnlineSecond(staffStatusStatVO.getOnlineSecond());
        // 休息总时长
        vo.setStopSecond(staffStatusStatVO.getStopSecond());
        // 空闲总时长
        vo.setFreeSecond(staffStatusStatVO.getFreeSecond());
        return vo;
    }

    @NotNull
    private List<GroupStatUserVO.OrderCountVO> getOrderCountVOS(long userId, int handleResult) {
        List<VonDoingCountTypeVO> orderCountTypeList;
        List<Integer> groupList = getGroupList();
        if (CollectionUtils.isEmpty(groupList)) {
            return Lists.newArrayList();
        }
        if (handleResult == HandleResultEnum.done.getType()) {
            // 处理完成只查当天的
            List<Integer> doneList = vonConfigFacade.getAllDoneHandleResultList();
            orderCountTypeList = workOrderRepository.getTodayTypeWithCountByUserAndHandleResult(userId, groupList, doneList);
        } else {
            // 其他状态查历史的
            orderCountTypeList = workOrderDao.getTypeWithCountByUserAndHandleResult(userId,
                    groupList, handleResult);
        }
        ArrayList<GroupStatUserVO.OrderCountVO> doingOrderCountList = Lists.newArrayList();
        for (VonDoingCountTypeVO d : orderCountTypeList) {
            GroupStatUserVO.OrderCountVO v = new GroupStatUserVO.OrderCountVO();
            v.setOrderType(d.getOrderType());
            v.setCount(d.getNum());
            doingOrderCountList.add(v);
        }
        return doingOrderCountList;
    }
}
