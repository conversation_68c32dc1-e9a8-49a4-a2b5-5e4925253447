package com.shuidihuzhu.workorder.core.service.core.impl;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CfWorkOrderCreate;
import com.shuidihuzhu.workorder.core.service.core.OrderOperationFacade;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderOperationFacadeImpl implements OrderOperationFacade {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private StaffStatusService staffStatusService;

    @Resource
    private Analytics analytics;
    @Override
    public int createWorkOrder(WorkOrderBase wordOrder) {
        final int workOrder = workOrderDao.createWorkOrder(wordOrder);
        staffStatusService.onOrderCreate(wordOrder);

        // 工单创建成功埋点上报
        try {
            if (workOrder > 0) {
                CfWorkOrderCreate cfWorkOrderCreate = new CfWorkOrderCreate();
                cfWorkOrderCreate.setWork_order_id(wordOrder.getId());
                cfWorkOrderCreate.setWork_order_type((long) wordOrder.getOrderType());
                cfWorkOrderCreate.setUser_tag(String.valueOf(wordOrder.getCaseId()));
                cfWorkOrderCreate.setUser_tag_type(UserTagTypeEnum.uuid);
                analytics.track(cfWorkOrderCreate);
            }
        } catch (Exception e) {
            log.error("埋点上报失败 ", e);
        }

        return workOrder;
    }
}
