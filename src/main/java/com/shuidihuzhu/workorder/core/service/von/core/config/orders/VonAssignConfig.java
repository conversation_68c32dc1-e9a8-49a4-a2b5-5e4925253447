package com.shuidihuzhu.workorder.core.service.von.core.config.orders;

import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.IVonConfig;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.VonConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@VonConfig("assign")
public class VonAssignConfig implements IVonConfig {

    @ApiModelProperty("使用老版工单分配")
    private Boolean isUseOldAssigner;

    @ApiModelProperty("领单限制数量")
    private Integer assignLimitCount;

    @ApiModelProperty("不给有工单处理中的人分配")
    private Boolean notAssignDoingOperator;

    @ApiModelProperty("每查出一个领单人一次分多少工单")
    private Integer assignCountPearOperator;

    @ApiModelProperty("优先分配给上一次处理该案例该类型工单的操作人 若走一人分配制则每次只能分配一个工单```assignCountPearOperator``` == 1")
    private Boolean priorAssignLastAssign;

    @ApiModelProperty("是否默认自动分配 默认为true 自动分配")
    private Boolean autoAssign;

    @ApiModelProperty("指定优先分配人是否启用 读取vonPriorAssignOperatorId 优先分配")
    private Boolean priorAssignerEnable;

    @ApiModelProperty("最少攒多少工单才会分配一次")
    private Integer minAssignOrderCount;

    @ApiModelProperty("是否在组内无人在线时降级为不分组分配")
    private Boolean downWhenGroupNoOnlineUser;

    @ApiModelProperty("日接单限制是否开启")
    private Boolean dayAssignLimitEnable;

    @ApiModelProperty("组分配时无需校验工单权限白名单")
    private String groupAssignCheckPermissionWhiteList;

    @ApiModelProperty("组分配时需校验工单权限")
    private Boolean groupAssignCheckOrderPermission;

    public boolean valid() {

        // 若走一人分配制则每次只能分配一个工单```assignCountPearOperator``` == 1
        if (priorAssignLastAssign != null && assignCountPearOperator != null) {
            if (priorAssignLastAssign && assignCountPearOperator != 1) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void promoteDefault(int orderType) {

        if (isUseOldAssigner == null) {
            // 老工单都要走老分配
            this.isUseOldAssigner = orderType <= WorkOrderType.pr_return_visit_service.getType();
            if (WorkOrderType.PR_WORK_ORDER_LIST.contains(orderType)) {
                isUseOldAssigner = true;
            }
        }
        if (assignLimitCount == null) {
            assignLimitCount = 5;
        }
        if (notAssignDoingOperator == null) {
            notAssignDoingOperator = false;
        }
        if (assignCountPearOperator == null) {
            assignCountPearOperator = 1;
        }
        if (priorAssignLastAssign == null) {
            priorAssignLastAssign = false;
        }
        if (autoAssign == null) {
            autoAssign = true;
        }
        if (priorAssignerEnable == null) {
            priorAssignerEnable = false;
        }
        if (downWhenGroupNoOnlineUser == null) {
            downWhenGroupNoOnlineUser = false;
        }
        if (dayAssignLimitEnable == null) {
            dayAssignLimitEnable = false;
        }

        if (groupAssignCheckOrderPermission == null) {
            groupAssignCheckOrderPermission = true;
        }
    }

    @Data
    public static class NoticeConfig {

    }
}
