package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface WorkOrderTypeService {

    Response<Boolean> insertWorkOrder(WorkOrderTypeDO workOrderTypeDO);

    WorkOrderTypeDO getFromOrderTypeCode(int orderType);

    List<WorkOrderTypeDO> getAllOrderType();

    /**
     * 根据一级工单类型获取所有二级工单类型id
     */
    List<Integer> getByOneLevel(int oneTypeCode);

    Response<Boolean> insertWorkOrderOneType(WorkOrderOneTypeDO workOrderOneTypeDO);

    Map<Integer, List<Integer>> getClassiyByPermissions(Set<String> permissions);

    List<String> getAllPermissions();

    /**
     * 获取所有一级工单类型（用于添加工单类型时选择展示）
     */
    List<WorkOrderOneTypeDO> getAllOneType();

    /**
     * 根据二级工单类型获取一级工单类型
     */
    Integer getOneFromTwo(Integer orderTypeCode);

    String getPermissionFromOrderTypeCode(int orderTypeCode);
}
