
# 水滴筹-工单平台-分配

### 分配触发逻辑
- 工单创建
- 工单处理
- 员工上下线

### 配置
```java

@ApiModelProperty("使用老版工单分配")
private Boolean isUseOldAssigner;

@ApiModelProperty("领单限制数量")
private Integer assignLimitCount;

@ApiModelProperty("不给有工单处理中的人分配")
private Boolean notAssignDoingOperator;

@ApiModelProperty("每查出一个领单人一次分多少工单")
private Integer assignCountPearOperator;

@ApiModelProperty("优先分配给上一次处理该案例该类型工单的操作人 若走一人分配制则每次只能分配一个工单```assignCountPearOperator``` == 1")
private Boolean priorAssignLastAssign;
```

### 分配方式
#### 一人分配制

优先分配给上一次处理该案例该类型工单的操作人

若走一人分配制则每次只能分配一个工单```assignCountPearOperator``` == 1

配置变量 ```priorAssignLastAssign```