package com.shuidihuzhu.workorder.core.service.core;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import com.shuidihuzhu.workorder.core.model.view.GroupStatItemVO;
import com.shuidihuzhu.workorder.core.model.view.GroupStatUserVO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface OrderGroupService {
    Set<Integer> getGroupSet();

    List<Integer> getGroupList();

    List<Integer> getGroupListConst();

    void onStaffStatusChange(VonStaffStatusChangePayload vonStatusChangeEvent);

    Response<List<GroupStatItemVO>> getGroupStatInfo();

    Response<List<GroupStatUserVO>> getGroupStatUserList(long operatorId, Integer staffStatus, <PERSON>olean free);

    Response<Integer> getGroupAutoJumpTab(long userId);

    List<Long> filterUser(List<Long> users);

    void cleanFilterUser();

    void onOrderHandle(long workOrderId);
}
