package com.shuidihuzhu.workorder.core.service.core.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.model.param.PermissionParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.dao.VonAssignGroupDAO;
import com.shuidihuzhu.workorder.core.dao.write.WorkOrderWriteDAO;
import com.shuidihuzhu.workorder.core.delegate.sea.SeaAuthHelper;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AssignGroupServiceImpl implements AssignGroupService {

    @Resource
    private VonAssignGroupDAO vonAssignGroupDAO;

    @Resource
    private WorkOrderWriteDAO workOrderWriteDAO;

    @Resource
    private BaseWorkOrderService baseWorkOrderService;

    @Resource
    private UserOperationRecordDao recordDao;

    @Nullable
    @Override
    public AssignGroupDO getGroupByUserId(long userId, int orderType) {
        List<AssignGroupDO> list = vonAssignGroupDAO.getListByOrderType(orderType);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<String> permissions = list.stream().map(AssignGroupDO::getPermission).collect(Collectors.toList());

        PermissionParam validPermissionParam = PermissionParam.builder()
                .userId(userId)
                .appCode(SeaAuthHelper.delegate().getAuthAppCode())
                .permissions(permissions)
                .build();
        Response<Set<String>> permissionResp = SeaAuthHelper.delegate().permission().validUserPermissions(validPermissionParam);
        if (permissionResp == null || permissionResp.notOk()) {
            log.warn("调用权限失败 resp {}", permissionResp, new RuntimeException("调用权限失败"));
            return null;
        }
        Set<String> validPermissionSet = permissionResp.getData();
        for (AssignGroupDO d : list) {
            if (validPermissionSet.contains(d.getPermission())){
                return d;
            }
        }
        return null;
    }

    @Override
    public Response<Void> updateAssignGroupWithPermission(long workOrderId, int orderType, String permission, long operatorId, String remark) {
        long groupId = getGroupIdByOrderTypeAndPermission(orderType, permission);
        int res = workOrderWriteDAO.updateAssignGroupIdById(workOrderId, orderType, groupId);
        if (res <= 0) {
            return NewResponseUtil.makeError(ErrorCode.ALREADY_UPDATE_ASSIGN_GROUP);
        }

        // 释放工单
        baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(Lists.newArrayList(workOrderId));

        WorkOrderRecord record = WorkOrderRecord.create(0, workOrderId, orderType, operatorId, remark, OperateMode.upgrade);
        recordDao.saveRecord(record);
        return NewResponseUtil.makeSuccess();
    }

    @Override
    public AssignGroupDO getById(long assignGroupId) {
        return vonAssignGroupDAO.getById(assignGroupId);
    }

    @Override
    public long getGroupIdByOrderTypeAndPermission(int orderType, String assignGroupPermission) {
        Long v = vonAssignGroupDAO.getGroupIdByOrderTypeAndPermission(orderType, assignGroupPermission);
        return v == null ? 0 : v;
    }

    @Override
    public List<AssignGroupDO> getListByOrderType(int orderType) {
        return vonAssignGroupDAO.getListByOrderType(orderType);
    }
}
