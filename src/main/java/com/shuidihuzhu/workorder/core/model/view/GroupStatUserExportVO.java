package com.shuidihuzhu.workorder.core.model.view;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GroupStatUserExportVO {

    @ApiModelProperty("员工ID")
    private long operatorId;

    @ApiModelProperty("员工姓名")
    private String operatorName;

    @ApiModelProperty("员工工作状态")
    private int staffStatus;
//
//    @ApiModelProperty("处理中一级工单类型")
//    private String doingOneLevelOrderTypeMsg;
//
//    @ApiModelProperty("处理中二级工单类型")
//    private String doingTwoLevelOrderTypeMsg;

    @ApiModelProperty("此刻是否空闲")
    private boolean free;

    @ApiModelProperty("处理中量（员工当前处理中的工单量）")
    private List<OrderCountVO> doingOrderCountList;

    @ApiModelProperty("稍后处理量（员工当前稍后处理的工单量）")
    private List<OrderCountVO> laterDoingOrderCountList;

    @ApiModelProperty("处理完成的工单量（员工今天截止到当前的处理完成工单量）")
    private List<OrderCountVO> doneOrderCountList;

    @ApiModelProperty("上线时间：员工今天首次操作“上班”的时间")
    private Date firstOnlineTime;

    @ApiModelProperty("下班时间：员工今天最后一次操作“下班”的时间")
    private Date lastOfflineTime;

    @ApiModelProperty("上班总时长 秒")
    private int onlineSecond;

    @ApiModelProperty("休息总时长 秒")
    private int stopSecond;

    @ApiModelProperty("空闲总时长 秒")
    private int freeSecond;

    @Data
    public static class OrderCountVO{

        @ApiModelProperty("工单类型")
        private int orderType;

        @ApiModelProperty("工单数量")
        private int count;
    }
}
