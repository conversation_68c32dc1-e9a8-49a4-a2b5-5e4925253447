package com.shuidihuzhu.workorder.core.model.view;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class StaffStatusStatVO {

    @ApiModelProperty("上线时间：员工今天首次操作“上班”的时间")
    private Date firstOnlineTime;

    @ApiModelProperty("下班时间：员工今天最后一次操作“下班”的时间")
    private Date lastOfflineTime;

    @ApiModelProperty("上班总时长 秒")
    private int onlineSecond;

    @ApiModelProperty("休息总时长 秒")
    private int stopSecond;

    @ApiModelProperty("空闲总时长 秒")
    private int freeSecond;

}
