package com.shuidihuzhu.workorder.core.model.view;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.shuidihuzhu.workorder.core.model.convertor.BooleanTextConverter;
import com.shuidihuzhu.workorder.core.model.convertor.ExcelSecond2durationConvertor;
import com.shuidihuzhu.workorder.core.model.convertor.ExcelStaffStatusConvertor;
import com.shuidihuzhu.workorder.core.model.convertor.GroupStatOrderCountConvertor;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class GroupStatUserVO {

    private static final int COLUMN_4 = 30;
    private static final int COLUMN_3 = 10;
    private static final int COLUMN_2 = 10;

    @ExcelProperty("员工ID")
    @ApiModelProperty("员工ID")
    private long operatorId;

    @ExcelProperty("员工姓名")
    @ApiModelProperty("员工姓名")
    private String operatorName;

    @ExcelProperty(value = "员工工作状态", converter = ExcelStaffStatusConvertor.class)
    @ApiModelProperty("员工工作状态")
    private int staffStatus;
//
//    @ApiModelProperty("处理中一级工单类型")
//    private String doingOneLevelOrderTypeMsg;
//
//    @ApiModelProperty("处理中二级工单类型")
//    private String doingTwoLevelOrderTypeMsg;

    @ExcelProperty(value = "此刻是否空闲", converter = BooleanTextConverter.class)
    @ApiModelProperty("此刻是否空闲")
    private boolean free;

    @ColumnWidth(COLUMN_4)
    @ExcelProperty(value = "处理中量", converter = GroupStatOrderCountConvertor.class)
    @ApiModelProperty("处理中量（员工当前处理中的工单量）")
    private List<OrderCountVO> doingOrderCountList;

    @ColumnWidth(COLUMN_4)
    @ExcelProperty(value = "稍后处理量", converter = GroupStatOrderCountConvertor.class)
    @ApiModelProperty("稍后处理量（员工当前稍后处理的工单量）")
    private List<OrderCountVO> laterDoingOrderCountList;

    @ColumnWidth(COLUMN_4)
    @ExcelProperty(value = "处理完成量", converter = GroupStatOrderCountConvertor.class)
    @ApiModelProperty("处理完成的工单量（员工今天截止到当前的处理完成工单量）")
    private List<OrderCountVO> doneOrderCountList;

    @ColumnWidth(COLUMN_3)
    @ExcelProperty("上班时间")
    @DateTimeFormat("HH:mm")
    @ApiModelProperty("上班时间：员工今天首次操作“上班”的时间")
    private Date firstOnlineTime;

    @ColumnWidth(COLUMN_3)
    @DateTimeFormat("HH:mm")
    @ExcelProperty("下班时间")
    @ApiModelProperty("下班时间：员工今天最后一次操作“下班”的时间")
    private Date lastOfflineTime;

    @ColumnWidth(COLUMN_2)
    @ExcelProperty(value = "上班总时长", converter = ExcelSecond2durationConvertor.class)
    @ApiModelProperty("上班总时长 秒")
    private int onlineSecond;

    @ColumnWidth(COLUMN_2)
    @ExcelProperty(value = "休息总时长", converter = ExcelSecond2durationConvertor.class)
    @ApiModelProperty("休息总时长 秒")
    private int stopSecond;

    @ColumnWidth(COLUMN_2)
    @ExcelProperty(value = "空闲总时长", converter = ExcelSecond2durationConvertor.class)
    @ApiModelProperty("空闲总时长 秒")
    private int freeSecond;

    @Data
    public static class OrderCountVO{

        @ApiModelProperty("工单类型")
        private int orderType;

        @ApiModelProperty("工单数量")
        private int count;
    }
}
