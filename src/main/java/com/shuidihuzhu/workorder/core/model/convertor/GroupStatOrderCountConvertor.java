package com.shuidihuzhu.workorder.core.model.convertor;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.google.common.collect.Lists;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeServiceHolder;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.core.model.view.GroupStatUserVO;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;

public class GroupStatOrderCountConvertor implements Converter<Object> {

    @Override
    public Class supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Object convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(Object value, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        List<GroupStatUserVO.OrderCountVO> list = (List<GroupStatUserVO.OrderCountVO>) value;
        List<String> l = Lists.newArrayList();
        for (GroupStatUserVO.OrderCountVO i : list) {
            WorkOrderTypeDO workOrderTypeDO = WorkOrderTypeServiceHolder.get().getFromOrderTypeCode(i.getOrderType());
            l.add(workOrderTypeDO.getMsg() + "：" + i.getCount());
        }
        String join = StringUtils.join(l, "\n");

        return new CellData<>(join);
    }
}