package com.shuidihuzhu.workorder.core.model.von;

import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VonOrderCreateContext {

    //    --------------- basic param start
    private WorkOrderCreateParam createParam;
    private int operatorOrgId;

    //    --------------- result start
    private long createOrderId;

    @ApiModelProperty("是否校验通过")
    private Boolean validPassed;
    //    --------------- result end

//    --------------- basic param end

//    --------------- valid param start
//    --------------- valid param end

    public static VonOrderCreateContext createParam(WorkOrderCreateParam param){
        VonOrderCreateContext v = new VonOrderCreateContext();
        v.setCreateParam(param);
        return v;
    }

}
