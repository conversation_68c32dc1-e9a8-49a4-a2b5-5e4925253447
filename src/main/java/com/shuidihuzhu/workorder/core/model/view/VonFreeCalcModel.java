package com.shuidihuzhu.workorder.core.model.view;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class VonFreeCalcModel {

    @ApiModelProperty("记录id")
    private long recordId;

    @ApiModelProperty("总计空闲时长")
    private long totalFreeDuration;

    @ApiModelProperty("是否空闲")
    private boolean free;

    @ApiModelProperty("空闲开始时间")
    private long freeStartTime;

}
