package com.shuidihuzhu.workorder.core.event.mq.payload;

import com.shuidihuzhu.workorder.model.enums.OperateMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VonOrderStatusChangePayload {

    private long workOrderId;
    private int orderType;
    private int sourceHandleResult;
    private int targetHandleResult;

    private long operatorId;
    private OperateMode operateMode;
    private String operateMsg;
    private String operateComment;

}
