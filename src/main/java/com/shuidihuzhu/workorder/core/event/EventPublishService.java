package com.shuidihuzhu.workorder.core.event;

import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class EventPublishService {

    @Autowired
    ApplicationContext applicationContext;

    public void send(ApplicationEvent event){
        applicationContext.publishEvent(event);
    }

    @Async(WorkOrderConfig.Async.LocalCache)
    public void sendAsync(ApplicationEvent event){
        applicationContext.publishEvent(event);
    }

}
