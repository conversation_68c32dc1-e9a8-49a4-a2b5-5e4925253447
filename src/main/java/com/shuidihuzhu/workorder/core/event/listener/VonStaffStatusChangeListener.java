package com.shuidihuzhu.workorder.core.event.listener;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.event.model.VonStaffStatusChangeEvent;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class VonStaffStatusChangeListener {

    @Autowired
    private OrderGroupService orderGroupService;

    /**
     * 员工在线状态变化事件
     */
    @EventListener(classes = VonStaffStatusChangeEvent.class)
    public void onVonStaffStatusChangeEvent(VonStaffStatusChangeEvent event) {
        log.info("onVonStaffStatusChangeEvent data {}", event);
        VonStaffStatusChangePayload payload = event.getPayload();

        StopWatch stopWatch = StopWatch.createStarted();
        orderGroupService.onStaffStatusChange(payload);

        MaliMQComponent.builder()
                .setTags(MQTag.VON_STAFF_STATUS_CHANGE)
                .setPayload(JSON.toJSONString(payload))
                .send();
        stopWatch.stop();
        log.info("onVonStaffStatusChangeEvent send end {}", stopWatch.getTime());
    }
}
