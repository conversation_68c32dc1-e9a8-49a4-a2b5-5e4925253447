package com.shuidihuzhu.workorder.core.event.mq.payload;

import lombok.*;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class VonStaffStatusChangePayload {

    private long operatorId;
    private int orderType;
    private int staffStatus;

    public VonStaffStatusChangePayload(long operatorId, int orderType, int staffStatus) {
        this.operatorId = operatorId;
        this.orderType = orderType;
        this.staffStatus = staffStatus;
    }
}
