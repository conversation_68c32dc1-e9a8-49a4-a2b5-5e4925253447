package com.shuidihuzhu.workorder.core.event.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonOrderStatusChangePayload;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.StaffFreeStatService;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(
        id = MQTag.VON_ORDER_STATUS_CHANGE,
        topic = MQTopicCons.CF,
        tags = MQTag.VON_ORDER_STATUS_CHANGE,
        group = "von-" + MQTag.VON_ORDER_STATUS_CHANGE
)
@Slf4j
public class VonOrderStatusChangeConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Resource
    private StaffFreeStatService staffFreeStatService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String payload = consumerMessage.getPayload();
        VonOrderStatusChangePayload data = JSON.parseObject(payload, VonOrderStatusChangePayload.class);

        staffFreeStatService.onOrderStatusChange(data.getWorkOrderId());

        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 1;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
