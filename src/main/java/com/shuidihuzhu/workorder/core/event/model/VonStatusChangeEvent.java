package com.shuidihuzhu.workorder.core.event.model;

import com.shuidihuzhu.workorder.core.event.mq.payload.VonOrderStatusChangePayload;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class VonStatusChangeEvent extends ApplicationEvent {

    private final VonOrderStatusChangePayload payload;

    public VonStatusChangeEvent(Object source, VonOrderStatusChangePayload payload){
        super(source);
        this.payload = payload;
    }
}
