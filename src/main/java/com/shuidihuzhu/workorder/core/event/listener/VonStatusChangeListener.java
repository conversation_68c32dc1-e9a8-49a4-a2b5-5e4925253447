package com.shuidihuzhu.workorder.core.event.listener;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.core.event.model.VonStatusChangeEvent;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonOrderStatusChangePayload;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.event.WorkOrderStatusEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class VonStatusChangeListener {

    @Autowired
    private UserOperationRecordDao recordDao;

    @Autowired
    private WorkOrderDao workOrderDao;

    /**
     * 工单状态变化事件
     */
    @EventListener(classes = VonStatusChangeEvent.class)
    public void onVonStatusChangeEvent(VonStatusChangeEvent event) {
        log.info("onVonStatusChangeEvent data {}", event);
        VonOrderStatusChangePayload payload = event.getPayload();

        MaliMQComponent.builder()
                .setTags(MQTag.VON_ORDER_STATUS_CHANGE)
                .setPayload(JSON.toJSONString(payload))
                .send();

        long workOrderId = payload.getWorkOrderId();


        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);

        WorkOrderRecord record = new WorkOrderRecord();
        record.setCaseId(workOrder.getCaseId());
        record.setWorkOrderId(workOrderId);
        record.setWorkOrderType(workOrder.getOrderType());
        record.setOperatorId(payload.getOperatorId());
        record.setOperateMode(payload.getOperateMode().getType());
        record.setOperateDesc(payload.getOperateMsg());
        record.setComment(payload.getOperateComment());
        recordDao.saveRecord(record);
    }

    @EventListener(classes = WorkOrderStatusEvent.class)
    public void onVonStatusChangeEventOld(WorkOrderStatusEvent vonStatusChangeEvent) {
        log.info("onVonStatusChangeEventOld data {}", vonStatusChangeEvent);

        MaliMQComponent.builder()
                .setTags(MQTag.VON_ORDER_STATUS_CHANGE_OLD)
                .setPayload(JSON.toJSONString(vonStatusChangeEvent.getWorkOrderBase()))
                .send();

    }
}
