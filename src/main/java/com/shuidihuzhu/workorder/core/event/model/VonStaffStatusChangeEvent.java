package com.shuidihuzhu.workorder.core.event.model;

import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class VonStaffStatusChangeEvent extends ApplicationEvent {

    private VonStaffStatusChangePayload payload;

    public VonStaffStatusChangeEvent(Object source, VonStaffStatusChangePayload payload){
        super(source);
        this.payload = payload;
    }
}
