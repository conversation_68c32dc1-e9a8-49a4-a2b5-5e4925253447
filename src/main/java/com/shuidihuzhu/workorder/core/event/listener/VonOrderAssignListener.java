package com.shuidihuzhu.workorder.core.event.listener;

import com.shuidihuzhu.cf.enhancer.mq.MaliMQComponent;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class VonOrderAssignListener {

    @Autowired
    private OrderAssignFacade orderAssignFacade;

    @Value("${apollo.von.order.assign.mq.enable:true}")
    private boolean mqEnable;

    /**
     * 自动分单事件
     */
    @EventListener(classes = AssignateWorkOrderEvent.class)
    public void onVonOrderAssign(AssignateWorkOrderEvent assignateWorkOrderEvent) {
        final int orderType = assignateWorkOrderEvent.getOrderType();
        log.info("onVonOrderAssign data {}", orderType);
        if (mqEnable) {
            MaliMQComponent.builder()
                    .setTags(MQTag.VON_ORDER_ASSIGN_TRIGGER)
                    .setPayload(orderType)
                    .send();
        } else {
            orderAssignFacade.onAssignEvent(orderType);
        }
    }

}
