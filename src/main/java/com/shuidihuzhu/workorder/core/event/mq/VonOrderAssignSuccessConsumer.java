package com.shuidihuzhu.workorder.core.event.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.service.core.AssignStatService;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(
        id = MQTag.VON_ORDER_ASSIGN_SUCCESS,
        topic = MQTopicCons.CF,
        tags = MQTag.VON_ORDER_ASSIGN_SUCCESS,
        group = "von-" + MQTag.VON_ORDER_ASSIGN_SUCCESS
)
@Slf4j
public class VonOrderAssignSuccessConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Autowired
    private AssignStatService assignStatService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 2;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
