package com.shuidihuzhu.workorder.core.event.mq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.core.service.core.StaffFreeStatService;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(
        id = MQTag.VON_ORDER_STATUS_CHANGE_OLD,
        topic = MQTopicCons.CF,
        tags = MQTag.VON_ORDER_STATUS_CHANGE_OLD,
        group = "von-" + MQTag.VON_ORDER_STATUS_CHANGE_OLD
)
@Slf4j
public class VonOrderStatusChangeOldConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Resource
    private StaffFreeStatService staffFreeStatService;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String payload = consumerMessage.getPayload();
        BasicWorkOrder workOrder = JSON.parseObject(payload, BasicWorkOrder.class);

        staffFreeStatService.onOrderStatusChange(workOrder.getId());

        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 1;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
