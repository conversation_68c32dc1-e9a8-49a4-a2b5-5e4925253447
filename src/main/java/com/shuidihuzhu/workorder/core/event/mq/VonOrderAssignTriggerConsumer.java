package com.shuidihuzhu.workorder.core.event.mq;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.MQTag;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(
        id = MQTag.VON_ORDER_ASSIGN_TRIGGER,
        topic = MQTopicCons.CF,
        tags = MQTag.VON_ORDER_ASSIGN_TRIGGER,
        group = "von-" + MQTag.VON_ORDER_ASSIGN_TRIGGER
)
@Slf4j
public class VonOrderAssignTriggerConsumer extends MaliBaseMQConsumer<String> implements MessageListener<String> {

    @Autowired
    private OrderAssignFacade orderAssignFacade;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        final String payload = consumerMessage.getPayload();
        orderAssignFacade.onAssignEvent(Integer.parseInt(payload));
        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 0;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

    @Override
    protected boolean isDiscardable() {
        return true;
    }
}
