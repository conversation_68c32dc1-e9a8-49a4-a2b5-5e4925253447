package com.shuidihuzhu.workorder.core.event;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.workorder.core.event.model.VonStaffStatusChangeEvent;
import com.shuidihuzhu.workorder.core.event.model.VonStatusChangeEvent;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonOrderStatusChangePayload;
import com.shuidihuzhu.workorder.core.event.mq.payload.VonStaffStatusChangePayload;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.model.event.AssignateWorkOrderEvent;
import com.shuidihuzhu.workorder.model.event.WorkOrderStatusEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class EventPublishHelper {

    @Autowired
    private EventPublishService eventPublishService;

    private static EventPublishService sEventPublishService;

    @PostConstruct
    public void init(){
        sEventPublishService = eventPublishService;
    }

    public static EventPublishService service(){
        return sEventPublishService;
    }

    public static void sendStatic(ApplicationEvent event){
        service().send(event);
    }

    public static void sendStaffStatusChange(Object source, long operatorId, int orderType, int staffStatus){
        VonStaffStatusChangePayload e = new VonStaffStatusChangePayload(operatorId, orderType, staffStatus);
        sendStatic(new VonStaffStatusChangeEvent(source, e));
    }

    public static void sendOrderStatusChangeOld(Object source, WorkOrderBase base) {
        WorkOrderStatusEvent e = new WorkOrderStatusEvent(source, base);
        sendStatic(e);
    }

    public static void sendOrderAssign(Object source, int orderType) {
        AssignateWorkOrderEvent e = new AssignateWorkOrderEvent(source, orderType);
        sendStatic(e);
    }

    public void send(ApplicationEvent event){
        service().send(event);
    }

    public void sendOrderStatusChange(Object source,
                                      long workOrderId,
                                      int orderType,
                                      int sourceHandleResult,
                                      int targetHandleResult,
                                      long operatorId,
                                      OperateMode operateMode,
                                      String operateMsg,
                                      String operateComment
    ) {
        VonOrderStatusChangePayload payload =
                new VonOrderStatusChangePayload(workOrderId, orderType, sourceHandleResult, targetHandleResult, operatorId, operateMode, operateMsg, operateComment);
        VonStatusChangeEvent event = new VonStatusChangeEvent(source, payload);
        send(event);
    }
}
