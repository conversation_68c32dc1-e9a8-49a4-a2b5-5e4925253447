package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.MAIN)
public interface VonPermissionDisableDAO {

    List<String> getDisablePermissionByOperatorId(@Param("operatorId") long operatorId);

    int removeDisable(@Param("operatorId") long operatorId, @Param("permission") String permission);

    int disable(@Param("operatorId") long operatorId, @Param("permission") String permission);

    boolean isDisable(@Param("operatorId") long operatorId, @Param("permission") String permission);

    @DataSource(WorkOrderConfig.Ds.SLAVE)
    List<Long> getDisableList(@Param("userIds") List<Long> userIds, @Param("permission") String permission);
}
