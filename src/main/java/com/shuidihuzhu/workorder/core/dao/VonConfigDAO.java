package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.MAIN)
public interface VonConfigDAO {

    String get(@Param("namespace") String namespace, @Param("key") String key);

    int save(@Param("namespace") String namespace, @Param("key") String key, @Param("value") String value);

    List<VonConfigDO> getAllByNamespace(@Param("namespace") String namespace);
}
