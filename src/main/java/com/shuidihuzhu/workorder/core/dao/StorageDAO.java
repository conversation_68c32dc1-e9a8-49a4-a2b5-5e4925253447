package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.model.domain.StorageDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.SLAVE)
public interface StorageDAO {

    StorageDO getLastByType(@Param("workOrderId") long workOrderId, @Param("type") int type);

    List<StorageDO> getListByType(@Param("workOrderId") long workOrderId, @Param("type") int type);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    int add(StorageDO d);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    int addBatch(@Param("list") List<VonStorageVO> list);

    @DataSource(WorkOrderConfig.Ds.MAIN)
    int updateByTypeValue(@Param("workOrderId") long workOrderId, @Param("type") int type, @Param("value") String value);

    List<StorageDO> getAllByOrderIds(@Param("workOrderIds") List<Long> workOrderIds);
}
