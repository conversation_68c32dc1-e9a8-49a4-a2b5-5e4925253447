package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.core.model.von.AssignGroupDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.MAIN)
public interface VonAssignGroupDAO {

    Long getGroupIdByOrderTypeAndPermission(@Param("orderType") int orderType, @Param("permission") String permission);

    String getPermissionByGroupId(@Param("id") long id);

    AssignGroupDO getById(@Param("id") long id);

    List<AssignGroupDO> getListByOrderType(@Param("orderType") int orderType);
}
