package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.SLAVE)
public interface WorkOrderReadDAO {

    List<BasicWorkOrder> getListByIds(@Param("workOrderIds") List<Long> workOrderIds);

    BasicWorkOrder getById(@Param("workOrderId") Long workOrderId);
}
