package com.shuidihuzhu.workorder.core.dao.write;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.MAIN)
public interface WorkOrderWriteDAO {

    int updateHandleResultByIds(@Param("orderIds") List<Long> orderIds, @Param("handleResult") int handleResult);

    int updateAssignGroupIdById(@Param("workOrderId") long workOrderId,
                                @Param("orderType") int orderType,
                                @Param("assignGroupId") long assignGroupId);

}
