package com.shuidihuzhu.workorder.core.dao;

import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource(WorkOrderConfig.Ds.MAIN)
public interface VonOrderConfigDAO {

    String getByOrderType(@Param("orderType") int orderType);

    int saveConfig(@Param("orderType") int orderType, @Param("content") String content);
}
