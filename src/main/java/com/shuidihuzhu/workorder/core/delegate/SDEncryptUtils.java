package com.shuidihuzhu.workorder.core.delegate;

import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 【加密用老加密，解密用新解密】
 * <AUTHOR>
 */
@Component
public class SDEncryptUtils {

    private static ShuidiCipher sShuidiCipher;

    private static OldShuidiCipher sOldShuidiCipher;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @PostConstruct
    public void init(){
        sShuidiCipher = shuidiCipher;
        sOldShuidiCipher = oldShuidiCipher;
    }

    public static String encrypt(String content){
        return sOldShuidiCipher.aesEncrypt(content);
    }

    public static String decrypt(String content){
        return sShuidiCipher.decrypt(content);
    }


}
