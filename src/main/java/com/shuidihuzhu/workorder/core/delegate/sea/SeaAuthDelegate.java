package com.shuidihuzhu.workorder.core.delegate.sea;

import com.shuidihuzhu.client.auth.saas.feign.PermissionFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class SeaAuthDelegate {

    @Getter
    @Value("${auth.saas.appCode:}")
    private String authAppCode;

    @Resource
    private PermissionFeignClient permissionFeignClient;

    @Getter
    @Resource
    private UserFeignClient userFeignClient;

    public PermissionFeignClient permission() {
        return permissionFeignClient;
    }
}
