package com.shuidihuzhu.workorder.core.delegate.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserInfoServiceClient;
import com.shuidihuzhu.workorder.core.delegate.UserInfoDelegate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-05-12 11:34 AM
 **/
@Component
public class UserInfoDelegateImpl implements UserInfoDelegate {

    @Resource
    private UserInfoServiceClient userInfoServiceClient;

    private UserInfoServiceClient getClient() {
        return userInfoServiceClient;
    }

    @Override
    public UserInfoModel getUserInfoByCryptoMobile(String aesEncryptMobile) {
        return getClient().getUserInfoByCryptoMobile(aesEncryptMobile);
    }

    @Override
    public UserInfoModel getUserInfoByMobile(String mobile) {
        return getClient().getUserInfoByMobile(mobile);
    }

    @Override
    public UserInfoModel getUserInfoByUserId(long userId) {
        return getClient().getUserInfoByUserId(userId);
    }
}
