package com.shuidihuzhu.workorder.core.delegate.sea;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class SeaAuthHelper {

    private final SeaAuthDelegate seaAuthDelegate;

    private static SeaAuthDelegate sSeaAuthDelegate;

    public SeaAuthHelper(SeaAuthDelegate seaAuthDelegate) {
        this.seaAuthDelegate = seaAuthDelegate;
    }

    @PostConstruct
    public void init(){
        sSeaAuthDelegate = seaAuthDelegate;
    }

    public static SeaAuthDelegate delegate(){
        return sSeaAuthDelegate;
    }

}
