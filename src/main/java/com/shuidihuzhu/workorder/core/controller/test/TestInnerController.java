package com.shuidihuzhu.workorder.core.controller.test;

import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.OrderAssignFacade;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("innerapi/admin/order/test")
public class TestInnerController {

    @Autowired
    private OrderAssignFacade orderAssignFacade;

    @PostMapping("test-assign")
    public Response<Void> testAssign(@RequestParam int orderType){
        return orderAssignFacade.doAssignate(orderType);
    }

    @PostMapping("service-exception")
    public Response<Void> testServiceException(@RequestParam(required = false) String msg){
        throw ServiceResponseException.create(msg, ErrorCode.SYSTEM_PARAM_ERROR);
    }
}
