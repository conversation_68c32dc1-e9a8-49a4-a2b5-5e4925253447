package com.shuidihuzhu.workorder.core.controller.core;

import com.shuidihuzhu.client.cf.workorder.core.VonAssignFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.core.AssignGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class VonAssignFeignController implements VonAssignFeignClient {

    @Autowired
    private AssignGroupService assignGroupService;

    @Override
    public Response<Void> updateAssignGroupWithPermission(long workOrderId, int orderType, String permission, long operatorId, String remark) {
        return assignGroupService.updateAssignGroupWithPermission(workOrderId, orderType, permission, operatorId, remark);
    }
}
