package com.shuidihuzhu.workorder.core.controller.admin;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.apache.commons.collections4.CollectionUtils;


import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("admin/cf/work-order")
@RestController
@Slf4j
public class WorkOrderTypeController {
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @RequiresPermission("order-type:config")
    @ApiOperation("新增二级工单类型")
    @RequestMapping(path = "insert-work-order-type", method = RequestMethod.POST)
    public Response<Boolean> insertWorkOrderType(WorkOrderTypeDO workOrderTypeDO) {
        return workOrderTypeService.insertWorkOrder(workOrderTypeDO);
    }

    @RequiresPermission("order-type:config")
    @ApiOperation("新增一级工单类型")
    @RequestMapping(path = "insert-work-order-one-type", method = RequestMethod.POST)
    public Response<Boolean> insertWorkOrderOneType(WorkOrderOneTypeDO workOrderOneTypeDO) {
        return workOrderTypeService.insertWorkOrderOneType(workOrderOneTypeDO);
    }

    @RequiresPermission("order-type:config")
    @ApiOperation("根据orderTypeCode查询工单类型信息")
    @RequestMapping(path = "get-by-order-type-code", method = RequestMethod.POST)
    public Response<WorkOrderTypeRecord> getByOrderTypeCode(int orderType) {

        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if(workOrderTypeDO == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        // 转换
        WorkOrderTypeRecord workOrderTypeRecord = new WorkOrderTypeRecord();
        BeanUtils.copyProperties(workOrderTypeDO, workOrderTypeRecord);
        return NewResponseUtil.makeSuccess(workOrderTypeRecord);
    }

    @RequiresPermission("order-type:config")
    @ApiOperation("查询所有二级工单类型信息")
    @RequestMapping(path = "get-all-order-type", method = RequestMethod.POST)
    public Response<List<WorkOrderTypeDO>> getAllOrderType() {

        List<WorkOrderTypeDO> workOrderTypeDO = workOrderTypeService.getAllOrderType();
        if(CollectionUtils.isEmpty(workOrderTypeDO)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        return NewResponseUtil.makeSuccess(workOrderTypeDO);
    }

    @RequiresPermission("order-type:config")
    @ApiOperation("查询所有一级工单类型信息（只用于新增工单类型时选择工单类型的展示）")
    @RequestMapping(path = "get-all-one-type", method = RequestMethod.POST)
    public Response<List<WorkOrderOneTypeDO>> getAllOrderOneType() {

        List<WorkOrderOneTypeDO> workOrderOneTypeDO = workOrderTypeService.getAllOneType();
        if(CollectionUtils.isEmpty(workOrderOneTypeDO)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        return NewResponseUtil.makeSuccess(workOrderOneTypeDO);
    }
}
