package com.shuidihuzhu.workorder.core.controller.admin;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigService;
import com.shuidihuzhu.workorder.model.enums.VonConfigKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RequestMapping("admin/cf/work-order/config")
@RestController
@Slf4j
public class VonConfigAdminController {

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @RequiresPermission("von:config")
    @PostMapping("get-by-order-type")
    public Response<List<VonConfigDO>> getByOrderType(@RequestParam() int orderType){
        final List<VonConfigDO> config = vonConfigFacade.getAllByOrderType(orderType);
        return NewResponseUtil.makeSuccess(config);
    }

    @RequiresPermission("von:config")
    @PostMapping("save-by-map")
    public Response<Object> saveByMap(@RequestParam() int orderType, @RequestBody Map<String, String> configMap){
        vonConfigFacade.saveByMap(orderType, configMap);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("von:config")
    @PostMapping("save-by-order-type")
    public Response<Object> saveByOrderType(@RequestParam() int orderType,
                                            @RequestParam("key") String key,
                                            @RequestParam("value") String value){
        vonConfigFacade.saveConfig(orderType, key, value);
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("von:config-order-pu-tong-doing-limit")
    @PostMapping("save-order-pu-tong-doing-limit")
    public Response<Object> savePuTongDoingLimit(@RequestParam("doingLimit") int doingLimit){
        vonConfigFacade.saveConfig(
                WorkOrderType.cailiao_5.getType(),
                "assign.assignLimitCount",
                String.valueOf(doingLimit)
        );
        return NewResponseUtil.makeSuccess(null);
    }

    @RequiresPermission("von:config-order-pu-tong-doing-limit")
    @PostMapping("get-order-pu-tong-doing-limit")
    public Response<Object> getPuTongDoingLimit(){
        final Integer value = vonConfigFacade.getIntegerProperty(WorkOrderType.cailiao_5.getType(),
                "assign.assignLimitCount");
        return NewResponseUtil.makeSuccess(value);
    }

    @RequiresPermission("von:config")
    @PostMapping("get-config-enum")
    public Response<List<Map<String, String>>> getConfigEnum() {
        List<Map<String, String>> list = VonConfigKey.list;
        return NewResponseUtil.makeSuccess(list);
    }

    /**
     * todo delete 洗完数据删除
     */
    @PostMapping("reload-data")
    public Response<Void> reloadData(String password){
        if (!StringUtils.equals("4eY5mkMGE67T", password)) {
            return NewResponseUtil.makeFail("不对");
        }
        return vonConfigFacade.process();
    }

}
