package com.shuidihuzhu.workorder.core.controller.core;

import com.shuidihuzhu.client.cf.workorder.v2.client.WorkOrderStatV2FeignClient;
import com.shuidihuzhu.client.cf.workorder.v2.model.view.WorkOrderStatVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.StatService;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class WorkOrderStatV2FeignController implements WorkOrderStatV2FeignClient {

    @Autowired
    private StatService statService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Override
    public Response<List<WorkOrderStatVO>> list(int oneLevel, String twoLevel, long userId) {
        return statService.list(oneLevel, twoLevel, userId);
    }

    @Override
    public Response<List<Integer>> listCaseIdsByTypeAndCount(List<Integer> caseIds,
                                                             Integer orderType, Integer count) {
        List<Integer> caseList = baseWorkOrderService.listCaseIdsByTypeAndCount(caseIds, orderType, count);
        return NewResponseUtil.makeSuccess(caseList);
    }
}
