package com.shuidihuzhu.workorder.core.controller.admin;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.workorder.core.model.view.OrderPermissionInfoVO;
import com.shuidihuzhu.workorder.core.model.view.StaffSelectVO;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RequestMapping("admin/cf/work-order/staff")
@RestController
public class VonStaffAdminController {

    @Autowired
    private StaffStatusService staffStatusService;

    @ApiOperation("根据工单类型查询所有用户")
    @RequiresPermission("staff:get-staff-list")
    @RequestMapping(path = "get-all-staff-list",method = RequestMethod.POST)
    public Response<List<StaffSelectVO>> getAllStaffOfSelect(@RequestParam String orderType) {
        return staffStatusService.getAllStaffOfSelect(orderType);
    }

}
