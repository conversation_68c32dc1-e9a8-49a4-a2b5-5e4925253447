package com.shuidihuzhu.workorder.core.controller.admin;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.model.vo.StaffStatusVO;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组分配接口
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@RequestMapping("admin/cf/work-order/group-assign")
@RestController
public class VonGroupAssignController {

    @Value("${apollo.von.group-assign.enable:false}")
    private boolean groupEnable;

    @Autowired
    private OrderGroupService orderGroupService;

    @Autowired
    private StaffStatusService staffStatusService;

    /**
     * 返回组分配开关
     */
    @ApiOperation("返回组分配开关")
    @PostMapping("group-enable")
    public Response<Boolean> groupEnable(){
        return NewResponseUtil.makeSuccess(groupEnable);
    }

    @ApiOperation("查询组分配应该自动跳转的工单类型 为-1时不跳转")
    @PostMapping("get-group-auto-jump-tab")
    public Response<Integer> getGroupAutoJumpTab(){
        long userId = ContextUtil.getAdminLongUserId();
        return orderGroupService.getGroupAutoJumpTab(userId);
    }

    @RequestMapping(path = "get-staff-types",method = RequestMethod.POST)
    public Response<List<StaffStatusVO>> getSelfStaffType(){
        long userId = ContextUtil.getAdminLongUserId();
        List<Integer> groupList = orderGroupService.getGroupList();
        log.info("getStaffType orderType={} userId={}", groupList, userId);

        List<StaffStatus> list = staffStatusService.getStaffStatusByTypes(userId, groupList);
        if (CollectionUtils.isEmpty(list)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<StaffStatusVO> res = list.stream().map(this::mapV).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(res);
    }

    private StaffStatusVO mapV(StaffStatus staffStatus) {
        StaffStatusVO v = new StaffStatusVO();
        v.setOrderType(staffStatus.getOrderType());
        v.setStaffStatus(staffStatus.getStaffStatus());
        v.setOperatorId(staffStatus.getUserId());
        return v;
    }

}
