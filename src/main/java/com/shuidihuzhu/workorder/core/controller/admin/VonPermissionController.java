package com.shuidihuzhu.workorder.core.controller.admin;

import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.core.model.view.OrderPermissionInfoVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RefreshScope
@RequestMapping("admin/cf/work-order/permission")
@RestController
public class VonPermissionController {

    @Autowired
    private VonPermissionService vonPermissionService;

    @ApiOperation("查询用户工单权限列表")
    @RequiresPermission("von:order-permission")
    @PostMapping("get-by-user")
    public Response<List<OrderPermissionInfoVO>> getByUser(@RequestParam("operatorId") long operatorId){
        return vonPermissionService.getInfoByUser(operatorId);
    }

    @ApiOperation("修改用户工单权限")
    @RequiresPermission("von:order-permission")
    @PostMapping("save-disable-by-user")
    public Response<Void> saveDisableByUser(@RequestParam("operatorId") long operatorId,
                                            @RequestParam int orderType,
                                            @RequestParam boolean disable){
        return vonPermissionService.saveDisableByUser(operatorId, orderType, disable);
    }
}
