package com.shuidihuzhu.workorder.core.controller.core;

import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCreateParam;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.model.von.VonOrderCreateContext;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.create.VonOrderCreatePlugin;
import com.shuidihuzhu.workorder.core.service.von.core.plugin.handle.VonOrderHandlePlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
public class WorkOrderCoreFeignController implements WorkOrderCoreFeignClient {

    @Autowired
    private VonOrderCreatePlugin vonOrderCreatePlugin;

    @Autowired
    private VonOrderHandlePlugin vonOrderHandlePlugin;

    @Override
    public Response<Long> create(WorkOrderCreateParam param) {
        Response<VonOrderCreateContext> r = vonOrderCreatePlugin.create(VonOrderCreateContext.createParam(param));
        Long workOrderId = Optional.ofNullable(r.getData()).map(VonOrderCreateContext::getCreateOrderId).orElse(0L);
        return NewResponseUtil.makeResponse(r.getCode(), r.getMsg(), workOrderId);
    }

    @Override
    public Response<Void> handle(HandleOrderParam param) {
        return vonOrderHandlePlugin.handle(param);
    }
}
