package com.shuidihuzhu.workorder.core.controller.storage;

import com.shuidihuzhu.client.cf.workorder.storage.VonStorageVO;
import com.shuidihuzhu.client.cf.workorder.storage.WorkOrderStorageFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.service.storage.StorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class WorkOrderStorageFeignController implements WorkOrderStorageFeignClient {

    @Autowired
    private StorageService storageService;

    @Override
    public Response<VonStorageVO> getLastByType(long workOrderId, int type) {
        return storageService.getLastByType(workOrderId, type);
    }

    @Override
    public Response<List<VonStorageVO>> getListByType(long workOrderId, int type) {
        return storageService.getListByType(workOrderId, type);
    }

    @Override
    public Response<VonStorageVO> addByTypeValue(long workOrderId, int type, String value) {
        return storageService.addByTypeValue(workOrderId, type, value);
    }

    @Override
    public Response<Void> addByList(List<VonStorageVO> list) {
        return storageService.addByList(list);
    }

    @Override
    public Response<Void> updateByTypeValue(long workOrderId, int type, String value) {
        return storageService.updateByTypeValue(workOrderId, type, value);
    }
}
