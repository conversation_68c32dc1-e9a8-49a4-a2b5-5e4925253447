package com.shuidihuzhu.workorder.core.controller.core;

import com.shuidihuzhu.client.cf.workorder.v2.client.VonSnapshotFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class VonSnapshotFeignController implements VonSnapshotFeignClient {

    @Override
    public Response<Void> saveJson(long workOrderId, String jsonData) {
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<String> getLastJsonSnapshot(long workOrderId) {
        return NewResponseUtil.makeSuccess(null);
    }
}
