package com.shuidihuzhu.workorder.core.controller.read;

import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.QueryListResult;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.dao.WorkOrderReadDAO;
import com.shuidihuzhu.workorder.core.service.core.OrderReadService;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.storage.StorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class WorkOrderReadFeignController implements WorkOrderReadFeignClient {

    @Resource
    private WorkOrderReadDAO workOrderReadDAO;

    @Resource
    private StorageService storageService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private OrderReadService orderReadService;

    @Override
    public Response<List<BasicWorkOrder>> getListByOrderIdList(List<Long> workOrderIdList) {
        return orderReadService.getListByOrderIdList(workOrderIdList);
    }

    @Override
    public Response<List<QueryListResult>> getListByOrderIdListOld(List<Long> workOrderIdList) {
        return baseWorkOrderService.getListByOrderIdListOld(workOrderIdList);
    }

    @Override
    public Response<BasicWorkOrder> getByOrderId(Long workOrderId) {
        BasicWorkOrder o = workOrderReadDAO.getById(workOrderId);
        o = storageService.fillStorage(o);
        return NewResponseUtil.makeSuccess(o);
    }

    @Override
    public Response<PaginationListVO<BasicWorkOrder>> search(OrderSearchParam param) {
        return orderReadService.search(param);
    }

    @Override
    public Response<PageResult<WorkOrderVO>> getOrderListByListParam(WorkOrderListParam param) {
        return orderReadService.getOrderList(param);
    }

}
