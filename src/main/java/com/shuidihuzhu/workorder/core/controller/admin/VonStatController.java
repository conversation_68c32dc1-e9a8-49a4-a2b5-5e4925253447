package com.shuidihuzhu.workorder.core.controller.admin;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.data.platform.client.ExportLargeExcelClient;
import com.shuidihuzhu.cf.data.platform.util.ExportUtil;
import com.shuidihuzhu.client.auth.saas.annotation.RequiresPermission;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ContextUtil;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.model.view.GroupStatItemVO;
import com.shuidihuzhu.workorder.core.model.view.GroupStatUserVO;
import com.shuidihuzhu.workorder.core.service.core.OrderGroupService;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RefreshScope
@RequestMapping("admin/cf/work-order/stat")
@RestController
@Slf4j
public class VonStatController {

    @Autowired
    private OrderGroupService orderGroupService;

    @Resource
    private ExportUtil exportUtil;

    @Resource
    private BaseWorkOrderService baseWorkOrderService;

    @ApiOperation("获取组待处理概况")
    @PostMapping("get-group-info")
    public Response<List<GroupStatItemVO>> getGroupStatInfo() {
        return orderGroupService.getGroupStatInfo();
    }

    @ApiOperation("获取组用户统计列表")
    @PostMapping("get-group-stat-user-list")
    public Response<List<GroupStatUserVO>> getGroupStatUserList(
            @RequestParam(value = "operatorId", required = false, defaultValue = "0") Long operatorId,
            @RequestParam(required = false) Integer staffStatus,
            @ApiParam("是否空闲") @RequestParam(required = false) Boolean free) {
        return orderGroupService.getGroupStatUserList(operatorId, staffStatus, free);
    }

    @ApiOperation("导出组用户统计列表")
    @GetMapping("export-group")
    public Response<Void> exportGroup(HttpServletResponse response,
                                 @RequestParam(value = "operatorId", required = false, defaultValue = "0") Long operatorId,
                                 @RequestParam(required = false) Integer staffStatus,
                                 @ApiParam("是否空闲") @RequestParam(required = false) Boolean free) {
        Response<List<GroupStatUserVO>> dataResp = orderGroupService.getGroupStatUserList(operatorId, staffStatus, free);
        if (dataResp.notOk()) {
            return NewResponseUtil.makeRelayFail(dataResp);
        }
        List<GroupStatUserVO> data = dataResp.getData();
        long userId = ContextUtil.getAdminLongUserId();

        String fileName = "工单人员统计数据-" + DateUtil.getCurrentDateTimeStr();
        RpcResult<Void> rpcResult = exportUtil.export(userId, data, GroupStatUserVO.class, fileName);
        log.info("导出组用户统计列表 {}", rpcResult);
//        writeExcel(response, data, fileName, GroupStatUserVO.class);
        return NewResponseUtil.makeSuccess(null);
    }

    @ApiOperation("获取工单处理状态")
    @RequiresPermission("von:stat-get-order-handle-result")
    @PostMapping("get-order-handle-result")
    public Response<Integer> getOrderHandleResult(@RequestParam(value = "workOrderId", required = false, defaultValue = "0") long workOrderId) {
        WorkOrderVO workOrderVO = baseWorkOrderService.getWorkOrderById(workOrderId);
        if (Objects.isNull(workOrderVO)) {
            return NewResponseUtil.makeSuccess(-100);
        }
        return NewResponseUtil.makeSuccess(workOrderVO.getHandleResult());
    }
}
