package com.shuidihuzhu.workorder.core.controller.innerapi;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.VonPermissionService;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("innerapi/admin/order/von-config")
public class InnerConfigController {

    @Autowired
    private VonConfigFacade vonConfigFacade;

    @Autowired
    private VonPermissionService vonPermissionService;

    @PostMapping("get-by-order-type")
    public Response<List<VonConfigDO>> getByOrderType(@RequestParam() int orderType){
        List<VonConfigDO> config = vonConfigFacade.getAllByOrderType(orderType);
        return NewResponseUtil.makeSuccess(config);
    }

    @PostMapping("save-by-map")
    public Response<Object> saveByMap(@RequestParam() int orderType, @RequestBody Map<String, String> configMap){
        vonConfigFacade.saveByMap(orderType, configMap);
        return NewResponseUtil.makeSuccess(null);
    }

    @PostMapping("save-by-list")
    public Response<Object> saveByList(@RequestBody List<VonConfigDO> configList){
        vonConfigFacade.saveByList(configList);
        return NewResponseUtil.makeSuccess(null);
    }


    @PostMapping("init-staff-by-order-type")
    public Response<Void> initStaffByOrderType(@RequestParam() int orderType){
        return vonPermissionService.initStaffByOrderType(orderType);
    }

}
