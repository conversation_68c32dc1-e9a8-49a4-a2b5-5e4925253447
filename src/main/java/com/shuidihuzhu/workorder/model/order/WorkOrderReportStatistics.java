package com.shuidihuzhu.workorder.model.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-08-05 16:33
 **/
@Data
@ApiModel
public class WorkOrderReportStatistics {

    @ApiModelProperty("id")
    private long id;

    @ApiModelProperty("日期")
    private int statDay;

    @ApiModelProperty("小时")
    private int statHour;

    @ApiModelProperty("处理人")
    private long operatorId;

    @ApiModelProperty("工单类型")
    private int orderType;

    @ApiModelProperty("累计离线次数")
    private int offlineCount;

    @ApiModelProperty("今日分配数量")
    private int assignCount;

    @ApiModelProperty("处理完成量")
    private int doneCount;

    @ApiModelProperty("今日拨打电话数量")
    private int callCount;

    @ApiModelProperty("今日总通话时长")
    private int callDuration;

    @ApiModelProperty("结束处理数量")
    private int endDealCount;

    @ApiModelProperty("无需处理数量")
    private int noNeedDealCount;

    @ApiModelProperty("升级二线数量")
    private int upgradeCount;

    @ApiModelProperty("转入失联数量")
    private int lostCount;

}
