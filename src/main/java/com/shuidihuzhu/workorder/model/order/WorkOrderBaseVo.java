package com.shuidihuzhu.workorder.model.order;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/12/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WorkOrderBaseVo extends WorkOrderBase {

    private String callStatus;

    private String orderExtType;


    @Override
    public List<WorkOrderExt> getWorkOrderExt() {
        return null;
    }
}
