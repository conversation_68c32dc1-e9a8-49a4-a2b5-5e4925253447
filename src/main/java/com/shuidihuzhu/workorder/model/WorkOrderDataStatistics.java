package com.shuidihuzhu.workorder.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Data
public class WorkOrderDataStatistics {
    private long id;
    private int statDay;
    private int statHour;
    private long operatorId;
    private int orderType;
    private int callCount;
    private int doneCount;
    private int assignCount;
    private int manualLockCount;
    private int exceptionDoneCount;
    private int auditRejectCount;
    private int auditPassCount;
    private int stopCaseCount;
    private int withinThreeMinutesCount;
    private int outsideThreeMinutesCount;
    private int withinTenMinutesCount;
    private int outsideTenMinutesCount;
    private Date createTime;
    private Date updateTime;
    private int isDelete;

    private int handleManualLockCount;
    private int effectiveCallCount;
}
