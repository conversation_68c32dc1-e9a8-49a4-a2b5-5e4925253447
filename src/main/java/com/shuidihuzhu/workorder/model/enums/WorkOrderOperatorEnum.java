package com.shuidihuzhu.workorder.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Getter
@AllArgsConstructor
public enum WorkOrderOperatorEnum {

    CF_WORK_ORDER(1, "筹工单"),
    // 招募需求：招募手动分配优先级高于系统 所以单独定义枚举来区分操作 
    // 为了不影响筹的工单关于筹工单的调用默认使用枚举  CF_WORK_ORDER 后续有需要再拓展
    PR_SYSTEM_ASSIGN_WORK_ORDER(2, "招募系统自动分配工单"),
    PR_MANUAL_ASSIGN_WORK_ORDER(3, "招募人工手动分配工单"),
    ;
    private final int code;
    private final String name;
}
