package com.shuidihuzhu.workorder.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@Getter
public enum OperateMode {

    create(1,"创建工单"),

    assignation(2,"分配工单"),

    free(3,"释放工单"),

    handle(4,"处理工单"),

    reminder(5,"人工催单"),

    manualAssignation(7,"手动分配"),

    reprocess(8,"重新审核"),

    init(9,"工单初始化"),

    transfer(10,"举报工单转移"),

    assignType(11,"修改工单分配类型"),

    changeQc(12, "修改质检结果"),

    batchAllocation(13, "批量分配工单"),

    transferOrder(14, "转单"),

    callback(15,"回收工单"),

    upgrade(16,"升级工单"),

    /**
     * 通用备注
     */
    common(99, ""),

    ;

    private int type;

    private String msg;

    OperateMode(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

}
