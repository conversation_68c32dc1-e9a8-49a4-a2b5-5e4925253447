package com.shuidihuzhu.workorder.model.enums;


import com.shuidihuzhu.common.web.enums.MyErrorCode;

/**
 * <AUTHOR>
 * @DATE 2018/12/19
 */
public enum ErrorCode implements MyErrorCode {

    SUCCESS(0, "success"),
    SYSTEM_ERROR(1, "系统错误"),
    RPC_FALL_BACK(2, "rpc fall back"),
    SYSTEM_PARAM_ERROR(1001, "参数错误"),
    SYSTEM_PERMISSION_ERROR(1002, "没有操作权限"),
    SYSTEM_HANDLE_ERROR(1003, "工单未操作成功"),
    SYSTEM_CASE_ERROR(1004, "查询案例错误"),
    SYSTEM_REDIS_LOCK_ERROR(1005, "获取分布式锁失败"),
    SYSTEM_ORDER_REPEAT(1006, "工单已生成"),
    SYSTEM_CASE_END_ERROR(1007, "案例已结束"),
    SYSTEM_ORDER_ERROR(1008, "未找到对应工单"),
    SYSTEM_ORDER_TYPE_ERROR(1009, "工单类型错误"),
    SYSTEM_STAFF_ERROR(1010, "未找到对应工单类型人员"),
    SYSTEM_ORDER_CON(1011, "不满足生成条件"),


    BUSI_YANHOU_COUNT(2001,"操作失败，已超过最大稍后处理数量"),
    NOT_MORE_WORK_ORDER(2002, "没有更多待分配工单"),
    NOT_MORE_STAFF_ASSIGNATE(2003, "没有更多可分配工单人员"),
    WORK_ORDER_HAS_ASSIGNED(2004, "工单已分配"),
    STAFF_HAS_OFFLINE(2005, "员工已离线"),
    CAN_NOT_REPROCESS(2006, "不符合重新审核条件"),
    OFFLINE_LIMIT_REMIND(2007, "今日离线次数已达最大次数，无法再操作离线"),
    WORK_ORDER_STATUS_ERROR(2008, "工单状态错误"),
    ASSIGN_PLUGIN_NO_MATCH(2010, "人员分配不符合特殊指定"),
    RECEIPT_THRESHOLD_ERROR(2011, "已达到接收人可处理工单的上限，请更换接收人"),
    TARGET_IS_OFFLINE(2012, "接收人不在线，请更换接收人"),
    NOT_SUPPORT_ORDER_TYPE(2013, "工单类型不支持"),

    STAFF_OFFLINE_BY_MANAGER(2014, "已被组长操作离线"),
    CANNOT_BOTH_ON_LINE(2015, "不允许同时在线"),
    ORDER_RELEASE_FAIL(2016, "工单回收失败"),
    ALREADY_UPDATE_ASSIGN_GROUP(2017, "分配组已是该组，无需再次操作"),

    SYSTEM_NO_OPERATION_ERROR(3001, "暂不支持"),

    OPERATION_FAILED(9001, "操作失败，请重新操作"),
    ;

    private int code;

    private String msg;


    ErrorCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMsg() {
        return msg;
    }



}
