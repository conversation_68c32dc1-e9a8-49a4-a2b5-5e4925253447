package com.shuidihuzhu.workorder.model.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wangpeng
 * @Date: 2023/3/2 11:09
 * @Description:
 */
@Getter
public enum VonConfigKey {
    WORK_ORDER_HANDLE_RESULT("assign.workOrderHandleResult", "工单的哪些审核结果可以重新审核 不填或输入空格为不限制 (eg:8,9,11)", "String"),
    HANDLE_RESULT_MAPPER_DONE_LIST("handleResultMapper.doneList", "获取已完成的状态列表 不填或空格走硬编码配置 (eg: 8,9,11)", "String"),
    ASSIGN_LIMIT_COUNT("assign.assignLimitCount", "领单限制数量", "Integer"),
    ASSIGN_COUNT_PEAR_OPERATOR("assign.assignCountPearOperator", "每查出一个领单人一次分多少工单", "Integer"),
    MIN_ASSIGN_ORDER_COUNT("assign.minAssignOrderCount", "最少攒多少工单才会分配一次", "Integer"),
    IS_USE_OLD_ASSIGNER("assign.isUseOldAssigner", "使用老版工单分配", "Boolean"),
    NOT_ASSIGN_DOING_OPERATOR("assign.notAssignDoingOperator", "不给有工单处理中的人分配", "Boolean"),
    //优先分配给上一次处理该案例该类型工单的操作人 若走一人分配制则每次只能分配一个工单```assignCountPearOperator``` == 1
    PRIOR_ASSIGN_LAST_ASSIGN("assign.priorAssignLastAssign", "优先分配给上一次处理该案例该类型工单的操作人", "Boolean"),
    AUTO_ASSIGN("assign.autoAssign", "是否默认自动分配 默认为true 自动分配", "Boolean"),
    PRIOR_ASSIGNER_ENABLE("assign.priorAssignerEnable", "指定优先分配人是否启用", "Boolean"),
    DOWN_WHEN_GROUP_NO_ONLINE_USER("assign.downWhenGroupNoOnlineUser", "是否在组内无人在线时降级为不分组分配", "Boolean"),
    DAY_ASSIGN_LIMIT_ENABLE("assign.dayAssignLimitEnable", "日接单限制是否开启", "Boolean"),
    WORK_ORDER_SELF_HANDLE("assign.workOrderSelfHandle", "工单是否要本人处理", "Boolean"),
    ENABLE_LATER_DO_AUTO_FREE("assign.enableLaterDoAutoFree", "稍后处理是否支持离线自动回收", "Boolean"),
    ENABLE_DOING_AUTO_FREE("assign.enableDoingAutoFree", "处理中是否支持离线自动回收", "Boolean"),
    WORK_ORDER_REPROCESS("assign.workOrderReprocess", "工单是否可以重新审核", "Boolean"),
    WORK_ORDER_REMINDER("assign.workOrderReminder", "工单是否可以催单", "Boolean"),
    GROUP_ASSIGN_CHECK_ORDER_PERMISSION("assign.groupAssignCheckOrderPermission", "组分配时是否需校验工单权限,默认是", "Boolean"),
    ;


    private final String configKey;

    private final String configMsg;

    private final String configFieldType;

    VonConfigKey(String configKey, String configMsg, String configFieldType) {
        this.configKey = configKey;
        this.configMsg = configMsg;
        this.configFieldType = configFieldType;
    }

    public static final List<Map<String, String>> list = new ArrayList<>();

    static {
        for (VonConfigKey item : VonConfigKey.values()) {
            Map<String, String> map = new HashMap<>();
            map.put("configKey", item.getConfigKey());
            map.put("configMsg", item.getConfigMsg());
            map.put("configFieldType", item.getConfigFieldType());
            list.add(map);
        }
    }
}
