package com.shuidihuzhu.workorder.model;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018-10-30  14:18
 */
@Getter
public class EagleParam {

    private long userId;

    @Deprecated
    private int experimentId;

    private String extInfo = "{}";

    private String debug;

    private String ip;

    private String key;

    public static Builder builder(long userId){
        Builder builder = new Builder();
        builder.userId(userId);
        return builder;
    }

    private EagleParam() {
    }

    @Override
    public String toString() {
        return "AdminEagleParam{" +
                "userId=" + userId +
                ", experimentId=" + experimentId +
                ", extInfo='" + extInfo + '\'' +
                ", debug='" + debug + '\'' +
                ", ip='" + ip + '\'' +
                ", key='" + key + '\'' +
                '}';
    }

    public static class Builder {

        EagleParam param = new EagleParam();

        private Builder() {}

        public Builder userId(long userId){
            param.userId = userId;
            return this;
        }

        @Deprecated
        public Builder experimentId(int experimentId){
            param.experimentId = experimentId;
            return this;
        }

        public Builder extInfo(String extInfo){
            param.extInfo = extInfo;
            return this;
        }

        public Builder debug(String debug){
            param.debug = debug;
            return this;
        }

        public Builder ip(String ip){
            param.ip = ip;
            return this;
        }

        public Builder key(String key){
            param.key = key;
            return this;
        }

        public EagleParam build(){
            return param;
        }

    }
}
