package com.shuidihuzhu.workorder.model;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import lombok.Getter;

import java.util.function.Function;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
public final class OpResult<T> {

    private OpResult() {
        //禁止实例化
    }


    public static <T> OpResult<T> createSucResult() {
        return new OpResult<>(ErrorCode.SUCCESS);
    }

    public static <T> OpResult<T> createSucResult(T data) {
        return new OpResult<>(ErrorCode.SUCCESS, data);
    }

    public static <T> OpResult<T> createFailResult(ErrorCode errorCode) {
        return new OpResult<>(errorCode);
    }

    public static <T> OpResult<T> createFailResult(ErrorCode errorCode, T data) {
        return new OpResult<>(errorCode, data);
    }

    public static <T, R> OpResult<R> createWithResponse(Response<T> resp, Function<? super T, ? extends R> mapper) {
        if (resp.ok()) {
            T data = resp.getData();
            if (data == null) {
                return createSucResult();
            }
            return createSucResult(mapper.apply(data));
        }
        return new OpResult<>(ErrorCode.SYSTEM_ERROR, null, resp.getMsg());
    }

    public static <T> OpResult<T> createWithResponse(Response<T> resp) {
        return createWithResponse(resp, Function.identity());
    }


    public boolean isSuccess() {
        return errorCode == ErrorCode.SUCCESS;
    }


    public boolean isFail() {
        return !isSuccess();
    }


    private OpResult(ErrorCode errorCodeEnum) {
        this(errorCodeEnum, null, null);
    }

    private OpResult(ErrorCode errorCode, T data) {
        this(errorCode, data, null);
    }

    private OpResult(ErrorCode errorCode, T data, String message) {
        this.errorCode = errorCode;
        this.data = data;
        this.message = message;
    }

    @Getter
    private String message;

    @Getter
    private ErrorCode errorCode;

    @Getter
    private T data;

    @Override
    public String toString() {
        return "OpResult{" +
                "message='" + message + '\'' +
                ", errorCode=" + errorCode +
                ", data=" + data +
                '}';
    }
}