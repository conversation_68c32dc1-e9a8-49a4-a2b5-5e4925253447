package com.shuidihuzhu.workorder.model;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */
public interface MQTag {


    String CF = "CF";

    String create_work_order = "create_work_order";


    String delay_work_order = "delay_work_order";


    String delay_more_work_order = "delay_more_work_order";


    String genjin_order_create_mq = "genjin_order_create_mq";

    String GET_CALL_TOTAL_DURATION = "GET_CALL_TOTAL_DURATION";

    String QC_WORK_ORDER_ASSIGN_EVENT = "QC_WORK_ORDER_ASSIGN_EVENT";
    String PR_WORK_ORDER_ASSIGN_EVENT = "PR_WORK_ORDER_ASSIGN_EVENT";
    String PR_WORK_ORDER_ASSIGN_EVENT_V2 = "PR_WORK_ORDER_ASSIGN_EVENT_V2";

    String QC_WORK_ORDER_ASSIGN_EVENT_V2 = "QC_WORK_ORDER_ASSIGN_EVENT_V2";

    String VON_STAFF_STATUS_CHANGE = "VON_STAFF_STATUS_CHANGE";

    String VON_ORDER_STATUS_CHANGE = "VON_ORDER_STATUS_CHANGE";

    String VON_ORDER_STATUS_CHANGE_OLD = "VON_ORDER_STATUS_CHANGE_OLD";

    String VON_ORDER_ASSIGN_GROUP = "VON_ORDER_ASSIGN_GROUP";

    String VON_ORDER_ASSIGN_SUCCESS = "VON_ORDER_ASSIGN_SUCCESS";

    String VON_ORDER_ASSIGN_TRIGGER = "VON_ORDER_ASSIGN_TRIGGER";

}
