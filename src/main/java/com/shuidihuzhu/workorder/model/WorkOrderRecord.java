package com.shuidihuzhu.workorder.model;

import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.QcWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/20
 */
@Data
public class WorkOrderRecord {

    private long id;

    private long caseId;
    //工单id
    private long workOrderId;

    private int workOrderType;

    private long operatorId;

    private String comment;

    /**
     * @see OperateMode
     */
    private int operateMode;

    private String operateDesc;

    private Date createTime;

    public WorkOrderRecord() {
    }

    private WorkOrderRecord(long caseId, long workOrderId, int workOrderType, long operatorId, String comment, int operateMode, String operateDesc) {
        this.caseId = caseId;
        this.workOrderId = workOrderId;
        this.workOrderType = workOrderType;
        this.operatorId = operatorId;
        this.comment = comment;
        this.operateMode = operateMode;
        this.operateDesc = operateDesc;
    }

    public static WorkOrderRecord create(long caseId, long workOrderId, int workOrderType, long operatorId, String comment,
                                         OperateMode operateMode) {
        return new WorkOrderRecord(caseId, workOrderId, workOrderType, operatorId, comment,
                operateMode.getType(), operateMode.getMsg());
    }

    public static WorkOrderRecord create(WorkOrderBase order, long operatorId, String comment,
                                         OperateMode operateMode) {
        return create(order.getCaseId(),
                order.getId(),
                order.getOrderType(),
                operatorId,
                comment,
                operateMode);
    }

    public static WorkOrderRecord createRecord(WorkOrderBase workOrder) {

        if (workOrder.getId() == 0) {
            return null;
        }

        WorkOrderRecord record = new WorkOrderRecord();
        record.setCaseId(workOrder.getCaseId());
        record.setWorkOrderType(workOrder.getOrderType());
        record.setOperatorId(workOrder.getOperatorId());
        record.setComment(StringUtils.isNotEmpty(workOrder.getComment()) ? workOrder.getComment() : "系统操作");
        record.setOperateMode(OperateMode.create.getType());
        record.setOperateDesc(OperateMode.create.getMsg());
        record.setWorkOrderId(workOrder.getId());

        return record;
    }


    public static WorkOrderRecord assignationRecord(long workOrderId, int orderType, Long user, long assignerId, String comment, OperateMode operateMode) {
        boolean isManual = assignerId > 0;
        if (StringUtils.isBlank(comment)) {
            comment = isManual ? "手动分配" + assignerId : "系统自动分配";
        }
        if (operateMode == null) {
            operateMode = isManual ? OperateMode.manualAssignation : OperateMode.assignation;
        }

        return create(0, workOrderId, orderType, user, comment, operateMode);
    }

    public static WorkOrderRecord assignationRecord(long workOrderId, int orderType, Long user, long assignerId) {
        return assignationRecord(workOrderId, orderType, user, assignerId, null, null);
    }


    public static WorkOrderRecord qcAssignationRecord(long workOrderId, int orderType, long operatorId, String name) {

        boolean isManual = operatorId > 0;
        String comment = isManual ? "手动分配" + name : "系统自动分配";
        OperateMode operateMode = isManual ? OperateMode.manualAssignation : OperateMode.assignation;

        return create(0, workOrderId, orderType, operatorId, comment, operateMode);
    }

    /**
     * 重新审核记录
     */
    public static WorkOrderRecord assignationReprocessRecord(long workOrderId, int orderType, Long user, String comment) {
        return create(0, workOrderId, orderType, user, comment, OperateMode.reprocess);
    }

    public static List<WorkOrderRecord> freeRecord(List<WorkOrderBase> doingList, String comment, long operatorId) {
        return doingList.stream().map(r -> {

            WorkOrderRecord record = new WorkOrderRecord();
            record.setWorkOrderType(r.getOrderType());
            if (operatorId <= 0) {
                record.setOperatorId(r.getOperatorId());
            } else {
                record.setOperatorId(operatorId);
            }
            record.setComment(comment);
            record.setOperateMode(OperateMode.free.getType());
            record.setOperateDesc(OperateMode.free.getMsg());
            record.setWorkOrderId(r.getId());
            return record;

        }).collect(Collectors.toList());
    }

    public static List<WorkOrderRecord> freeRecord(List<WorkOrderBase> doingList) {
        return freeRecord(doingList, "用户离线", 0);
    }


    public static WorkOrderRecord handleRecord(HandleOrderParam param) {

        if (param.getWorkOrderId() == 0) {
            return null;
        }

        int handleResult = param.getHandleResult();
        String comment = HandleResultEnum.getShowMsgByType(handleResult);
        if (StringUtils.isNotEmpty(param.getOperComment())) {
            comment = param.getOperComment() + "_" + handleResult;
        }

        return create(0, param.getWorkOrderId(), param.getOrderType(), param.getUserId(), comment, OperateMode.handle);
    }

    public static WorkOrderRecord reminderRecord(long workOrderId, long operatorId, String userComment) {

        WorkOrderRecord record = new WorkOrderRecord();
        record.setWorkOrderType(0);
        record.setOperatorId(operatorId);
        record.setComment(userComment);
        record.setOperateMode(OperateMode.reminder.getType());
        record.setOperateDesc(OperateMode.reminder.getMsg());
        record.setWorkOrderId(workOrderId);

        return record;
    }

}
