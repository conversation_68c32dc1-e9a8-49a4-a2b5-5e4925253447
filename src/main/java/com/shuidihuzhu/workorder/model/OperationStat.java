package com.shuidihuzhu.workorder.model;

/**
 * <AUTHOR>
 * @DATE 2019/5/12
 */
public final class OperationStat {

    /**
     * 统计指标
     */
    public static final String WOEKORDER_OPERATING_STAT = "workorder_operation_stat";


    /**
     * 类别名称
     */
    public static final String OPERATION = "operation";


    /**
     * 类别名称   工单类型
     */
    public static final String ORDERTYPE = "ordertype";

    /**
     * 工单
     */
    public static final String ORDER_NAME = "order_name";

    /**
     * 一级工单类型
     */
    public static final String ONE_TYPE = "one_type";

    /**
     * 分配
     */
    public static final String assignate = "assignate";

    /**
     * 分配成功
     */
    public static final String assignate_succ = "assignate_succ";

    /**
     * 转单成功
     */
    public static final String transfer_succ = "transfer_succ";


    /**
     * 系统补单
     */
    public static final String retry_create = "retry_create";

    /**
     * 自动补初审工单
     */
    public static final String auto_mend_work_order = "auto_mend_work_order";


    /**
     * 处理
     */
    public static final String handle = "handle";


    /**
     * 处理
     */
    public static final String handle_result = "handle_result";
}
