package com.shuidihuzhu.workorder.model.event;

import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;


/**
 * <AUTHOR>
 * @DATE 2019/5/20
 */
@Getter
@Setter
public class WorkOrderStatusEvent extends ApplicationEvent {


    private static final long serialVersionUID = 3318819899371822886L;

    private WorkOrderBase workOrderBase;


    public WorkOrderStatusEvent(Object source,WorkOrderBase workOrderBase) {
        super(source);
        this.workOrderBase = workOrderBase;
    }
}
