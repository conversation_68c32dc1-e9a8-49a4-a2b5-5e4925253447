package com.shuidihuzhu.workorder.model.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @time 2019/3/14 上午10:22
 * @desc
 */
public class AssignateWorkOrderEvent extends ApplicationEvent {
    private static final long serialVersionUID = -1145725696968129007L;
    @Getter
    @Setter
    private int orderType;

    public AssignateWorkOrderEvent(Object source, int orderType) {
        super(source);
        this.orderType = orderType;
    }
}
