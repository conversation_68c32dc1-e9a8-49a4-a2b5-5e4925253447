package com.shuidihuzhu.workorder;

import com.shuidihuzhu.cf.enhancer.subject.druid.EnableDruidMonitor;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.EnableDynamicThreadPool;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeCommonsClient;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeConsulHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDataSourceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeDiskSpaceHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.health.EnableFakeRedissonHealth;
import com.shuidihuzhu.eb.grafana.configuration.plugin.hystrix.executionhook.EnableCfMetricsHystrixCommandExecutionHook;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.elasticsearch.ElasticSearchReactiveHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.dashboard.EnableHystrixDashboard;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {
		"com.shuidihuzhu",
		"com.shuidi"
},exclude = {DataSourceAutoConfiguration.class, ElasticSearchReactiveHealthContributorAutoConfiguration.class})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableDiscoveryClient
@EnableCircuitBreaker
@EnableFeignClients(basePackages = {"com.shuidihuzhu.client", "com.shuidihuzhu.cf.client",
		"com.shuidihuzhu.cf.feign", "com.shuidihuzhu.data",
		"com.shuidihuzhu.cf.adminfeign", "com.shuidihuzhu.auth",
		"com.shuidihuzhu.cf.risk.client",
		"com.shuidihuzhu.cf.data",
		"com.shuidihuzhu.pr"})
@EnableHystrixDashboard
@EnableFakeDataSourceHealth
@EnableFakeRedissonHealth
@EnableFakeCommonsClient
@EnableFakeConsulHealth
@EnableFakeDiskSpaceHealth
@EnableCfMetricsHystrixCommandExecutionHook
@EnableAsync
@EnableDynamicThreadPool(globalModel = false, noticeKey = "0b3add40-c27b-493a-aa57-4fefdaa03046", activeTrigger=false)
@EnableDruidMonitor
public class Application {
	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
		}
}
