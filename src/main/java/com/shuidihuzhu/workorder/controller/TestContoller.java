package com.shuidihuzhu.workorder.controller;

import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthGroupDto;
import com.shuidihuzhu.client.auth.saas.model.dto.GroupMembersResultDto;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.workorder.model.MQTag;
import com.shuidihuzhu.workorder.model.vo.GenjinModel;
import com.shuidihuzhu.workorder.service.impl.StaffStatusServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@RestController
@RequestMapping(path = "admin/order")
@Slf4j
public class TestContoller {

    @Autowired
    StaffStatusServiceImpl staffStatusService;

    @RequestMapping(path = "send")
    public void sendMQ(long operatorId,int orderType){
        WorkOrderBase workOrderBase = new WorkOrderBase() {
            @Override
            public List<WorkOrderExt> getWorkOrderExt() {
                return null;
            }
        };
        workOrderBase.setOperatorId(operatorId);
        workOrderBase.setOrderType(orderType);
        staffStatusService.onOrderCreate(workOrderBase);
    }








}
