package com.shuidihuzhu.workorder.controller.feign;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.CfCreditWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.impl.CreditWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: fengxuan
 * @create 2019-12-25 18:00
 **/
@RestController
@Slf4j
public class CreditFeignController implements CfCreditWorkOrderClient {

    @Resource(name = "creditWorkOrderService")
    private CreditWorkOrderServiceImpl creditWorkOrderService;

    @PostMapping(path = "innerapi/admin/order/create-credit")
    @Override
    public Response<Long> createCredit(CreditWorkOrder workOrder) {
        log.info("createFunduse workOrder:{}", JSON.toJSONString(workOrder));
        OpResult<Long> workIdOpr = creditWorkOrderService.doCreate(workOrder);
        return NewResponseUtil.makeError(workIdOpr.getErrorCode(), workIdOpr.getData());
    }

    @PostMapping(path = "innerapi/admin/order/handle-credit")
    @Override
    public Response handleCredit(CreditHandleOrderParam param) {
        log.info("handleFunduse workOrder:{}", JSON.toJSONString(param));
        OpResult handle = creditWorkOrderService.doHandle(param);
        return NewResponseUtil.makeError(handle.getErrorCode(), handle.getData());
    }

    @PostMapping(path = "innerapi/admin/order/credit-orderlist")
    @Override
    public Response<PageResult<WorkOrderVO>> creditOrderlist(WorkOrderListParam param) {
        log.debug("funduseOrderlist param:{}", JSON.toJSONString(param));
        OpResult<PageResult<WorkOrderVO>> orderList = creditWorkOrderService.getOrderList(param);
        return NewResponseUtil.makeError(orderList.getErrorCode(), orderList.getData());
    }
}
