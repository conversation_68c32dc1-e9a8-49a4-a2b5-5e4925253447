package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.CfWorkOrderConstant;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.micrometer.core.instrument.MeterRegistry;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/5/17
 */
@Api("初审工单")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class ChuciFeignController {


    @Resource(name = "chuciWorkOrderService")
    private WorkOrderFacade workOrderFacade;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Resource
    private MeterRegistry meterRegistry;


    @ApiOperation(("创建初审工单"))
    @RequestMapping(path = "create-chuci",method = RequestMethod.POST)
    public Response<Long> createChuci(@RequestBody ChuciWorkOrder workOrder){

        log.info("createShenhe workOrder={}",workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation("若发起案例创建初审工单失败，执行重新创建")
    @PostMapping("check-and-fix-chu-ci-work-order")
    public Response checkAndFixChuCiWorkOrder(@RequestBody ChuciWorkOrder workOrder){

        log.info("checkAndFixChuCiWorkOrder workOrder={}",workOrder);

        OpResult opResult = doCheckAndFixChuCiWorkOrder(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    /**
     * 若发起案例创建初审工单失败，执行重新创建
     * @param workOrder
     * @return
     */
    private OpResult doCheckAndFixChuCiWorkOrder(@RequestBody ChuciWorkOrder workOrder) {
        WorkOrderBase workOrderBase = workOrderDao.getLastByCaseIdAndOrderTypes(workOrder.getCaseId(), CfWorkOrderConstant.checkUnCreateChushen);
        if (workOrderBase != null) {
            return OpResult.createSucResult();
        }
        try {
            meterRegistry.counter(
                    OperationStat.WOEKORDER_OPERATING_STAT,
                    OperationStat.OPERATION,
                    OperationStat.auto_mend_work_order,
                    OperationStat.ORDER_NAME,
                    WorkOrderType.shenhe.name()
            ).increment();
        } catch (Exception e) {
            log.error("doCheckAndFixChuCiWorkOrder stat error", e);
        }
        workOrder.setOperComment("job自动补单");
        log.info("doCheckAndFixChuCiWorkOrder workOrder={}",workOrder);
        return workOrderFacade.doCreate(workOrder);
    }


    @RequestMapping(path = "hanlde-chuci",method = RequestMethod.POST)
    @Deprecated
    public Response hanldeChuci(@RequestBody ChuciHandleOrderParam param ){

        log.info("hanldechuci param={}",param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }


    @ApiOperation("处理初审工单")
    @RequestMapping(path = "hanlde-chuci-v2",method = RequestMethod.POST)
    public Response<Map<Integer,Long>> hanldeChuciV2(@RequestBody ChuciHandleOrderParam param ){

        log.info("hanldechuci param={}",param);

        OpResult<Map<Integer,Long>> opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation("查询初审工单列表")
    @RequestMapping(path = "chuci-orderlist",method = RequestMethod.POST)
    public Response<PageResult<WorkOrderVO>> chuciOrderList(@RequestBody WorkOrderListParam param){

        OpResult opResult =  workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

}
