package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.PrOnlineWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.PrHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.PrOnlineWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.PrOnlineWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api("线上招募工单")
@RestController
public class PrOnlineWorkOrderClientController implements PrOnlineWorkOrderClient {

    @Resource(name = "prOnlineOrderService")
    private WorkOrderFacade<PrOnlineWorkOrder, PrHandleOrderParam, WorkOrderListParam> workOrderFacade;
    @Autowired
    private PrOnlineWorkOrderService prOnlineWorkOrderService;

    @Override
    public Response<Long> create(PrOnlineWorkOrder workOrder) {
        log.info("PrOnlineWorkOrder create prOnlineWorkOrder:{}", workOrder);
        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);
        if (opResult.isSuccess()) {
            return NewResponseUtil.makeSuccess(opResult.getData());
        }
        return NewResponseUtil.makeError(opResult.getErrorCode());
    }

    @Override
    public Response<Void> handle(PrHandleOrderParam param) {
        workOrderFacade.doHandle(param);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response orderList(WorkOrderListParam param) {
        OpResult opResult = workOrderFacade.getOrderList(param);
        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response<Long> getOnlineAssign(List<String> misIdList) {
        return NewResponseUtil.makeSuccess(prOnlineWorkOrderService.getOnlineAssign(misIdList));
    }
}
