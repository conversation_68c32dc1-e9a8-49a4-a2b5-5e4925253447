package com.shuidihuzhu.workorder.controller.feign;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderDataStatisticsClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderDataStatisticsView;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderReportStatisticsVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.WorkOrderDataStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */

@Slf4j
@RestController
public class WorkOrderDataStatisticsController implements CfWorkOrderDataStatisticsClient {

    @Resource
    private WorkOrderDataStatisticsService workOrderDataStatisticsService;

    @Override
    public Response<List<WorkOrderDataStatisticsView>> getOrgDataStat(List<Integer> orgIds, List<Integer> orderTypes) {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());

//        if (CollectionUtils.isEmpty(orgIds) || CollectionUtils.isEmpty(orderTypes)) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        return NewResponseUtil.makeSuccess(workOrderDataStatisticsService.getOrgDataStat(orgIds, orderTypes));
    }

    @Override
    public Response<List<WorkOrderDataStatisticsView>> getUserDataStat(List<Long> userIds, List<Integer> orderTypes) {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());

//        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(orderTypes)) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//        return NewResponseUtil.makeSuccess(workOrderDataStatisticsService.getUserDataStat(userIds, orderTypes));
    }

    @Override
    public Response<List<WorkOrderDataStatisticsView>> getDataDetailStatByOrderType(int orgId, long userId, List<Integer> orderTypes) {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());

//        if (orgId < 0 && userId <= 0) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        return NewResponseUtil.makeSuccess(workOrderDataStatisticsService.getDataDetailStatByOrderType(orgId, userId, orderTypes));
    }

    @Override
    public Response<List<WorkOrderReportStatisticsVo>> getReportUserDataStat(List<Long> userIds, List<Integer> orderTypes) {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());
//        if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(orderTypes)) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        return NewResponseUtil.makeSuccess(workOrderDataStatisticsService.getReportUserDataStat(userIds, orderTypes));
    }

    @Override
    public Response<List<WorkOrderReportStatisticsVo>> getReportUserDataByOrderType(long userId, List<Integer> orderTypes) {
        return NewResponseUtil.makeSuccess(Lists.newArrayList());
//        if (userId <= 0 || CollectionUtils.isEmpty(orderTypes)) {
//            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        return NewResponseUtil.makeSuccess(workOrderDataStatisticsService.getReportUserDataByOrderType(userId, orderTypes));
    }

}
