package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @DATE 2019/12/11
 */
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class WenjuanFeignController {

    @Resource(name = "wenjuanWorkOrderService")
    private WorkOrderFacade workOrderFacade;


    /**
     * 创建工单
     *
     * @param workOrder
     * @return
     */
    @RequestMapping(path = "create-wenjuan", method = RequestMethod.POST)
    public Response<Long> createWenjuan(@RequestBody WenjuanWorkOrder workOrder) {

        log.info("createWenjuan workOrder={}", workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(), opResult.getData());

    }


    /**
     * 处理工单
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "handle-wenjuan", method = RequestMethod.POST)
    public Response handleWenjuan(@RequestBody HandleOrderParam param) {

        log.info("handleWenjuan param={}", param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(), opResult.getData());

    }

    /**
     * 查询工单
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "wenjuan-orderlist", method = RequestMethod.POST)
    public Response<PageResult<WorkOrderVO>> wenjuanOrderlist(@RequestBody WorkOrderListParam param) {

        log.info("wenjuanOrderlist param={}", param);

        if (StringUtils.isBlank(param.getHandleResult())) {
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(), "handleResult 不能为空", null);
        }

        OpResult<PageResult<WorkOrderVO>> opResult = workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

}
