package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.service.admin.AdminWorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * WorkOrder admin api
 *
 * <AUTHOR>
 * @date 2019-07-08
 */
@Api("工单 后门")
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class AdminWorkOrderController {

    @Autowired
    private AdminWorkOrderService adminWorkOrderService;

    @ApiOperation(("工单状态 初始化"))
    @PostMapping("/init-order-state")
    Response<Void> initWorkOrderState(@ApiParam("工单id") @RequestParam("workOrderId") Long workOrderId,
                                      @ApiParam("operatorId") @RequestParam("operatorId") Integer operatorId) {
        return adminWorkOrderService.initWorkOrderState(workOrderId, operatorId);
    }
}
