package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.CfWorkOrderAnalysisClient;
import com.shuidihuzhu.client.cf.workorder.model.vo.WorkOrderAnalysisVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.service.impl.AnalysisWorkHandlerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-11-22 12:55
 **/
@RestController
@RequestMapping("innerapi/admin/work-order")
@Slf4j
public class WorkOrderAnalysisFeignController implements CfWorkOrderAnalysisClient {

    @Autowired
    AnalysisWorkHandlerServiceImpl workHandlerService;

    @RequestMapping(path = "whole-analysis", method = RequestMethod.POST)
    @Override
    public Response<List<WorkOrderAnalysisVO>> wholeAnalysis() {
        return NewResponseUtil.makeSuccess(workHandlerService.wholeAnalysis());
    }

    @RequestMapping(path = "period-analysis", method = RequestMethod.POST)
    @Override
    public Response<List<WorkOrderAnalysisVO>> periodAnalysis(int oneType) {
        return NewResponseUtil.makeSuccess(workHandlerService.periodAnalysis(oneType));
    }
}
