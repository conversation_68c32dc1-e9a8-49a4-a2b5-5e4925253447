package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.CfTwWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.ResultUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @DATE 2020/8/31
 */
@RestController
public class TwFeignController implements CfTwWorkOrderClient {


    @Resource(name = "twWorkOrderService")
    private WorkOrderFacade workOrderFacade;


    @Override
    public Response<Long> createTw(TwWorkOrder workOrder) {

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @Override
    @Deprecated
    public Response handleTw(HandleOrderParam param) {

        TwHandleOrderParam p = new TwHandleOrderParam();
        BeanUtils.copyProperties(param,p);
        
        OpResult opResult = workOrderFacade.doHandle(p);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());

    }

    @Override
    public Response handleTwOrder(TwHandleOrderParam param) {

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());

    }

    @Override
    public Response<PageResult<WorkOrderVO>> twOrderlist(WorkOrderListParam param) {
        
        if (StringUtils.isBlank(param.getHandleResult())){
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(), "handleResult 不能为空",null);
        }

        OpResult<PageResult<WorkOrderVO>> opResult = workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }
}
