package com.shuidihuzhu.workorder.controller.feign;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.PrWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.model.vo.PrWorkOrderVO;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.pr.common.utils.MetricsUtil;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.OperationStat;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.service.*;
import com.shuidihuzhu.workorder.service.custom.recruit.WorkOrderAssignExpAnalyzer;
import com.shuidihuzhu.workorder.service.custom.recruit.model.PrOptStat;
import com.shuidihuzhu.workorder.service.impl.PrWorkOrderServiceImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/26
 */
@Slf4j
@Api("招募工单")
@RestController
public class PrWorkOrderClientController implements PrWorkOrderClient {

    @Resource
    private PrWorkOrderServiceImpl workOrderFacade;
    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private BaseWorkOrderService baseWorkOrderService;
    @Autowired
    private UserOperationRecordDao recordDao;
    @Autowired
    private StaffStatusService staffStatusService;
    @Resource
    private OrderExtService orderExtService;
    @Resource
    private OrganizationService organizationService;
    @Resource
    private AssignWorkOrderService assignWorkOrderService;
    @Resource
    private StaffStatusDao staffStatusDao;
    @Resource
    private WorkOrderAssignExpAnalyzer workOrderAssignExpAnalyzer;
    @Resource
    private MetricsUtil metricsUtil;
    @Resource
    private MeterRegistry meterRegistry;
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public Response<Long> createPrWorkOrder(PrWorkOrder workOrder) {
        log.info("createPrWorkOrder workOrder={}", workOrder);
        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);
        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response hanldePr(PrHandleOrderParam param) {
        log.info("PrHandleOrderParam:{}", param);
        OpResult opResult = workOrderFacade.doHandle(param);
        reportHandle(param);
        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    private void reportHandle(PrHandleOrderParam param) {
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());
        if (workOrderBase == null) {
            return;
        }

        if (List.of(HandleResultEnum.undoing.getType(), HandleResultEnum.doing.getType(),
                HandleResultEnum.later_doing.getType()).contains(workOrderBase.getHandleResult())) {
            return;
        }

        metricsUtil.timer(PrOptStat.PR_WORK_ORDER_FINISH_TIMER,
                workOrderBase.getFinishTime().getTime()-workOrderBase.getHandleTime().getTime(),
                OperationStat.ORDERTYPE, workOrderBase.getOrderType()+"");

        meterRegistry.gauge(PrOptStat.PR_WORK_ORDER_FINISH_GAUGE,
                Tags.of(OperationStat.ORDERTYPE,
                        Optional.ofNullable(workOrderTypeService.getFromOrderTypeCode(workOrderBase.getOrderType()))
                                .map(WorkOrderTypeDO::getMsg)
                                .orElse("")),
                workOrderBase.getFinishTime().getTime()-workOrderBase.getHandleTime().getTime());
    }

    @Override
    public Response hanldePrOrderList(long userId, List<Long> workOrderIdList, int handleResult) {
        log.info("hanldePrOrderList: userId{} handleResult:{} workOrderIdList:{}", userId, handleResult, workOrderIdList);
        OpResult opResult = workOrderFacade.handlePrOrderList(userId, workOrderIdList, handleResult);
        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response<PageResult<PrWorkOrderVO>> prOrderList(WorkOrderListParam param) {
        OpResult opResult = workOrderFacade.getOrderList(param);
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @Override
    public Response<List<WorkOrderVO>> queryByIds(List<Long> ids) {
        List<WorkOrderVO> workOrderVOS = workOrderFacade.queryByIds(ids);
        return NewResponseUtil.makeSuccess(workOrderVOS);
    }


    @Override
    public Response<List<StaffStat>> getPrStaffStatusStat(long userId) {
        return  NewResponseUtil.makeSuccess(staffStatusService.getStaffStatusStatByPermissions(userId, WorkOrderType.getPrPermissions()));
    }


    @Override
    public Response<Long> workOrderRelease(long workOrderId, long operatorId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (workOrder.getHandleResult() != HandleResultEnum.doing.getType()
                && workOrder.getHandleResult() != HandleResultEnum.later_doing.getType()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(Lists.newArrayList(workOrderId));
        if (result > 0) {
            List<WorkOrderRecord> records = WorkOrderRecord.freeRecord(Lists.newArrayList(workOrder), "手动回收", operatorId);
            recordDao.saveRecordList(records);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Long> workOrderCallback(long workOrderId, long operatorId, String comment) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (workOrder.getHandleResult() != HandleResultEnum.doing.getType()
                && workOrder.getHandleResult() != HandleResultEnum.later_doing.getType()
                && workOrder.getHandleResult() != HandleResultEnum.system_lock_patient_end.getType()) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(Lists.newArrayList(workOrderId));
        if (result > 0) {
            comment = StringUtils.isBlank(comment) ? "修改为待分配" : comment;
            WorkOrderRecord workOrderRecord = WorkOrderRecord.create(workOrder, operatorId, comment, OperateMode.callback);
            recordDao.saveRecord(workOrderRecord);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Integer> workOrderReleases(List<Long> workOrderIds, long operatorId) {
        if (CollectionUtils.isEmpty(workOrderIds)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<WorkOrderBase> workOrders = workOrderDao.listById(workOrderIds);
        if (CollectionUtils.isEmpty(workOrders)) {
            return NewResponseUtil.makeSuccess(null);
        }
        workOrders = workOrders.stream().filter(t -> (t.getHandleResult() == HandleResultEnum.doing.getType()
                || t.getHandleResult() == HandleResultEnum.later_doing.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrders)){
            return NewResponseUtil.makeSuccess(null);
        }
        workOrderIds = workOrders.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(workOrderIds);
        if (result > 0) {
            List<com.shuidihuzhu.workorder.model.WorkOrderRecord> records =
                    WorkOrderRecord.freeRecord(workOrders, "批量手动回收", operatorId);
            recordDao.saveRecordList(records);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<List<Long>> transferWorkOrder(List<Long> workOrderList, Long destOptUid, Long operatorId, String reason, List<Integer> unChangeHandleResult) {
        return NewResponseUtil.makeSuccess(assignWorkOrderService.transferWorkOrder(workOrderList, destOptUid, operatorId, reason, unChangeHandleResult));
    }

    @Override
    public Response<List<RawWorkOrder>> listRawWorkOrder(List<Long> workOrderIds) {
        return NewResponseUtil.makeSuccess(baseWorkOrderService.getRawWorkOrder(workOrderIds));
    }

    @Override
    public Response<Integer> updateWorkOrderOperatorId(List<Long> workOrderIds, Long operatorId) {
        if (CollectionUtils.isEmpty(workOrderIds) || operatorId == null || operatorId<=0){
            return NewResponseUtil.makeSuccess(0);
        }
        int operatorOrgId = organizationService.getUserOrgId(operatorId);
        return NewResponseUtil.makeSuccess(workOrderFacade.updateWorkOrderOperatorId(workOrderIds, operatorId, operatorOrgId));
    }

    @Override
    public Response<List<WorkOrderExt>> listOrderExtByIdsAndExtNames(List<Long> workOrderIds, List<String> extNames) {
        return NewResponseUtil.makeSuccess(orderExtService.listOrderExtByIdsAndExtNames(workOrderIds, extNames));
    }

    @Override
    public Response<List<StaffStatus>> listStaffOnline(List<Long> userIds, int workOrderType) {
        return NewResponseUtil.makeSuccess(staffStatusService.getStaffOnlineToday(userIds, workOrderType));
    }

    @Override
    public Response<List<StaffStatus>> listStaffByUserIdsOrderTypes(List<Long> userIds, List<Integer> workOrderTypes) {
        return NewResponseUtil.makeSuccess(staffStatusDao.getStaffs(userIds, workOrderTypes));
    }

    @Override
    public Response<Boolean> replaceExtValue(long orderId, String extName, String extValue) {
        return NewResponseUtil.makeSuccess(orderExtService.replaceExt(orderId, extName, extValue) > 0);
    }

    @Override
    public Response<Void> assignExpAnalyze() {
        workOrderAssignExpAnalyzer.assignExpAnalyze();
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> simpleTransfer(List<SimpleTransferParam> simpleTransferParams) {
        assignWorkOrderService.simpleTransfer(simpleTransferParams);
        return NewResponseUtil.makeSuccess(null);
    }

}
