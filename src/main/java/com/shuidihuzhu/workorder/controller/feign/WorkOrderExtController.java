package com.shuidihuzhu.workorder.controller.feign;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.service.WorkOrderExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-04-22 20:49
 **/
@RestController
public class WorkOrderExtController implements WorkOrderExtFeignClient {
    @Autowired
    private WorkOrderExtService workOrderExtService;

    @Override
    public Response<WorkOrderExtVO> addByNameValue(long workOrderId, String name, String value) {
        WorkOrderExtVO vo = workOrderExtService.addByNameValue(workOrderId, name, value);
        return NewResponseUtil.makeSuccess(vo);
    }

    @Override
    public Response<Void> addByList(List<WorkOrderExtVO> list) {
        workOrderExtService.addByList(list);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> markDeleteByName(long workOrderId, String name) {
        workOrderExtService.markDeleteByName(workOrderId, name);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> updateByNameValue(long workOrderId, String name, String value) {
        workOrderExtService.updateByNameValue(workOrderId, name, value);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<WorkOrderExtVO> getLastByName(long workOrderId, String name) {
        WorkOrderExtVO orderExtVO = workOrderExtService.getLastByName(workOrderId, name);
        return NewResponseUtil.makeSuccess(orderExtVO);
    }

    @Override
    public Response<List<WorkOrderExtVO>> getListByName(long workOrderId, String name) {
        List<WorkOrderExtVO> extVOS = workOrderExtService.getListByName(workOrderId, name);
        return  NewResponseUtil.makeSuccess(extVOS);
    }

    @Override
    public Response<List<WorkOrderExtVO>> getListByNameAndIdList(List<Long> workOrderIdList, String name) {
        List<WorkOrderExtVO> workOrderExtVOList = workOrderExtService.getListByNameAndIdList(workOrderIdList, name);
        return  NewResponseUtil.makeSuccess(workOrderExtVOList);
    }

    @Override
    public Response<Void> saveByList(long workOrderId, List<WorkOrderExt> extList) {
        Map<String, String> extMap = workOrderExtService.getWorkOrderExt(workOrderId).stream()
                .collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue, (o1, o2) -> o2));

        Map<String, String> extListMap = extList.stream().collect(Collectors.toMap(WorkOrderExt::getExtName, WorkOrderExt::getExtValue, (o1, o2) -> o2));
        for (Map.Entry<String, String> entry : extListMap.entrySet()) {
            if (extMap.containsKey(entry.getKey())){
                if (!entry.getValue().equals(extMap.get(entry.getKey()))){
                    workOrderExtService.updateByNameValue(workOrderId, entry.getKey(), entry.getValue());
                }
            }else{
                workOrderExtService.addByNameValue(workOrderId,entry.getKey(), entry.getValue());
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }

}
