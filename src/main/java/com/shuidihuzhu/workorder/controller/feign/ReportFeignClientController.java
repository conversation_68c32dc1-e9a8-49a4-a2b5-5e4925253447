package com.shuidihuzhu.workorder.controller.feign;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.CfReportWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.*;
import com.shuidihuzhu.workorder.service.mq.ReportWorkOrderAssignateImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/12/26 下午3:29
 * @desc
 */
@Slf4j
@Api("举报工单")
@RestController
public class ReportFeignClientController implements CfReportWorkOrderClient {

    @Resource(name = "reportWorkOrderServiceImpl")
    private WorkOrderFacade workOrderFacade;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private WorkOrderDaoExt workOrderDaoExt;

    @Resource
    private OrganizationService organizationService;
    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private ReportWorkOrderAssignateImpl reportWorkOrderAssignateImpl;

    @Override
    public Response<Long> createReport(ReportWorkOrder workOrder) {
        log.info("createReport workOrder={}",workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response<Long> mappingReport(ReportMappingParam mappingParam) {
        if(Objects.isNull(mappingParam) || mappingParam.getWorkOrderId() <= 0 || mappingParam.getReportId() <= 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        long workOrderId = mappingParam.getWorkOrderId();
        WorkOrderVO workOrderVO = baseWorkOrderService.getWorkOrderById(workOrderId);
        if(Objects.isNull(workOrderVO) || !WorkOrderType.REPORT_TYPES.contains(workOrderVO.getOrderType())){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        final int handleResult = workOrderVO.getHandleResult();
        if(handleResult == HandleResultEnum.noneed_deal.getType() || handleResult == HandleResultEnum.end_deal.getType()){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        WorkOrderExt orderExt = new WorkOrderExt();
        orderExt.setWorkOrderId(workOrderId);
        orderExt.setExtName(OrderExtName.reportId.getName());
        orderExt.setExtValue(String.valueOf(mappingParam.getReportId()));

        long id = orderExtService.createWorkOrderExt(Lists.newArrayList(orderExt));

        if(id <= 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        return NewResponseUtil.makeSuccess(id);
    }

    @Override
    public Response handleReport(ReportHandleOrderParam param) {

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response<PageResult<WorkOrderVO>> reportOrderlist(WorkOrderListParam param) {
        OpResult opResult =  workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

    @Override
    public Response<List<WorkOrderVO>> queryReportByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<WorkOrderBase> workOrderBases = workOrderDao.listById(ids);


        List<WorkOrderVO> workOrderVOS = workOrderBases.stream().map(workOrderBase -> {

            List<WorkOrderExt> exts = workOrderDaoExt.getWorkOrderExtByName(workOrderBase.getId(), Lists.newArrayList(OrderExtName.reportId.getName()));
            List<String> reportIds = exts.stream().map(WorkOrderExt::getExtValue).collect(Collectors.toList());

            WorkOrderVO vo = new WorkOrderVO();
            vo.setWorkOrderId(workOrderBase.getId());
            vo.setCaseId(workOrderBase.getCaseId());
            vo.setOrderType(workOrderBase.getOrderType());
            vo.setOperatorId(workOrderBase.getOperatorId());
            vo.setUpdateTime(workOrderBase.getUpdateTime());
            vo.setHandleTime(workOrderBase.getHandleTime());
            vo.setFinishTime(workOrderBase.getFinishTime());
            vo.setHandleResult(workOrderBase.getHandleResult());
            vo.setReportIds(String.join(",", reportIds));

            return vo;

        }).collect(Collectors.toList());

        return ResponseUtil.makeSuccess(workOrderVOS);
    }

    @Override
    public Response<String> workOrderTransfer(WorkOrderTranferParam param) {
        if(Objects.isNull(param) || param.getSourceUserId() <= 0 || param.getTargetUserId() <= 0 || param.getWorkOrderId() <= 0){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(param.getWorkOrderId());
        if(Objects.isNull(workOrderBase) || param.getSourceUserId() != workOrderBase.getOperatorId()){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int operatorOrgId = organizationService.getUserOrgId(param.getTargetUserId());
        int res = workOrderDao.updateOperator(param.getWorkOrderId(), param.getTargetUserId(), operatorOrgId);
        if(res > 0){

            List<WorkOrderExt> exts = Lists.newArrayList();

            WorkOrderExt orderExt = new WorkOrderExt();
            orderExt.setWorkOrderId(param.getWorkOrderId());
            orderExt.setExtName(OrderExtName.workOrderTranfer.getName());
            orderExt.setExtValue(param.getSourceUserId() + ":" + param.getTargetUserId());
            exts.add(orderExt);

            orderExtService.createWorkOrderExt(exts);

            return ResponseUtil.makeSuccess("success");
        }

        return ResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
    }

    @Override
    public Response<Integer> batchAllocation(List<Long> ids, int orderType, int operatorId, int recipientId) {
        //校验是否在线
        StaffStatus staffStatus = staffStatusService.getStaffStatus(recipientId, orderType);
        if (staffStatus.getStaffStatus() != StaffStatusEnum.online.getType()) {
            return NewResponseUtil.makeError(com.shuidihuzhu.workorder.model.enums.ErrorCode.TARGET_IS_OFFLINE);
        }

        //校验处理的工单数量
        List<WorkOrderDoingCount> workOrderDoingCounts = workOrderDao.getDoingCount(List.of((long) recipientId),
                orderType, HandleResultEnum.doing.getType());

        List<StaffStatus> staffList = staffStatusService.getStaffOnlineToday(List.of((long) recipientId), orderType);
        log.info("reportTransfer list:{},staffList:{}", JSON.toJSONString(workOrderDoingCounts), JSON.toJSONString(staffList));

        int doingCount = CollectionUtils.isNotEmpty(workOrderDoingCounts) ? workOrderDoingCounts.get(0).getNum() : 0;
        int receiptThreshold = CollectionUtils.isNotEmpty(staffList) ? staffList.get(0).getReceiptThreshold() : 0;

        if (orderType == WorkOrderType.casefirstreport.getType() && doingCount >= receiptThreshold) {
            return NewResponseUtil.makeError(com.shuidihuzhu.workorder.model.enums.ErrorCode.RECEIPT_THRESHOLD_ERROR);
        }

        //分配
        int result = baseWorkOrderService.batchAllocation(ids, orderType, operatorId, recipientId);

        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Integer> getDoingCount() {
        var handleCount = reportWorkOrderAssignateImpl.getHandleCount();
        log.info("ReportFeignClientController.getDoingCount handleCount:{}", handleCount);
        return NewResponseUtil.makeSuccess(handleCount);
    }
}
