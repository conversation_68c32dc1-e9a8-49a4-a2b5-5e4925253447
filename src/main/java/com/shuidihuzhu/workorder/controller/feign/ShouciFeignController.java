package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.ShouciHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.ShouciWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.ShouciWorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.OrderExtName;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.OrderExtService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/18
 */
@Api("首次沟通工单")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class ShouciFeignController {


    @Resource(name = "shouciWorkOrderService")
    private WorkOrderFacade workOrderFacade;

    @Resource
    private OrderExtService orderExtService;

    @ApiOperation(("创建首次沟通工单"))
    @RequestMapping(path = "create-shouci",method = RequestMethod.POST)
    public Response<Long> createShouci(@RequestBody ShouciWorkOrder workOrder){

        log.info("createShouci workOrder={}",workOrder);

        // 创建工单
        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation("处理首次沟通工单")
    @RequestMapping(path = "hanlde-shouci",method = RequestMethod.POST)
    public Response Handlsehouci(@RequestBody ShouciHandleOrderParam param ){

       log.info("handlsehouci ShouciHandleOrderParam={}",param);

       OpResult opResult = workOrderFacade.doHandle(param);


       return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }


    @ApiOperation("查询首次沟通工单")
    @RequestMapping(path = "shouci-orderlist",method = RequestMethod.POST)
    public Response<PageResult<WorkOrderVO>> shouciOrderList(@RequestBody ShouciWorkOrderListParam param){
        log.info("request body :{}",param);
        OpResult opResult =  workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("将首次沟通工单直接与天润实际呼通状态关联")
    @RequestMapping(path = "shouci-bind-call-unicode",method = RequestMethod.POST)
    public Response<Void> bindShouCiCallUnicode(@RequestParam long workOrderId, @RequestParam String callUnicode){
        orderExtService.saveIfNotEmpty(workOrderId, OrderExtName.callUnicode, callUnicode);
        return NewResponseUtil.makeSuccess(null);
    }

}
