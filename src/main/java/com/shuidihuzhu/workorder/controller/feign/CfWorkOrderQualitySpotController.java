package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.CfWorkOrderQualitySpotClient;
import com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.service.CfWorkOrderQualitySpotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/8/23
 */
@RestController
@Slf4j
public class CfWorkOrderQualitySpotController implements CfWorkOrderQualitySpotClient {

    @Resource
    private CfWorkOrderQualitySpotService cfWorkOrderQualitySpotService;


    @Override
    public Response<List<RiskQualitySpotWorkOrderUserConfig>> listByUserIds(List<Long> userIds, Long scene) {
        return NewResponseUtil.makeSuccess(cfWorkOrderQualitySpotService.listByUserIds(userIds, scene));
    }

    @Override
    public Response<RiskQualitySpotWorkOrderUserConfig> getByUserId(Long userId, Long scene) {
        return NewResponseUtil.makeSuccess(cfWorkOrderQualitySpotService.getByUserId(userId, scene));
    }

    @Override
    public Response<Boolean> save(RiskQualitySpotWorkOrderUserConfig config) {
        return NewResponseUtil.makeSuccess(cfWorkOrderQualitySpotService.save(config));
    }

    @Override
    public Response<Boolean> update(RiskQualitySpotWorkOrderUserConfig config) {
        return  NewResponseUtil.makeSuccess(cfWorkOrderQualitySpotService.update(config));
    }
}
