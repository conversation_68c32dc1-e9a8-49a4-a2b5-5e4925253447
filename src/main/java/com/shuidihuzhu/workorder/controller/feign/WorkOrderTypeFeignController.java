package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.CfWorkOrderTypeFeignClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderTypeRecord;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api("工单类型信息")
@RestController
public class WorkOrderTypeFeignController implements CfWorkOrderTypeFeignClient {
    @Resource
    private WorkOrderTypeService workOrderTypeService;

    @Override
    public Response<WorkOrderTypeRecord> getByOrderTypeCode(int orderType) {

        WorkOrderTypeDO workOrderTypeDO = workOrderTypeService.getFromOrderTypeCode(orderType);
        if(workOrderTypeDO == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }
        // 转换
        WorkOrderTypeRecord workOrderTypeRecord = new WorkOrderTypeRecord();
        BeanUtils.copyProperties(workOrderTypeDO, workOrderTypeRecord);
        return NewResponseUtil.makeSuccess(workOrderTypeRecord);
    }

    @Override
    public Response<List<Integer>> getByOneLevel(int oneType) {
        List<Integer> list = workOrderTypeService.getByOneLevel(oneType);
        return NewResponseUtil.makeSuccess(list);
    }

    @Override
    public Response<Integer> getOneFromTwo(int twoType) {
        Integer oneType = workOrderTypeService.getOneFromTwo(twoType);
        return NewResponseUtil.makeSuccess(oneType);
    }
}
