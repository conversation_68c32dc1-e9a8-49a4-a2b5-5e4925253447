package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.StaffStatus;
import com.shuidihuzhu.workorder.model.WorkOrderTypeDO;
import com.shuidihuzhu.client.cf.workorder.model.enums.StaffStatusEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.EnvService;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.StaffStatusDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.StaffStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */
@Api("工单处理人员管理")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/staffs")
public class StaffStatusFeignController {

    @Autowired
    private StaffStatusDao staffStatusDao;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private EnvService envService;

    @Resource
    private WorkOrderTypeService workOrderTypeService;

    private static final int MAX_OFFLINE_TIME_BY_SELF  = 2;

    @ApiOperation("变更人员状态")
    @RequestMapping(path = "change-status",method = RequestMethod.POST)
    public Response changeStatus(@RequestBody StaffStatus staffStatus){

    log.info("changeStatus StaffStatus={}",staffStatus);

        if (!staffStatusService.canSelfOnline(staffStatus)) {
            log.info("不能自动在线:{}", staffStatus);
            return NewResponseUtil.makeError(ErrorCode.STAFF_OFFLINE_BY_MANAGER);
        }

        if (!staffStatusService.canBothOnline(staffStatus)) {
            log.info("不能同时在线:{}", staffStatus);
            return NewResponseUtil.makeError(ErrorCode.CANNOT_BOTH_ON_LINE);
        }
        if (WorkOrderType.casehistoryreport.getType() != staffStatus.getOrderType()
                && WorkOrderType.up_grade_second.getType() != staffStatus.getOrderType()
                && WorkOrderType.lost_report.getType() != staffStatus.getOrderType()
                && !WorkOrderType.QC_WORK_ORDER_LIST.contains(staffStatus.getOrderType())) {
            //本人操作的离线
            boolean offLineBySelf = staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType() && staffStatus.getOperType() == 0;
            if (offLineBySelf) {
                int countOfflineTimesToday = staffStatusService.countOfflineTimesToday(staffStatus);
                int offTimes = MAX_OFFLINE_TIME_BY_SELF;
                if (staffStatus.getOrderType() == WorkOrderType.cailiao_fuwu.getType()){
                    offTimes = 3;
                }
                if (countOfflineTimesToday >= offTimes && envService.isProduction()) {
                    return NewResponseUtil.makeError(ErrorCode.OFFLINE_LIMIT_REMIND);
                }
            }
        }


    int result = staffStatusService.changeStatus(staffStatus);

     if (result>0){
         return NewResponseUtil.makeSuccess("succ");
     }

     return NewResponseUtil.makeError(ErrorCode.SYSTEM_PERMISSION_ERROR);

    }

    @ApiOperation("变更人员状态")
    @RequestMapping(path = "update-receipt-threshold",method = RequestMethod.POST)
    public Response updateReceiptThreshold(@RequestBody StaffStatus staffStatus){

        WorkOrderTypeDO orderType = workOrderTypeService.getFromOrderTypeCode(staffStatus.getOrderType());
        if(orderType == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        StaffStatus ss = staffStatusService.getStaffStatus(staffStatus.getUserId(), orderType.getOrderTypeCode());
        if(Objects.isNull(ss)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_STAFF_ERROR);
        }

        ss.setReceiptThreshold(staffStatus.getReceiptThreshold());
        ss.setOperationId(staffStatus.getOperationId());

        staffStatusDao.changeStatus(ss);

        if (staffStatus.getOrderType() == WorkOrderType.casefirstreport.getType()) {
            //发送分配工单event
            staffStatusService.publishEvent(staffStatus.getOrderType());
        }

        return NewResponseUtil.makeSuccess(ss);
    }

    @PostMapping(path = "update-auto-allocation")
    public Response updateAutoAllocation(@RequestBody StaffStatus staffStatus){
        int orderType = staffStatus.getOrderType();
        if(!WorkOrderType.REPORT_TYPES.contains(orderType)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_TYPE_ERROR);
        }

        StaffStatus ss = staffStatusService.getStaffStatus(staffStatus.getUserId(), orderType);
        if(Objects.isNull(ss)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_STAFF_ERROR);
        }

        ss.setAutoAllocation(staffStatus.getAutoAllocation());
        ss.setOperationId(staffStatus.getOperationId());

        staffStatusDao.changeStatus(ss);

        return NewResponseUtil.makeSuccess(ss);

    }

    @ApiOperation("获取状态")
    @RequestMapping(path = "get-staff-status",method = RequestMethod.POST)
    public Response<StaffStatus> getStaffStatus(int orderType,long userId){

        log.info("getStaffStatus orderType={} userId={}",orderType,userId);

        StaffStatus staffStatus = staffStatusService.getStaffStatus(userId,orderType);

        return NewResponseUtil.makeSuccess(staffStatus);

    }

    @RequestMapping(path = "get-staff-types",method = RequestMethod.POST)
    public Response<List<StaffStatus>> getStaffType(String orderType,long userId){

        log.info("getStaffType orderType={} userId={}",orderType,userId);

        List<Integer> types = Arrays.stream(orderType.split(",")).map(Integer::valueOf).collect(Collectors.toList());

        List<StaffStatus> list = staffStatusService.getStaffStatusByTypes(userId,types);

        return NewResponseUtil.makeSuccess(list);

    }

    @RequestMapping(path = "get-staff-list",method = RequestMethod.POST)
    public Response<PageResult<StaffStatus>> getStaffStatusList(String orderType,int pageSize, String paging,long userId,
                                                                @RequestParam(value = "isAll",required = false) boolean isAll,
                                                                @RequestParam(value = "operatorId",required = false) long operatorId){

        log.info("getStaffStatusList orderType={} userId={},pageSize={},paging={}",orderType,userId,pageSize,paging);

        List<Integer> types = Arrays.stream(orderType.split(",")).map(Integer::valueOf).collect(Collectors.toList());

        OpResult<PageResult<StaffStatus>> opResult = staffStatusService.getStaffStatusByTypes(types,pageSize,paging,userId,isAll,operatorId);

        return NewResponseUtil.makeSuccess(opResult.getData());

    }

    @ApiOperation("获取当前有权限的处理人员列表")
    @RequestMapping(path = "get-staff-permissions",method = RequestMethod.POST)
    public Response<Set<StaffStatus>> getStaffPermissions(){

        Set<StaffStatus> set = staffStatusService.getStaffStatByPermissions();

        return NewResponseUtil.makeSuccess(set);

    }


    @RequestMapping(path = "auto-off",method = RequestMethod.POST)
    public Response autoOff(){
        log.info("autoOff start");

        staffStatusService.autoOff();

        log.info("autoOff end");
        return NewResponseUtil.makeSuccess("ok");
    }

    @RequestMapping(path = "report-auto-off",method = RequestMethod.POST)
    public Response reportAutoOff(){
        log.info("reportAutoOff start");

        staffStatusService.reportAutoOff();

        return NewResponseUtil.makeSuccess("ok");
    }

    @RequestMapping(path = "get-by-order-type", method = RequestMethod.POST)
    public Response<List<StaffStatus>> getStaffStatusByOrderType(String orderType) {
        List<StaffStatus> staffStatusList = staffStatusService.getStaffStatusByOrderType(orderType);
        return NewResponseUtil.makeSuccess(staffStatusList);
    }

    @RequestMapping(path = "clear-off-job-users", method = RequestMethod.POST)
    public Response<String> clearOffJobUsers() {
        staffStatusService.clearOffJobUsers();
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "change-auto-on-line-status", method = RequestMethod.POST)
    public Response<String> changeAutoOnLineStatus(@RequestBody StaffStatus staffStatus) {

        staffStatusService.changeAutoOnLineStatus(staffStatus);
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "clear-auto-off-line-status", method = RequestMethod.POST)
    public Response<String> clearAutoOfflineStatus() {
        staffStatusService.clearAutoOfflineStatus();
        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "allow-assign-ordertype", method = RequestMethod.POST)
    public Response<String> allowAssignOrdertype(@RequestParam(value = "userId") long userId,
                                                 @RequestParam(value = "allowAssign") String allowAssign) {
        staffStatusService.allowAssign(userId,allowAssign);

        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "update-threshold-by-type", method = RequestMethod.POST)
    public Response<String> updateThresholdByType(@RequestParam(value = "orderType") int orderType,
                                                  @RequestParam(value = "threshold") int threshold) {
        staffStatusService.updateThresholdByType(orderType,threshold);

        return NewResponseUtil.makeSuccess("");
    }


    @RequestMapping(path = "free-by-userId", method = RequestMethod.POST)
    public Response<String> freeByUserId(@RequestParam(value = "orderType") int orderType,
                                         @RequestParam(value = "userIds") String userIds){

//        List<Long> Ids = Arrays.stream(userIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
//        staffStatusService.freeByUserId(Ids,orderType, startTime, endTime);

        return NewResponseUtil.makeError(ErrorCode.SYSTEM_NO_OPERATION_ERROR);
    }

    @RequestMapping(path = "free-by-userId-v2", method = RequestMethod.POST)
    Response<String> freeByUserIdV2(@RequestParam(value = "orderType") int orderType,
                                    @RequestParam(value = "userIds") String userIds,
                                    @RequestParam(value = "startTime", required = false) Date startTime,
                                    @RequestParam(value = "endTime", required = false) Date endTime){

        List<Long> Ids = Arrays.stream(userIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        staffStatusService.freeByUserId(Ids,orderType, startTime, endTime);

        return NewResponseUtil.makeSuccess("");
    }

    @RequestMapping(path = "get-threshold-by-type", method = RequestMethod.POST)
    public Response<StaffStatus> getThresholdByType(@RequestParam(value = "orderType") int orderType,
                                                    @RequestParam(value = "userId") Long userId) {

        return NewResponseUtil.makeSuccess(staffStatusService.getThresholdByType(orderType,userId));
    }

}
