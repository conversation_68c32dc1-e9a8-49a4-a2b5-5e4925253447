package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.OrderStat;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.service.juanzhuanStatService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @DATE 2019/10/10
 */
@Api("捐转工单统计")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/juanzhan/stat")
public class JuanzhanStatFeignController {

    @Autowired
    private juanzhuanStatService juanzhuanStatService;


    @RequestMapping(path = "juanzhanstat", method = RequestMethod.POST)
    public Response<String> juanzhanstat() {

        juanzhuanStatService.statAll();
        juanzhuanStatService.statInner();
        juanzhuanStatService.statOuter();
        juanzhuanStatService.statUser();

        return NewResponseUtil.makeSuccess("");
    }


    @RequestMapping(path = "juanzhanstat-org", method = RequestMethod.POST)
    public Response<String> juanzhanstatOrg(@RequestBody List<Integer> orgIds) {

        juanzhuanStatService.statOrg(orgIds);

        return NewResponseUtil.makeSuccess("");

    }

    @RequestMapping(path = "juanzhanstat-list", method = RequestMethod.POST)
    public Response<List<OrderStat>> juanzhanstatList(@RequestBody List<Integer> batchHours,
                                                      @RequestParam("statType") int statType) {
        List<OrderStat> list = juanzhuanStatService.getList(batchHours,statType);
        return NewResponseUtil.makeSuccess(list);
    }




}