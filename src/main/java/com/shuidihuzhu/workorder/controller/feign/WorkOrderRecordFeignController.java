package com.shuidihuzhu.workorder.controller.feign;


import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.vo.WorkOrderRecordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api("工单操作记录-工单日志")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/work-order/record")
public class WorkOrderRecordFeignController {

    @Autowired
    private UserOperationRecordDao userOperationRecordDao;
    @Autowired
    private UserFeignClient userFeignClient;

    @ApiOperation("工单记录/日志 列表")
    @PostMapping("list-by-work-order-id")
    public Response<List<WorkOrderRecordVO>> listByWorkOrderId(@RequestParam long workOrderId) {

        List<WorkOrderRecordVO> resultList = listViewByWorkOrderId(workOrderId);

        return NewResponseUtil.makeSuccess(resultList);
    }

    @ApiOperation("工单记录/日志 列表")
    @PostMapping("list-by-work-order-ids-and-order-opt-modes")
    public Response<List<com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord>> listByWorkOrderIdsAndOptModes(
            @RequestParam("workOrderIds") List<Long> workOrderIds, @RequestParam List<Integer> optModes) {

        List<com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord> workOrderRecords =
                userOperationRecordDao.listByWorkOrderIdsAndOptModes(workOrderIds, optModes)
                .stream()
                .map(recordDb-> {
                    com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord workOrderRecord =
                            new com.shuidihuzhu.client.cf.workorder.model.WorkOrderRecord();
                    BeanUtils.copyProperties(recordDb, workOrderRecord);
                    return workOrderRecord;
                }).collect(Collectors.toList());

        return NewResponseUtil.makeSuccess(workOrderRecords);
    }


    @PostMapping("insert-order-record-list")
    public Response<Integer> insertWorkOrderRecordList(@RequestBody List<WorkOrderRecord> recordVOList) {

        log.info("工单操作记录保存 param:{}", recordVOList);

        return NewResponseUtil.makeSuccess(userOperationRecordDao.saveRecordList(recordVOList));
    }



    private List<WorkOrderRecordVO> listViewByWorkOrderId(long workOrderId) {
        List<WorkOrderRecord> list = userOperationRecordDao.listByWorkOrderId(workOrderId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<Integer> userIds = list.stream().map(v -> (int) (v.getOperatorId())).distinct().collect(Collectors.toList());
        Map<Integer, AuthUserDto> userMap = getUserMap(userIds);
        if(MapUtils.isEmpty(userMap)){
            return Collections.emptyList();
        }
        return list.stream().map(v -> {
            WorkOrderRecordVO b = new WorkOrderRecordVO();
            AuthUserDto user = userMap.get((int) v.getOperatorId());
            b.setOperatorName(user == null ? "系统" : user.getUserName());
            BeanUtils.copyProperties(v, b);
            return b;
        }).collect(Collectors.toList());
    }



    @NotNull
    private Map<Integer, AuthUserDto> getUserMap(List<Integer> userIds) {
        List<Long> users = userIds.stream().map(Integer::longValue).collect(Collectors.toList());
        Map<Integer, AuthUserDto> map = new HashMap<>();

        Response<List<AuthUserDto>> rpcResponse = userFeignClient.getAuthUserByIds(users);
        if (rpcResponse.notOk() || rpcResponse.getData() == null) {
            log.error("获取用户信息失败, userIds:{}", userIds);
            return map;
        }

        List<AuthUserDto> userDtos = rpcResponse.getData();
        Map<Long, AuthUserDto> dtoMap = userDtos.stream()
                .collect(Collectors.toMap(AuthUserDto::getUserId, Function.identity(), (o1, o2) -> o2));
        for (Map.Entry<Long, AuthUserDto> entry : dtoMap.entrySet()) {
            map.put(entry.getKey().intValue(), entry.getValue());
        }
        return map;
    }

}
