package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.config.WorkOrderConfigFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigFacade;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
public class WorkOrderConfigFeignController implements WorkOrderConfigFeignClient {

    @Autowired
    private VonConfigFacade vonConfigService;

    @Override
    public Response<Void> getConfig() {
        throw new RuntimeException("WorkOrderConfigFeignController");
    }

    @Override
    public Response<Map<Integer, List<Integer>>> getDoneListConfig() {
        return vonConfigService.getDoneListConfig();
    }


}
