package com.shuidihuzhu.workorder.controller.feign;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.feign.CrowdfundingFeignClient;
import com.shuidihuzhu.cf.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.workorder.CfQcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.workorder.dao.UserOperationRecordDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.dao.WorkOrderDaoExt;
import com.shuidihuzhu.workorder.dao.WorkOrderOrgRelDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.WorkOrderRecord;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.enums.OperateMode;
import com.shuidihuzhu.workorder.repository.WorkOrderRepository;
import com.shuidihuzhu.workorder.service.*;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lixiaoshuang
 * @create: 2020-06-12 14:55
 **/
@Slf4j
@Api("质检工单")
@RestController
public class QcFeignClientController implements CfQcWorkOrderClient {

    @Resource(name = "qcWorkOrderServiceImpl")
    private WorkOrderFacade workOrderFacade;
    @Autowired
    private WorkOrderDao workOrderDao;
    @Autowired
    private WorkOrderRepository workOrderRepository;
    @Autowired
    private OrderExtService orderExtService;
    @Autowired
    private StaffStatusService staffStatusService;
    @Autowired
    private AssignWorkOrderService assignWorkOrderService;
    @Autowired
    private WorkOrderDaoExt workOrderDaoExt;
    @Autowired
    private CrowdfundingFeignClient crowdfundingFeignClient;
    @Autowired
    private BaseWorkOrderService baseWorkOrderService;
    @Autowired
    private UserOperationRecordDao recordDao;
    @Autowired
    private WorkOrderOrgRelDao workOrderOrgRelDao;


    @Override
    public Response<Long> createQcWorkOrder(QcWorkOrder workOrder) {
        log.info("createQcWorkOrder workOrder={}", workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response handleQc(QcHandleOrderParam param) {
        OpResult opResult = workOrderFacade.doHandle(param);
        return NewResponseUtil.makeResponse(opResult.getErrorCode().getCode(), opResult.getErrorCode().getMsg(), opResult.getData());
    }

    @Override
    public Response<Long> workOrderTransfer(long workOrderId, int orderType, int operatorId, long assignerId) {
        return workOrderTransferV2(workOrderId, orderType, operatorId, assignerId, true);
    }

    @Override
    public Response<Long> workOrderTransferV2(long workOrderId, int orderType, int operatorId, long assignerId, boolean onlineCheck) {
        if (workOrderId <= 0 || orderType <= 0 || operatorId <= 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        WorkOrderBase workOrderBase = workOrderDao.getWorkOrderById(workOrderId);
        if (Objects.isNull(workOrderBase)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        StaffStatus staffStatus = staffStatusService.getStaffStatus(assignerId, orderType);
        boolean offline = Objects.isNull(staffStatus) ||
                staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType();
        if (onlineCheck && offline) {
            return NewResponseUtil.makeError(ErrorCode.TARGET_IS_OFFLINE);
        }
        Response<Long> longResponse = assignWorkOrderService.qcAssignWorkOrder(workOrderId, operatorId, assignerId);
        if (longResponse.notOk()) {
            return longResponse;
        }

        List<WorkOrderExt> exts = Lists.newArrayList();
        WorkOrderExt orderExt = new WorkOrderExt();
        orderExt.setWorkOrderId(workOrderId);
        orderExt.setExtName(OrderExtName.workOrderTranfer.getName());
        orderExt.setExtValue(workOrderBase.getOperatorId() + ":" + operatorId);
        exts.add(orderExt);
        orderExtService.createWorkOrderExt(exts);

        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Long> workOrderRelease(long workOrderId, long operatorId) {
        WorkOrderBase workOrder = workOrderDao.getWorkOrderById(workOrderId);
        if (workOrder == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(Lists.newArrayList(workOrderId));
        if (result > 0) {
            List<com.shuidihuzhu.workorder.model.WorkOrderRecord> records = WorkOrderRecord.freeRecord(Lists.newArrayList(workOrder), "手动回收", operatorId);
            recordDao.saveRecordList(records);
        }
        return NewResponseUtil.makeSuccess(workOrderId);
    }

    @Override
    public Response<Integer> qcWorkOrderReleases(@RequestParam List<Long> workOrderIds,@RequestParam long operatorId) {
        if (CollectionUtils.isEmpty(workOrderIds)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<WorkOrderBase> workOrders = workOrderDao.listById(workOrderIds);
        if (CollectionUtils.isEmpty(workOrders)) {
            return NewResponseUtil.makeSuccess(null);
        }
        workOrders = workOrders.stream().filter(t -> (t.getHandleResult() == HandleResultEnum.doing.getType()
        || t.getHandleResult() == HandleResultEnum.later_doing.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrders)){
            return NewResponseUtil.makeSuccess(null);
        }
        workOrderIds = workOrders.stream().map(WorkOrderBase::getId).collect(Collectors.toList());
        int result = baseWorkOrderService.freeWorkOrderWithNoCheckOldStatus(workOrderIds);
        if (result > 0) {
            List<com.shuidihuzhu.workorder.model.WorkOrderRecord> records = WorkOrderRecord.freeRecord(workOrders, "批量手动回收", operatorId);
            recordDao.saveRecordList(records);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<Integer> getAllCount(int orderType, int handleResult) {
        log.info("QcFeignClientController.getAllCount orderType={},handleResult={}", orderType, handleResult);
        List<WorkOrderBase> workOrderBases = workOrderDao.queryUndoOrderOrderByLevelAndTime(orderType, handleResult, null, null);
        List<List<WorkOrderBase>> partition = Lists.partition(workOrderBases, 1000);
        long totalCount = 0;
        for (List<WorkOrderBase> workOrderBaseList : partition) {
            List<Long> workOrderIds = workOrderBaseList.parallelStream().map(WorkOrderBase::getId).collect(Collectors.toList());
            List<WorkOrderExt> workOrderExts = workOrderDaoExt.getWorkOrderExts(workOrderIds, OrderExtName.qcAssignType.getName());
            long count = workOrderExts.parallelStream().filter(workOrderExt -> Integer.parseInt(workOrderExt.getExtValue()) == AssignTypeEnum.MUST_ASSIGN.getCode()).count();
            totalCount = totalCount + count;
        }
        return NewResponseUtil.makeSuccess(Math.toIntExact(totalCount));
    }


    @Override
    public Response<Integer> qcTransfers(List<Long> workOrderIds, int orderType, long operatorId, long targetUserId) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        StaffStatus staffStatus = staffStatusService.getStaffStatus(targetUserId, orderType);

        if (Objects.isNull(staffStatus)) {
            return NewResponseUtil.makeError(ErrorCode.OPERATION_FAILED);
        }

        if (staffStatus.getStaffStatus() == StaffStatusEnum.offline.getType()) {
            return NewResponseUtil.makeError(ErrorCode.TARGET_IS_OFFLINE);
        }
        int result = baseWorkOrderService.qcTransfer(workOrderIds, orderType, operatorId, targetUserId);
        if (result == -1) {
            return NewResponseUtil.makeError(ErrorCode.WORK_ORDER_STATUS_ERROR);
        }
        return NewResponseUtil.makeSuccess(result);
    }

    @Override
    public Response<PageResult<WorkOrderVO>> qcOrderlist(WorkOrderListParam param) {
        OpResult opResult = workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

    @Override
    public Response<List<WorkOrderVO>> queryQcByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return ResponseUtil.makeError(com.shuidihuzhu.common.web.enums.ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<WorkOrderBase> workOrderBases = workOrderDao.listById(ids);

        List<WorkOrderVO> workOrderVoS = workOrderBases.stream().map(workOrderBase -> {
            WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.qcId.getName());
            WorkOrderVO vo = new WorkOrderVO();
            vo.setWorkOrderId(workOrderBase.getId());
            int orderType = workOrderBase.getOrderType();
            if (orderType != WorkOrderType.qc_wx_1v1.getType()
                    && orderType != WorkOrderType.qc_call.getType()
                    && orderType != WorkOrderType.qc_wx_1v1_repeat.getType()
                    && orderType != WorkOrderType.qc_hospital_dept.getType()
            ) {
                vo.setCaseId(workOrderBase.getCaseId());
                FeignResponse<CrowdfundingInfo> crowdfundingInfoFeignResponse = crowdfundingFeignClient.getCaseInfoById(workOrderBase.getCaseId());
                if (crowdfundingInfoFeignResponse.ok() && Objects.nonNull(crowdfundingInfoFeignResponse.getData())){
                    var crowdfundingInfo = crowdfundingInfoFeignResponse.getData();
                    vo.setTitle(crowdfundingInfo.getTitle());
                    vo.setCaseUuid(crowdfundingInfo.getInfoId());
                    vo.setCaseUserId(crowdfundingInfo.getUserId());
                }
            }

            if (orderType == WorkOrderType.qc_wx_1v1.getType()
                    || orderType == WorkOrderType.qc_call.getType()
                    || orderType == WorkOrderType.qc_wx_1v1_repeat.getType()) {
                //微信1v1工单的caseId保存的是1v1服务任务的id 不是案例id
                vo.setTaskId(workOrderBase.getCaseId());
            }
            vo.setOrderType(orderType);
            vo.setOperatorId(workOrderBase.getOperatorId());
            vo.setOrderLevel(workOrderBase.getOrderlevel());
            vo.setUpdateTime(workOrderBase.getUpdateTime());
            vo.setHandleTime(workOrderBase.getHandleTime());
            vo.setCreateTime(workOrderBase.getCreateTime());
            vo.setFinishTime(workOrderBase.getFinishTime());
            vo.setHandleResult(workOrderBase.getHandleResult());
            if (Objects.nonNull(workOrderExt)){
                vo.setQcId((Long.parseLong(workOrderExt.getExtValue())));
            }
            WorkOrderExt hospitalDeptExt = workOrderDaoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.hospitalDeptId.getName());
            if (hospitalDeptExt != null) {
                vo.setHospitalDeptId(Long.parseLong(hospitalDeptExt.getExtValue()));
            }
            return vo;

        }).collect(Collectors.toList());

        return ResponseUtil.makeSuccess(workOrderVoS);
    }

    @Override
    public Response<List<WorkOrderVO>> queryQcByBatch(int orderType, long currentWorkOrderId,
                                                      int pageSize, String startTime, String endTime) {
        if (orderType <= 0 || pageSize <= 0) {
            return ResponseUtil.makeSuccess(Collections.emptyList());
        }
        List<WorkOrderVO> workOrderVoS = this.getWorkOrderVOS(orderType, currentWorkOrderId, pageSize, startTime, endTime,
                HandleResultEnum.undoing.getType());
        return ResponseUtil.makeSuccess(workOrderVoS);
    }

    @Override
    public Response updateAssignType(List<Long> workOrders, int assignType) {
        if (CollectionUtils.isEmpty(workOrders)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        workOrderDaoExt.updateExtValue(workOrders, OrderExtName.qcAssignType.getName(), Integer.toString(assignType));
        String assignDesc;
        if (assignType == 1){
            assignDesc = "可分配";
        }else {
            assignDesc = "必须可分配";
        }
        if (assignType == AssignTypeEnum.MUST_ASSIGN.getCode()) {
            workOrderDao.updateHandleResultByIds(workOrders, HandleResultEnum.undoing.getType(),
                    Lists.newArrayList(HandleResultEnum.not_auto_assign.getType(), HandleResultEnum.no_auto_assign.getType()));
        } else {
            workOrderDao.updateHandleResultByIds(workOrders, HandleResultEnum.not_auto_assign.getType(),
                    Lists.newArrayList(HandleResultEnum.undoing.getType()));
        }
        //添加日志
        List<WorkOrderBase> workOrderBaseList = workOrderDao.listById(workOrders);
        List<WorkOrderRecord> workOrderRecords = Lists.newArrayList();
        for (WorkOrderBase workOrderBase : workOrderBaseList) {
            WorkOrderRecord record = new WorkOrderRecord();
            record.setWorkOrderType(workOrderBase.getOrderType());
            record.setOperatorId(workOrderBase.getOperatorId());
            record.setComment("状态更改为:"+assignDesc);
            record.setOperateMode(OperateMode.assignType.getType());
            record.setOperateDesc(OperateMode.assignType.getMsg());
            record.setWorkOrderId(workOrderBase.getId());
            workOrderRecords.add(record);
        }
        recordDao.saveRecordList(workOrderRecords);
        return NewResponseUtil.makeSuccess(null);
    }


    @Override
    public Response<List<WorkOrderVO>> queryListByBatch(long id, int handleResult,
                                                                    String time, int orderType, int limit){
        if (id < 0 || orderType < 0){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        List<WorkOrderBase> workOrderBases =  baseWorkOrderService.queryListByBatch(id, handleResult, time, orderType, limit);
        List<WorkOrderVO> workOrderVoS = workOrderBases.stream().map(workOrderBase -> {
            WorkOrderVO workOrderVO = new WorkOrderVO();
            workOrderVO.setWorkOrderId(workOrderBase.getId());
            workOrderVO.setCaseId(workOrderBase.getCaseId());
            workOrderVO.setOrderType(workOrderBase.getOrderType());
            return workOrderVO;
        }).sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId)).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(workOrderVoS);
    }


    @Override
    public Response<Integer> closeQcWorkOrderList(List<Long> workOrderIdList, int handleResult){
        if (CollectionUtils.isEmpty(workOrderIdList)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(baseWorkOrderService.closeQcOrderList(workOrderIdList, handleResult));
    }


    @Override
    public Response<Integer> openAgainQcWorkOrder(long workOrderId, long operatorId, String comment){
        if (workOrderId < 0 || StringUtils.isBlank(comment)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        return NewResponseUtil.makeSuccess(baseWorkOrderService.openAgainQcWorkOrder(workOrderId, operatorId, comment));
    }

    @Override
    public Response<Void> addExtValue(long workOrderId, String extName, String extValue) {
        var workOrderBase = workOrderDao.getWorkOrderById(workOrderId);
        if (Objects.isNull(workOrderBase)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_ERROR);
        }
        workOrderDaoExt.insertWorkOrderExt(workOrderId, extName, extValue);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<Void> addOrgId(List<WorkOrderOrgRel> workOrderOrgRels) {
        if (CollectionUtils.isEmpty(workOrderOrgRels)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_CON);
        }
        var orderIds = workOrderOrgRels.stream().map(WorkOrderOrgRel::getWorkOrderId).collect(Collectors.toList());
        var workOrderBases = workOrderDao.listById(orderIds);
        var workOrderBaseMap = workOrderBases.stream().collect(Collectors.toMap(WorkOrderBase::getId, Function.identity(), (k1, k2) -> k2));
        workOrderOrgRels.forEach(workOrderOrgRel -> {
            var workOrderBase = workOrderBaseMap.get(workOrderOrgRel.getWorkOrderId());
            if (Objects.nonNull(workOrderBase)) {
                workOrderOrgRel.setOrderType(workOrderBase.getOrderType());
            }
        });
        workOrderOrgRelDao.addList(workOrderOrgRels);
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<List<WorkOrderVO>> queryQcByBatchV2(int orderType, long currentWorkOrderId, int pageSize,
                                                        String startTime, String endTime, int handleResult) {
        if (orderType <= 0 || pageSize <= 0) {
            return ResponseUtil.makeSuccess(Collections.emptyList());
        }
        List<WorkOrderVO> workOrderVoS = getWorkOrderVOS(orderType, currentWorkOrderId, pageSize, startTime, endTime, handleResult);
        return ResponseUtil.makeSuccess(workOrderVoS);
    }

    @Override
    public Response<List<WorkOrderVO>> queryQcByBatchV3(int orderType, int pageSize, long currentWorkOrderId, String finishStartTime, String finishEndTime, Integer handleResult) {
        if (orderType <= 0 || pageSize <= 0) {
            return ResponseUtil.makeSuccess(Collections.emptyList());
        }
        List<WorkOrderVO> workOrderVoS = getWorkOrderVOSV3(orderType, pageSize, currentWorkOrderId ,finishStartTime, finishEndTime, handleResult);
        return ResponseUtil.makeSuccess(workOrderVoS);
    }

    @Override
    public Response<Long> queryMinIdQcByBatchV3(int orderType, String finishStartTime, String finishEndTime, Integer handleResult) {
        return ResponseUtil.makeSuccess(getMinIdQcByBatchV3(orderType, finishStartTime, finishEndTime, handleResult));
    }

    @Override
    public Response<List<WorkOrderExt>> getWorkOrderExtIgnoreIsDelete(long workOrderId, String extName) {
        if (workOrderId <= 0 || StringUtils.isEmpty(extName)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        List<WorkOrderExt> workOrderExts = workOrderDaoExt.getWorkOrderExtIgnoreDelete(workOrderId, extName);
        return NewResponseUtil.makeSuccess(workOrderExts);

    }


    @Override
    public Response<Long> qcAgainAssignWorkOrder(long workOrderId, long operatorId, long assignerId, String comment){
        if (workOrderId <=0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_CON);
        }
       return assignWorkOrderService.qcAgainAssignWorkOrder(workOrderId, operatorId, assignerId, comment);
    }

    @Override
    public Response<Integer> changeQcWorkOrderStatus(long workOrderId, long operatorId){
        if (workOrderId <=0 || operatorId < 0) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_ORDER_CON);
        }
        int result = baseWorkOrderService.changeQcWorkOrderStatus(workOrderId, operatorId);
        return NewResponseUtil.makeSuccess(result);
    }

    @NotNull
    private List<WorkOrderVO> getWorkOrderVOS(int orderType, long currentWorkOrderId, int pageSize, String startTime, String endTime, int handleResult) {
        List<WorkOrderBase> workOrderList = workOrderDao.getAllWorkorderList(0, List.of(orderType), pageSize,
                handleResult, 0,
                currentWorkOrderId, "pre", Collections.emptyList(), startTime, endTime,
                null, null, null, null, 0, "",0);

        return workOrderList.stream().map(workOrderBase -> {
            WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.qcId.getName());
            WorkOrderVO workOrderVO = new WorkOrderVO();
            workOrderVO.setWorkOrderId(workOrderBase.getId());
            if (orderType == WorkOrderType.qc_wx_1v1.getType()) {
                workOrderVO.setTaskId(workOrderBase.getCaseId());
            } else {
                workOrderVO.setCaseId(workOrderBase.getCaseId());
            }
            workOrderVO.setOrderType(workOrderBase.getOrderType());
            workOrderVO.setQcId((Long.parseLong(workOrderExt.getExtValue())));
            workOrderVO.setCreateTime(workOrderBase.getCreateTime());
            return workOrderVO;
        }).collect(Collectors.toList());
    }

    private List<WorkOrderVO> getWorkOrderVOSV3(int orderType, int pageSize,long currentWorkOrderId,
                                                String finishStartTime, String finishEndTime, Integer handleResult) {
        if (StringUtils.isBlank(finishStartTime) || StringUtils.isBlank(finishEndTime)) {
            return Lists.newArrayList();
        }
        List<WorkOrderBase> workOrderList = workOrderDao.findByFinishTime(orderType, pageSize,
                currentWorkOrderId, finishStartTime, finishEndTime, handleResult);

        return workOrderList.stream().map(workOrderBase -> {
            WorkOrderExt workOrderExt = workOrderDaoExt.getWorkOrderExt(workOrderBase.getId(), OrderExtName.qcId.getName());
            WorkOrderVO workOrderVO = new WorkOrderVO();
            workOrderVO.setWorkOrderId(workOrderBase.getId());
            if (orderType == WorkOrderType.qc_wx_1v1.getType()) {
                workOrderVO.setTaskId(workOrderBase.getCaseId());
            } else {
                workOrderVO.setCaseId(workOrderBase.getCaseId());
            }
            workOrderVO.setHandleTime(workOrderBase.getHandleTime());
            workOrderVO.setOrderType(workOrderBase.getOrderType());
            workOrderVO.setQcId((Long.parseLong(workOrderExt.getExtValue())));
            workOrderVO.setCreateTime(workOrderBase.getCreateTime());
            workOrderVO.setFinishTime(workOrderBase.getFinishTime());
            return workOrderVO;
        }).collect(Collectors.toList());
    }

    private Long getMinIdQcByBatchV3(int orderType, String finishStartTime, String finishEndTime, Integer handleResult) {
        return workOrderRepository.getMinIdQcByBatchV3(orderType, handleResult, finishStartTime, finishEndTime);
    }
}
