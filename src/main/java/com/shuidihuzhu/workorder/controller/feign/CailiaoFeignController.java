package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.CailiaoHandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.CailiaoWorkOrder;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderListParam;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.configuration.WorkOrderConfig;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.BaseWorkOrderService;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @DATE 2018/12/23
 */
@Api("材料审核工单")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class CailiaoFeignController {

    @Resource(name = "cailiaoWorkOrderService")
    private WorkOrderFacade workOrderFacade;


    /**
     * 创建工单
     * @param workOrder
     * @return
     */
    @RequestMapping(path = "create-cailiao",method = RequestMethod.POST)
    public Response<Long> createCailiao(@RequestBody CailiaoWorkOrder workOrder){

        log.info("createCailiao workOrder={}",workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());

    }


    /**
     * 处理工单
     * @param param
     * @return
     */
    @RequestMapping(path = "handle-cailiao",method = RequestMethod.POST)
    public Response handleCailiao(@RequestBody CailiaoHandleOrderParam param ){

        log.info("handleCailiao param={}",param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());

    }

    /**
     * 查询工单
     * @param param
     * @return
     */
    @RequestMapping(path = "cailiao-orderlist",method = RequestMethod.POST)
    public Response<List<WorkOrderVO>> cailoaoOrderlist (@RequestBody WorkOrderListParam param){

        log.info("cailoaoOrderlist param={}",param);

        if (StringUtils.isBlank(param.getHandleResult())){
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(), "handleResult 不能为空",null);
        }

        OpResult<List<WorkOrderVO>> opResult = workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

}
