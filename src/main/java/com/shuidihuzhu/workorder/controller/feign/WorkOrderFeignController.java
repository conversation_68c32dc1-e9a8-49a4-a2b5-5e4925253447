package com.shuidihuzhu.workorder.controller.feign;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.*;
import com.shuidihuzhu.client.cf.workorder.model.vo.AdminWorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.core.service.core.WorkOrderTypeService;
import com.shuidihuzhu.workorder.dao.WorkOrderDao;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount;
import com.shuidihuzhu.workorder.service.*;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @DATE 2018/12/27
 */
@Api("任务工单统计")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/type")
public class WorkOrderFeignController {

    @Autowired
    private WorkOrderTypeService workOrderTypeService;

    @Autowired
    private BaseWorkOrderService baseWorkOrderService;

    @Autowired
    private ClassifyTypeService classifyTypeService;

    @Autowired
    private StaffStatusService staffStatusService;

    @Autowired
    private WorkOrderTimelineSevice timelineSevice;

    @Autowired
    private AssignWorkOrderService assignWorkOrderService;

    @Autowired
    private OrderExtService orderExtService;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private WorkTypePropertyService workPropertyService;

    @Autowired
    private WorkTypePropertyService orderTypePropertyService;

    private List<Integer> unSupport_force_close = Lists.newArrayList();

    @PostConstruct
    public void init() {
        unSupport_force_close = workOrderTypeService.getByOneLevel(OneTypeEnum.zengxin.getType());
    }

    @ApiOperation("根据一级分类id获取所有二级分类id")
    @RequestMapping(path = "classify-type", method = RequestMethod.POST)
    public Response classifyType(int classifyType) {

        List<Integer> types = classifyTypeService.classifyType(classifyType);

        return NewResponseUtil.makeSuccess(types);
    }


    @ApiOperation("获取用户所有权限的工单分类信息")
    @RequestMapping(path = "user-classify", method = RequestMethod.POST)
    public Response<Map<Integer, List<Integer>>> userClassify(long userId) {

        Map<Integer, List<Integer>> map = classifyTypeService.userClassify(userId);

        return NewResponseUtil.makeSuccess(map);

    }

    @ApiOperation("获取用户所有权限的工单分类信息(原admin接口逻辑迁移)")
    @RequestMapping(path = "user-classify-all", method = RequestMethod.POST)
    public Response<List<AdminWorkOrderType>> userClassifyAll(long userId) {
        return classifyTypeService.userClassifyAll(userId);
    }


    @RequestMapping(path = "getUserCount", method = RequestMethod.POST)
    public Response<Integer> getUserCount(long userId, int orderType, String handleResult) {

        log.info("getUserCount userId={},orderType={},handleResult={}", userId, orderType, handleResult);
        if (StringUtils.isBlank(handleResult)) {
            return NewResponseUtil.makeError(SYSTEM_PARAM_ERROR);
        }

        int count = baseWorkOrderService.getCountByHandleResult(userId, orderType, handleResult);

        return NewResponseUtil.makeSuccess(count);
    }


    @RequestMapping(path = "getAllCount", method = RequestMethod.POST)
    public Response<Integer> getAllCount(int orderType,
                                         int handleResult) {

        log.info("getUserCount orderType={},handleResult={}", orderType, handleResult);

        return getAllCountV1(orderType,handleResult,0);
    }

    @RequestMapping(path = "getAllCountV1", method = RequestMethod.POST)
    public Response<Integer> getAllCountV1(int orderType,
                                           int handleResult,
                                           @RequestParam(value = "userId",defaultValue = "0",required = false) long userId) {

        log.info("getUserCount orderType={},handleResult={},userId={}", orderType, handleResult,userId);

        int count = baseWorkOrderService.getAllCountByHandleResult(orderType, handleResult,userId);

        return NewResponseUtil.makeSuccess(count);
    }


    @RequestMapping(path = "getWorkOrderCount", method = RequestMethod.POST)
    public Response<Integer> getWorkOrderCount(String orderTypes, String handleResult, String time) {
        if (StringUtils.isBlank(orderTypes) || StringUtils.isBlank(handleResult)){
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(),"orderTypes and handleResult shoud not blank",null);
        }

        List<Integer> results = Arrays.stream(handleResult.split(",")).filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList());
        List<Integer> orderType = Arrays.stream(orderTypes.split(",")).filter(StringUtils::isNumeric).map(Integer::valueOf).collect(Collectors.toList());

        if (results.isEmpty() || orderType.isEmpty()){
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(),"orderTypes and handleResult shoud not blank",null);
        }

        int count = baseWorkOrderService.getOrderCount(orderType, results, time);

        return NewResponseUtil.makeSuccess(count);
    }

    @ApiOperation("工单处理人员状态")
    @RequestMapping(path = "getStaffStatusStat", method = RequestMethod.POST)
    public Response<List<StaffStat>> getStaffStatusStat(@ApiParam("当前操作人id") long userId) {

        log.info("getStaffStatusStat userId={}", userId);

        List<StaffStat> list = staffStatusService.getStaffStatusStat(userId);

        return NewResponseUtil.makeSuccess(list);
    }

    @RequestMapping(path = "getWorkOrderById", method = RequestMethod.POST)
    public Response<WorkOrderVO> getWorkOrderById(@RequestParam("workOrderId") long workOrderId) {

        WorkOrderVO vo = baseWorkOrderService.getWorkOrderById(workOrderId);

        return NewResponseUtil.makeSuccess(vo);
    }

    @RequestMapping(path = "getOrderByTypeAndTime", method = RequestMethod.POST)
    public Response<WorkOrderVO> getOrderByTypeAndTime(int caseId, int orderType, String time) {

        WorkOrderVO vo = baseWorkOrderService.getOrderByTypeAndTime(caseId, orderType, time);

        return NewResponseUtil.makeSuccess(vo);
    }


    @RequestMapping(path = "getWorkOrderList", method = RequestMethod.POST)
    public Response<PageResult<QueryListResult>> getWorkOrderList(@RequestBody QueryListParam queryListParam) {

        OpResult<PageResult<QueryListResult>> opResult = baseWorkOrderService.getWorkOrderList(queryListParam);

        if (opResult.isSuccess()) {
            return NewResponseUtil.makeSuccess(opResult.getData());
        }

        return NewResponseUtil.makeError(opResult.getErrorCode());
    }


    @RequestMapping(path = "getWorkOrderListCount", method = RequestMethod.POST)
    public Response<Integer> getWorkOrderListCount(@RequestBody QueryListParam queryListParam) {

        int count = baseWorkOrderService.getWorkOrderListCount(queryListParam);

        return NewResponseUtil.makeSuccess(count);

    }

    @RequestMapping(path = "changeOrderLevel", method = RequestMethod.POST)
    public Response<Integer> changeOrderLevel(
            long workOrderId, int orderLevel, long operatorId,
            @RequestParam(value = "userComment", required = false, defaultValue = "") String userComment
    ) {

        OpResult opResult = baseWorkOrderService.changeOrderLevel(workOrderId, orderLevel, operatorId, userComment);

        return NewResponseUtil.makeError(opResult.getErrorCode());

    }

    @RequestMapping(path = "closeOrderBycaseIdAndType", method = RequestMethod.POST)
    public Response<Integer> closeOrderBycaseIdAndType(int caseId, int orderType, int handleResult, long operatorId, String comment) {

        int a = baseWorkOrderService.closeOrderBycaseIdAndType(caseId, orderType, handleResult, operatorId, comment);

        return NewResponseUtil.makeSuccess(a);

    }

    @ApiOperation("获取实时个人工单情况")
    @PostMapping("listStaffRealTimeWorkData")
    public Response<List<StaffRealTimeWorkData>> listStaffRealTimeWorkData(long userId) {
        return NewResponseUtil.makeSuccess(baseWorkOrderService.listStaffRealTimeWorkData(userId));
    }

    @ApiOperation("分配工单")
    @PostMapping("assign-work-order")
    public Response<Long> assignWorkOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId,
            @ApiParam("分配人id") @RequestParam(required = false, defaultValue = "0") long assignerId) {

        return assignWorkOrderService.manualAssignWorkOrder(workOrderId, operatorId, assignerId);
    }

    @ApiOperation("回收工单并分配")
    @PostMapping("callback-and-assign-work-order")
    public Response<Long> callbackAndAssignOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId,
            @ApiParam("分配人id") @RequestParam(required = false, defaultValue = "0") long assignerId) {

        return assignWorkOrderService.callbackOrderAndAssign(workOrderId, operatorId, assignerId);
    }

    @ApiOperation(value = "系统分配工单", notes = "不管被分配人是否在线，是否在员工列表中")
    @PostMapping("system-assign-work-order")
    public Response<Long> assignWorkOrderSystem(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId) {
        return assignWorkOrderService.assignWorkOrder(workOrderId, operatorId);
    }

    @ApiOperation(value = "回收释放工单", notes = "")
    @PostMapping("release-work-order")
    public Response<Long> releaseWorkOrderSystem(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("操作人id") @RequestParam long operatorId) {
        return assignWorkOrderService.releaseWorkOrder(workOrderId, operatorId, "系统释放");
    }

    @ApiOperation("工单重新审核")
    @PostMapping("reprocess-work-order")
    public Response<Long> reprocessWorkOrder(
            @ApiParam("工单id") @RequestParam long workOrderId,
            @ApiParam("领取人id") @RequestParam long operatorId,
            @ApiParam("描述") @RequestParam(required = false, defaultValue = "") String comment
    ) {
        return assignWorkOrderService.reprocessWorkOrder(workOrderId, operatorId, comment);
    }

    @ApiOperation("检查是否可以重新审核")
    @PostMapping("check-can-reprocess")
    public Response<Boolean> checkCanReprocess(@ApiParam("工单id") @RequestParam long workOrderId) {
        boolean canReprocess = baseWorkOrderService.checkCanReprocess(workOrderId);
        return NewResponseUtil.makeSuccess(canReprocess);
    }

    @RequestMapping(path = "saveTimeLine",method = RequestMethod.POST)
    public Response saveTimeLine(@RequestBody WorkOrderTimeline workOrderTimeline){

        log.info("saveTimeLine workOrderTimeline={}",workOrderTimeline);

        int a = timelineSevice.insertTimeLine(workOrderTimeline);

        return NewResponseUtil.makeSuccess(a);
    }

    @ApiOperation("获取最新的工单")
    @PostMapping("get-last-work-order")
    public Response<WorkOrderVO> getLastWorkOrder(@RequestParam int caseId, @RequestParam int orderType) {
        WorkOrderVO vo = baseWorkOrderService.getLastByCaseIdAndOrderType(caseId, orderType);
        return NewResponseUtil.makeSuccess(vo);
    }

    @ApiOperation("获取最新的工单")
    @PostMapping("get-last-work-order-by-types")
    public Response<WorkOrderVO> getLastWorkOrderByTypes(@RequestParam int caseId, @RequestParam List<Integer> orderTypes) {
        WorkOrderVO vo = baseWorkOrderService.getLastByCaseIdAndOrderTypes(caseId, orderTypes);
        return NewResponseUtil.makeSuccess(vo);
    }

    @ApiOperation("获取指定案例的工单列表")
    @PostMapping("list-work-order-by-case-id-and-types-results")
    public Response<List<WorkOrderVO>> listByCaseIdAndTypeAndResult(
            @RequestParam int caseId, @RequestParam List<Integer> orderTypes, @RequestParam List<Integer> results) {
        List<WorkOrderVO> vos = baseWorkOrderService.ListByCaseIdAndTypeAndResult(caseId, orderTypes, results);
        return NewResponseUtil.makeSuccess(vos);
    }

    @ApiOperation("根据工单id关闭工单")
    @PostMapping("handle-work-order-by-workid")
    public Response<Boolean> handleWorkOrderByWorkId(@ApiParam("工单id")@RequestParam long workId, @ApiParam("操作人id")@RequestParam long operatorId, @RequestParam int handleResult) {
        WorkOrderVO workOrderVO = baseWorkOrderService.getWorkOrderById(workId);
        if (workOrderVO != null && unSupport_force_close.contains(workOrderVO.getOrderType())) {
            return NewResponseUtil.makeError(ErrorCode.NOT_SUPPORT_ORDER_TYPE);
        }
        boolean affectRows = baseWorkOrderService.closeOrderByWorkOrderId(workId, handleResult, operatorId, "通过后门操作");
        return NewResponseUtil.makeSuccess(affectRows);
    }

    @ApiOperation("根据工单id处理工单")
    @PostMapping("handle-work-order-by-ids")
    public Response<Boolean> handleWorkOrderByIds(@ApiParam("工单id") @RequestParam List<Long> workOrderIds, @ApiParam("操作人id") @RequestParam long operatorId
            , @ApiParam("工单处理状态") @RequestParam int handleResult, @ApiParam("操作备注") @RequestParam String comment) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        boolean affectRows = baseWorkOrderService.closeOrderByWorkOrderIds(workOrderIds, handleResult, operatorId, comment);
        return NewResponseUtil.makeSuccess(affectRows);
    }

    @ApiOperation("根据工单信息查询工单id")
    @PostMapping("get-ids-by-type-result-time")
    public Response<List<Long>> getIdsByTypeResultTime(@RequestParam List<Integer> orderTypes, @RequestParam List<Integer> results
            , @RequestParam long createTime) {
        return NewResponseUtil.makeSuccess(baseWorkOrderService.getIdsByTypeResultTime(orderTypes, results, new Date(createTime)));
    }

    @ApiOperation("根据工单信息查询工单id")
    @PostMapping("get-ids-by-type-result-between-time")
    public Response<List<Long>> getIdsByTypeResultBetweenTime(@RequestParam List<Integer> orderTypes, @RequestParam List<Integer> results
            , @RequestParam long createTime, @RequestParam long endTime) {
        return NewResponseUtil.makeSuccess(baseWorkOrderService.getIdsByTypeResultBetweenTime(orderTypes, results, new Date(createTime), new Date(endTime)));
    }

    @ApiOperation("根据工单id关闭工单")
    @PostMapping("/query-work-order-by-case-and-type")
    public Response<List<WorkOrderExt>> queryWorkOrderByCaseAndType(@ApiParam("案例id")@RequestParam(name = "caseId") int caseId,
                                                             @ApiParam("参考cf-work-order:WorkOrderType")@RequestParam(name = "orderType") int orderType,
                                                             @ApiParam("extId")@RequestParam(name = "extId") String extId,
                                                             @ApiParam("参考cf-admin-api:CfSensitiveWordRecordEnum.BizType")@RequestParam(name = "contentType") String contentType){
        if(caseId <= 0 || workOrderTypeService.getFromOrderTypeCode(orderType) == null){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<WorkOrderBase> workOrderBases = baseWorkOrderService.listByCaseId(caseId, orderType);
        List<Long> workOrderIds = Lists.newArrayList();
        for (WorkOrderBase workOrderBase : workOrderBases){
            workOrderIds.add(workOrderBase.getId());
        }

        if(CollectionUtils.isEmpty(workOrderIds)){
            return NewResponseUtil.makeSuccess(null);
        }

        String extName = orderType == WorkOrderType.ugcprogress.getType() ? OrderExtName.wordId.getName() : OrderExtName.extId.getName();

        List<WorkOrderExt> workOrderExts = orderExtService.getWorkOrderExtList(workOrderIds, extName, extId);

        return NewResponseUtil.makeSuccess(workOrderExts);
    }

    @ApiOperation("根据案例查询扩展信息")
    @PostMapping("/query-ext-by-case")
    public Response<List<WorkOrderExt>> queryExtByCase(@ApiParam("案例id")@RequestParam(name = "caseId") int caseId,
                                                       @ApiParam("WorkOrderType")@RequestParam(name = "orderTypes") List<Integer> orderTypes,
                                                       @ApiParam("extName")@RequestParam(name = "extName") String extName,
                                                       @ApiParam("extValue")@RequestParam(name = "extValue") String extValue){

        if(caseId <= 0 || CollectionUtils.isEmpty(orderTypes) || StringUtils.isEmpty(extName) || StringUtils.isEmpty(extValue)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<WorkOrderVO> workOrderVOs = baseWorkOrderService.queryByCaseAndTypes(caseId, orderTypes);
        if(CollectionUtils.isEmpty(workOrderVOs)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        List<Long> workOrders = workOrderVOs.stream().map(WorkOrderVO::getWorkOrderId).collect(Collectors.toList());

        List<WorkOrderExt> workOrderExts = orderExtService.getWorkOrderExtList(workOrders, extName, extValue);

        return NewResponseUtil.makeSuccess(workOrderExts);
    }

    @ApiOperation("根据案例及工单类型查询工单")
    @PostMapping("/query-by-case-types")
    public Response<List<WorkOrderVO>> queryByCaseAndTypes(@ApiParam("案例id")@RequestParam(name = "caseId") int caseId,
                                                           @ApiParam("WorkOrderType")@RequestParam(name = "orderTypes") List<Integer> orderTypes){
        if(caseId <= 0 || CollectionUtils.isEmpty(orderTypes)){
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<WorkOrderVO> workOrderVOs = baseWorkOrderService.queryByCaseAndTypes(caseId, orderTypes);
        if(CollectionUtils.isEmpty(workOrderVOs)){
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }

        return NewResponseUtil.makeSuccess(workOrderVOs);
    }

    @ApiOperation("根据案例生成工单,必须含有case_id,orderType,operatorId,comment")
    @PostMapping("create-work-order-by-case")
    public Response<Boolean> createWorkByCase(@RequestParam int caseId, @RequestParam int orderType, @RequestParam int operatorId, String comment) {
        WorkOrderBase wordOrder = new WorkOrderBase() {
            @Override
            public List<WorkOrderExt> getWorkOrderExt() {
                return null;
            }
        };
        wordOrder.setCaseId(caseId);
        wordOrder.setComment(comment);
        wordOrder.setOrderType(orderType);
        //需要设置级别、状态未处理
        wordOrder.setOrderlevel(OrderLevel.edium.getType());
        wordOrder.setHandleResult(HandleResultEnum.undoing.getType());
        boolean workOrderEnforce = baseWorkOrderService.createWorkOrderEnforce(wordOrder, operatorId);
        return NewResponseUtil.makeSuccess(workOrderEnforce);
    }


    @ApiOperation("举报工单转移")
    @PostMapping("/report-transfer")
    public Response<String> reportTransfer(@ApiParam("工单id") @RequestParam(name = "ids") List<Long> ids,
                                           int orderType, int operatorId, int recipientId) {


//        StaffStatus staffStatus =  staffStatusService.getStaffStatus(recipientId,orderType);
//
//        if (staffStatus.getStaffStatus() != StaffStatusEnum.online.getType()){
//            return NewResponseUtil.makeError(ErrorCode.TARGET_IS_OFFLINE);
//        }

        List<WorkOrderDoingCount> list = workOrderDao.getDoingCount(Lists.newArrayList((long) recipientId), orderType, HandleResultEnum.doing.getType());
        List<StaffStatus> staffList = staffStatusService.getStaffOnlineAndStopAndOfflineToday(Lists.newArrayList((long) recipientId), orderType);
        int doingCount = CollectionUtils.isNotEmpty(list) ? list.get(0).getNum() : 0;
        int receiptThreshold = CollectionUtils.isNotEmpty(staffList) ? staffList.get(0).getReceiptThreshold() : 0;
        if(orderType == WorkOrderType.casefirstreport.getType() && doingCount >= receiptThreshold){
            return NewResponseUtil.makeError(ErrorCode.RECEIPT_THRESHOLD_ERROR);
        }

        int result = baseWorkOrderService.reportTransfer(ids, orderType, operatorId, recipientId);

        return NewResponseUtil.makeSuccess(result+"");
    }

    @ApiOperation("查询扩展信息表")
    @PostMapping("/ext/list-ext-infos")
    public Response<List<WorkOrderExt>> listExtInfos(@RequestParam("workIds") List<Long> workIds, @RequestParam("extName") String extName){
        if (CollectionUtils.isEmpty(workIds) || StringUtils.isBlank(extName)) {
            return NewResponseUtil.makeSuccess(Lists.newArrayList());
        }
        return NewResponseUtil.makeSuccess(orderExtService.getWorkOrderExts(workIds, extName));
    }


    @ApiOperation("查询工单类型的属性")
    @PostMapping("select-by-property-list")
    public Response<WorkTypeProperty.UpdatePropertyParam> selectByPropertyList(@RequestParam("firstWorkOrder") int firstWorkOrder) {
        return NewResponseUtil.makeSuccess(workPropertyService.selectByPropertyList(firstWorkOrder));
    }

    @ApiOperation("更新工单类型的属性")
    @PostMapping("update-second-work-order-type-property")
    Response<String> updateSecondWorkOrderProperty(@RequestBody WorkTypeProperty.UpdatePropertyParam param) {
        workPropertyService.updateSecondWorkOrderProperty(param);
        return NewResponseUtil.makeSuccess("");
    }

    @ApiOperation("查询工单类型的属性 只包含延后处理的限制")
    @PostMapping("select-by-type")
    Response<List<WorkTypeProperty.UpdatePropertyParam>> selectByType(@RequestParam int orderLevel,
                                                                      @RequestParam List<Integer> orderTypes,
                                                                      @RequestParam List<Integer> propertyType) {
        return NewResponseUtil.makeSuccess(workPropertyService.selectByType(orderLevel, orderTypes, propertyType));
    }

    @ApiOperation("更新工单类型的属性")
    @PostMapping("update-work-order-type-property")
    Response<String> updateWorkOrderProperty(@RequestBody WorkTypeProperty.UpdatePropertyParam param) {

        workPropertyService.updateWorkOrderProperty(param);
        return NewResponseUtil.makeSuccess("");
    }

    @ApiOperation("查看工单的扩展属性")
    @PostMapping("query-all-ext-ignore-delete")
    Response<List<WorkOrderExt>> queryAllWorkExtIgnoreDelete(@RequestParam(name = "workOrderId") long workOrderId,
                                                             @RequestParam(name = "extNames") List<String> extNames) {

        return NewResponseUtil.makeSuccess(orderExtService.getAllOrderExtIgnoreDelete(workOrderId, extNames));
    }

    @PostMapping("list-by-case-ids-and-types")
    Response<List<WorkOrderVO>> listByCaseIdsAndTypes(@RequestParam List<Integer> caseIds, @RequestParam List<Integer> orderTypes) {

        return NewResponseUtil.makeSuccess(baseWorkOrderService.listByCaseIdsAndTypes(caseIds, orderTypes));
    }

    @PostMapping(path = "delay-processing-work-order")
    Response<Boolean> delayProcessingWorkOrder(@RequestParam long userId, @RequestParam int orderType,
                                               @RequestParam String handleResult) {
        log.info("getUserCount userId={},orderType={},handleResult={}", userId, orderType, handleResult);
        if (StringUtils.isBlank(handleResult)) {
            return NewResponseUtil.makeFail(false);
        }
        int count =  baseWorkOrderService.getCountByHandleResult(userId, orderType, handleResult);
        if (count >= orderTypePropertyService.getSecondMaxDelayCount(orderType, WorkTypeProperty.PropertyType.DELAY_HANDLE_COUNT)){
            return NewResponseUtil.makeFail(false);
        }
        return NewResponseUtil.makeSuccess(true);
    }

}
