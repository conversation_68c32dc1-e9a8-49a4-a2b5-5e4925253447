package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.ICfOutcallRecordFeginClient;
import com.shuidihuzhu.client.cf.workorder.model.CfOutcallRecordParam;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.workorder.dao.IOutcallRecordDAO;
import com.shuidihuzhu.workorder.model.OutcallRecordDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/7/23 下午8:55
 * @desc
 */
@RestController
public class OutcallRecordFeignController implements ICfOutcallRecordFeginClient {

    @Autowired
    private IOutcallRecordDAO outcallRecordDAO;

    @Override
    public Response<String> saveOutcallRecord(CfOutcallRecordParam param) {
        if(Objects.isNull(param) || param.getCaseId() <= 0 || param.getWorkOrderId() <= 0 || StringUtils.isEmpty(param.getCallUnicode())){
            return ResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }

        OutcallRecordDO recordDO = new OutcallRecordDO();
        recordDO.setCaseId(param.getCaseId());
        recordDO.setWorkOrderId(param.getWorkOrderId());
        recordDO.setCallUnicode(param.getCallUnicode());

        int result = outcallRecordDAO.insert(recordDO);

        ErrorCode errorCode = result > 0 ? ErrorCode.SUCCESS : ErrorCode.SYSTEM_ERROR;

        return ResponseUtil.makeResponse(errorCode.getCode(), errorCode.getMsg(), null);
    }
}
