package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE 2019/5/17
 */
@Api("医院电话核实")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class DianhuaFeignController {


    @Resource(name = "dianhuaWorkOrderService")
    private WorkOrderFacade workOrderFacade;



    @ApiOperation(("创建医院核实工单"))
    @RequestMapping(path = "create-dianhua",method = RequestMethod.POST)
    public Response<Long> createDianhua(@RequestBody DianhuaWorkOrder workOrder){

        log.info("createDianhua workOrder={}",workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation(("处理医院核实工单"))
    @RequestMapping(path = "hanlde-dianhua",method = RequestMethod.POST)
    public Response hanldeDianhua(@RequestBody DianhuaHandleOrderParam param ){

        log.info("hanldeDianhua param={}",param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }


    @ApiOperation("查询医院核实工工单列表")
    @RequestMapping(path = "dianhua-orderlist",method = RequestMethod.POST)
    public Response<PageResult<DianhuaWorkOrderVo>> dianhuaOrderList(@RequestBody DianhuaWorkOrderListParam param){

        OpResult opResult =  workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

}
