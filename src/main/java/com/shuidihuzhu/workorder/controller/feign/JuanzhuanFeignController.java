package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.impl.JuanzhuanWorkOrderServiceImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2020/5/7
 */
@Api("1v1捐转工单")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class JuanzhuanFeignController {


    @Resource(name = "juanzhuanWorkOrderService")
    private JuanzhuanWorkOrderServiceImpl workOrderFacade;

    @ApiOperation(("创建1v1捐转工单"))
    @RequestMapping(path = "create-juanzhuan",method = RequestMethod.POST)
    public Response<Long> createJuanzhuan(@RequestBody JuanzhuanWorkOrder workOrder){

        log.info("createJuanzhuan workOrder={}",workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation("处理1v1捐转工单")
    @RequestMapping(path = "hanlde-juanzhuan",method = RequestMethod.POST)
    public Response hanldeJuanzhuan(@RequestBody JuanzhuanHandleOrderParam param ){

        log.info("hanldechuci param={}",param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(),opResult.getData());
    }

    @ApiOperation("查询1v1捐转工单")
    @RequestMapping(path = "juanzhuan-orderlist",method = RequestMethod.POST)
    public Response<PageResult<WorkOrderVO>> juanzhuanOrderList(@RequestBody WorkOrderListParam param){

        OpResult opResult =  workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("异常关闭1v1捐转工单")
    @RequestMapping(path = "close-juanzhuan",method = RequestMethod.POST)
    public Response closeJuanzhuanOrder(@RequestParam("caseId") int caseId,
                                        @RequestParam("comment") String comment){
        workOrderFacade.closeWorkOrder(caseId,comment);
        return NewResponseUtil.makeSuccess("");
    }


    @ApiOperation("获取案例d0工单的分享和捐款数据")
    @RequestMapping(path = "getD0ShareAndDonation",method = RequestMethod.POST)
    public Response<Map<Integer,WorkOrderVO>> getD0ShareAndDonation(@RequestBody List<Integer> caseIds){
        Map<Integer,WorkOrderVO> map = workOrderFacade.getD0ShareAndDonation(caseIds);
        return NewResponseUtil.makeSuccess(map);
    }


}
