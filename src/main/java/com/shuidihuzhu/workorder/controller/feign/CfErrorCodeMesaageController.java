package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.cf.client.alarm.center.errorcode.CfErrorCodeMesaageHelper;
import com.shuidihuzhu.cf.client.alarm.center.feign.CfErrorCodeMesaageFeignClient;
import com.shuidihuzhu.cf.client.alarm.center.model.ErrorCodeMesaageInfo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * 根据错误码，获取错误信息描述
 */
@Slf4j
@RestController
public class CfErrorCodeMesaageController implements CfErrorCodeMesaageFeignClient {

    static {
        for (ErrorCode errorCode : ErrorCode.values()) {
            CfErrorCodeMesaageHelper.register(errorCode.getCode(), errorCode.getMsg());
        }
    }

    @Override
    @PostMapping(path = "/innerapi/cf/error-code-mesaage-info/get")
    public Response<Set<ErrorCodeMesaageInfo>> getByCode(List<Integer> codes) {

        return NewResponseUtil.makeSuccess(CfErrorCodeMesaageHelper.getByCode(codes));
    }

}
