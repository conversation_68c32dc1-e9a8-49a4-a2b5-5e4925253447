package com.shuidihuzhu.workorder.controller.feign;

import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderFacade;
import com.shuidihuzhu.workorder.service.impl.UgcWorkOrderServiceImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.shuidihuzhu.workorder.model.enums.ErrorCode.SYSTEM_PARAM_ERROR;

/**
 * <AUTHOR>
 * @DATE 2019/10/10
 */
@Api("ugc工单")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/order")
public class UgcFeignController {

    @Resource(name = "ugcWorkOrderService")
    private UgcWorkOrderServiceImpl workOrderFacade;


    /**
     * 创建工单
     *
     * @param workOrder
     * @return
     */
    @RequestMapping(path = "create-ugc", method = RequestMethod.POST)
    public Response<Long> createUgc(@RequestBody UgcWorkOrder workOrder) {

        log.info("createUgc workOrder={}", workOrder);

        OpResult<Long> opResult = workOrderFacade.doCreate(workOrder);

        return NewResponseUtil.makeError(opResult.getErrorCode(), opResult.getData());

    }


    /**
     * 处理工单
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "handle-ugc", method = RequestMethod.POST)
    public Response handleUgc(@RequestBody UgcHandleOrderParam param) {

        log.info("handleUgc param={}", param);

        OpResult opResult = workOrderFacade.doHandle(param);

        return NewResponseUtil.makeError(opResult.getErrorCode(), opResult.getData());

    }

    /**
     * 查询工单
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "ugc-orderlist", method = RequestMethod.POST)
    public Response<PageResult<WorkOrderVO>> ugcOrderlist(@RequestBody WorkOrderListParam param) {

        log.info("ugcOrderlist param={}", param);

        if (StringUtils.isBlank(param.getHandleResult())) {
            return NewResponseUtil.makeResponse(SYSTEM_PARAM_ERROR.getCode(), "handleResult 不能为空", null);
        }

        OpResult<PageResult<WorkOrderVO>> opResult = workOrderFacade.getOrderList(param);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @RequestMapping(path = "ugc-orderlist-by-ids", method = RequestMethod.POST)
    public Response<List<WorkOrderVO>> ugcOrderlistByIds(@RequestBody List<Long> ids) {

        return NewResponseUtil.makeSuccess(workOrderFacade.getOrderListByIds(ids));
    }

    @PostMapping(path = "ugc-create-work-order-ext")
    public Response<Integer> ugcCreateWorkOrderExt(@RequestBody UgcWorkOrder workOrder){
        return NewResponseUtil.makeSuccess(workOrderFacade.ugcCreateWorkOrderExt(workOrder));
    }
}