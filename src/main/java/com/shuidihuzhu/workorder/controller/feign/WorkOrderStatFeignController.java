package com.shuidihuzhu.workorder.controller.feign;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.service.WorkOrderStatService;
import com.shuidihuzhu.workorder.service.impl.JuanzhuanWorkOrderStatServiceImpl;
import com.shuidihuzhu.workorder.util.ResultUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2019/4/1
 */
@Api("任务工单统计")
@Slf4j
@RestController
@RequestMapping(path = "innerapi/admin/stat")
public class WorkOrderStatFeignController {


    @Resource(name = "cailiaoWorkOrderStatServiceImpl")
    private WorkOrderStatService cailiaoWorkOrderStatService;


    @Resource(name = "shouciWorkOrderStatService")
    private WorkOrderStatService shouciWorkOrderStatService;


    @Resource(name = "chuciWorkOrderStatService")
    private WorkOrderStatService chuciWorkOrderStatService;

    @Resource(name = "dianhuaWorkOrderStatService")
    private WorkOrderStatService dianhuaWorkOrderStatService;


    @Resource(name = "ugcWorkOrderStatService")
    private WorkOrderStatService ugcWorkOrderStatService;


    @Resource(name = "ugcProgressWorkorderStatService")
    private WorkOrderStatService ugcProgressWorkorderStatService;


    @Resource(name = "wenjuanWorkOrderStatService")
    private WorkOrderStatService wenjuanWorkOrderStatService;

    @Resource(name = "reportWorkOrderStatService")
    private WorkOrderStatService reportWorkOrderStatService;


    @Resource(name = "fundUseWorkOrderStatService")
    private WorkOrderStatService fundUseWorkOrderStatService;

    @Resource(name = "juanzhuanWorkOrderStatService")
    private JuanzhuanWorkOrderStatServiceImpl juanzhuanWorkOrderStatService;

    @Resource(name = "subsidyWorkOrderStatService")
    private WorkOrderStatService subsidyWorkOrderStatService;

    @ApiOperation("服务费补贴工单统计")
    @RequestMapping(path = "workorder-stat-subsidy", method = RequestMethod.POST)
    public Response<List<SubsidyWorkOrderStat>> workorderStatSubsidy(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                 @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                 @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = subsidyWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("材料审核工单统计")
    @RequestMapping(path = "workorder-stat-cailiao", method = RequestMethod.POST)
    public Response<List<CaiLiaoWorkOrderStat>> workorderStatOne(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                 @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                 @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = cailiaoWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("首次沟通工单统计")
    @RequestMapping(path = "workorder-stat-shouci", method = RequestMethod.POST)
    public Response<List<ShouciWorkOrderStat>> workorderStatShouci(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                   @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                   @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = shouciWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("初审工单统计")
    @RequestMapping(path = "workorder-stat-chuci", method = RequestMethod.POST)
    public Response<List<ChuciWorkOrderStat>> workorderStatChuci(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                 @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                 @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = null;
        try {
            opResult = chuciWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);
        } catch (Exception e) {
            log.error("初审统计查询超时 {}, {}, {}", oneLevel, twoLevel, userId, e);
            return NewResponseUtil.makeResponse(-1, "查询超时", Lists.newArrayList());
        }

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("医院核实工单统计")
    @RequestMapping(path = "workorder-stat-dianhua", method = RequestMethod.POST)
    public Response<List<DianhuaWorkOrderStat>> workorderStatDianhua(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                 @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                 @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = dianhuaWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("ugc工单统计")
    @RequestMapping(path = "workorder-stat-ugc", method = RequestMethod.POST)
    public Response<List<UgcWorkOrderStat>> workorderStatUgc(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                             @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                             @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = ugcWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("ugc动态工单统计")
    @RequestMapping(path = "workorder-stat-ugcprogress", method = RequestMethod.POST)
    public Response<List<UgcProgressWorkOrderStat>> workorderStatUgcProgress(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                             @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                             @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = ugcProgressWorkorderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }



    @ApiOperation("问卷工单统计")
    @RequestMapping(path = "workorder-stat-wenjuan", method = RequestMethod.POST)
    public Response<List<WenjuanWorkOrderStat>> workorderStatWenjuan(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                     @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                     @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = wenjuanWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }


    @ApiOperation("资金用途工单统计")
    @RequestMapping(path = "workorder-stat-funduse", method = RequestMethod.POST)
    public Response<List<FundUseWorkOrderStat>> workorderStatFundUse(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                             @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                             @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = fundUseWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);
        return ResultUtils.transformOpResult2Response(opResult);
    }

    @ApiOperation("举报工单统计")
    @RequestMapping(path = "workorder-stat-report", method = RequestMethod.POST)
    public Response<List<ReportWorkOrderStat>> workorderStatReport(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                     @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                     @ApiParam(name = "用户id", required = false) long userId) {

        OpResult opResult = reportWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId);

        return ResultUtils.transformOpResult2Response(opResult);
    }



    @ApiOperation("1v1捐转工单统计")
    @RequestMapping(path = "workorder-stat-juanzhuan", method = RequestMethod.POST)
    public Response<List<JuanzhanWorkOrderStat>> workorderStatJuanzhuan(@ApiParam(name = "一级工单分类", required = true) int oneLevel,
                                                                        @ApiParam(name = "二级工单分类", required = false) String twoLevel,
                                                                        @ApiParam(name = "用户id", required = false) long userId,
                                                                        @ApiParam(name = "用户id", required = false,defaultValue = "-1") int  staffStatus) {

        OpResult opResult = juanzhuanWorkOrderStatService.getWorkOrderStatList(oneLevel, twoLevel, userId,staffStatus);
        return ResultUtils.transformOpResult2Response(opResult);
    }
}
