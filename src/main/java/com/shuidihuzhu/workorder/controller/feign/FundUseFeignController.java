package com.shuidihuzhu.workorder.controller.feign;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.CfFundUseWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.*;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.param.PageResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import com.shuidihuzhu.workorder.service.impl.FundUseWorkOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-19 14:50
 **/
@RestController
@Slf4j
public class FundUseFeignController implements CfFundUseWorkOrderClient {

    @Resource(name = "fundUseWorkOrderService")
    FundUseWorkOrderServiceImpl fundUseWorkOrderService;

    private static List<Integer> fund_use_types = Lists.newArrayList(
            WorkOrderType.funduseshenhe.getType(),
            WorkOrderType.funduserisk.getType()
    );

    @PostMapping(path = "innerapi/admin/order/create-funduse")
    @Override
    public Response<Long> createFunduse(FundUseWorkOrder workOrder) {
        log.info("createFunduse workOrder:{}", JSON.toJSONString(workOrder));
        OpResult<Long> workIdOpr = fundUseWorkOrderService.doCreate(workOrder);
        return NewResponseUtil.makeError(workIdOpr.getErrorCode(), workIdOpr.getData());
    }

    @PostMapping(path = "innerapi/admin/order/handle-funduse")
    @Override
    public Response handleFunduse(FundUseHandleOrderParam param) {
        log.info("handleFunduse workOrder:{}", JSON.toJSONString(param));
        OpResult handle = fundUseWorkOrderService.doHandle(param);
        return NewResponseUtil.makeError(handle.getErrorCode(), handle.getData());
    }

    @PostMapping(path = "innerapi/admin/order/funduse-orderlist")
    @Override
    public Response<PageResult<WorkOrderVO>> funduseOrderlist(WorkOrderListParam param) {
        log.info("funduseOrderlist param:{}", JSON.toJSONString(param));
        OpResult<PageResult<WorkOrderVO>> orderList = fundUseWorkOrderService.getOrderList(param);
        return NewResponseUtil.makeError(orderList.getErrorCode(), orderList.getData());
    }


    @PostMapping(path = "innerapi/admin/order/funduse-list-bycaseid")
    @Override
    public Response<List<WorkOrderVO>> funduseListByCaseId(int caseId, List<Integer> orderTypes) {
        log.info("fund use caseId:{}, ordertypes:{}", caseId, orderTypes);
        if (CollectionUtils.isEmpty(orderTypes)) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        for (Integer orderType : orderTypes) {
            if (!fund_use_types.contains(orderType)) {
                return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
            }
        }
        List<WorkOrderVO> workOrderVOS = fundUseWorkOrderService.funduseListByCaseId(caseId, orderTypes);
        return NewResponseUtil.makeSuccess(workOrderVOS);
    }
}
