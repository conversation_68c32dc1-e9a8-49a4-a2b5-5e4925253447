package com.shuidihuzhu.workorder.configuration.exceptionhandler;

import com.shuidihuzhu.common.web.aop.ShuidiExceptionHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MissingServletRequestParameterException;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class ShuidiExceptionHandlerConfiguration {

    @PostConstruct
    public void init() {
        ShuidiExceptionHandler.addIgnoreClass(ClientAbortException.class, ErrorCode.CLIENT_ABORT);
        ShuidiExceptionHandler.addIgnoreClass(MissingServletRequestParameterException.class, ErrorCode.ILLEGAL_ARGUMENT);
    }
}
