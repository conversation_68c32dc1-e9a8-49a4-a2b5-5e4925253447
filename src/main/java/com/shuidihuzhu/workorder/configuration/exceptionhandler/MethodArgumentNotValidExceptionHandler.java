package com.shuidihuzhu.workorder.configuration.exceptionhandler;

import com.shuidihuzhu.cf.enhancer.model.response.EhResponse;
import com.shuidihuzhu.cf.enhancer.utils.EhResponseUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.workorder.model.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class MethodArgumentNotValidExceptionHandler {

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Response<Void> handleBindException(BindException ex) {
        log.warn("参数校验异常", ex);
        BindingResult bindingResult = ex.getBindingResult();
        return promoteResponse(bindingResult);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Response<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.warn("参数校验异常", ex);
        BindingResult bindingResult = ex.getBindingResult();
        return promoteResponse(bindingResult);
    }

    private Response<Void> promoteResponse(BindingResult bindingResult) {
        StringBuilder stringBuilder = new StringBuilder();
        for (FieldError error : bindingResult.getFieldErrors()) {
            String field = error.getField();
            Object value = error.getRejectedValue();
            String msg = error.getDefaultMessage();
            String message = String.format("错误字段：%s，错误值：%s，原因：%s；", field, value, msg);
            EhResponse<Void> r = EhResponseUtils.response(ErrorCode.SYSTEM_PARAM_ERROR.getCode(), msg, null);
            return r.withDetail(message);
        }
        log.info("error msg:{}", stringBuilder);
        return EhResponseUtils.failWithMessage(stringBuilder.toString());
    }


}