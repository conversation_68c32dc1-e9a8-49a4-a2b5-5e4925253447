package com.shuidihuzhu.workorder.configuration;

import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.util.redisson.RedissonHandlerWrapper;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @DATE 2019/1/7
 */
@Configuration
public class RedissonConfiguration {


    @Bean("cf2RedissonHandler")
    @ConfigurationProperties("redisson-handler.cf-work-order-cf2-redisson-handler.cf-work-order")
    public RedissonHandler cf2RedissonHandler() {
        return new RedissonHandlerWrapper();
    }
}
