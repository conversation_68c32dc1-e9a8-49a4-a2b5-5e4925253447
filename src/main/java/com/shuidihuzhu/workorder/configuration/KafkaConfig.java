package com.shuidihuzhu.workorder.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @since 2023-08-25 3:23 PM
 **/
@Configuration
public class KafkaConfig {

    @Setter
    @Getter
    public static class KafkaRmsConfig {
        private String bootstrapServers;
        private String username;
        private String password;
    }

    @Bean
    @ConfigurationProperties(prefix = "rms.kafka.consumer.cf-work-order-data-statistics-consumer.cf-work-order")
    public KafkaRmsConfig getKafkaRmsConfig() {
        return new KafkaRmsConfig();
    }

}
