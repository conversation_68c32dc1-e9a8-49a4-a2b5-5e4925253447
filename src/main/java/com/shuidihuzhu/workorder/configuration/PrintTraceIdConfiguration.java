package com.shuidihuzhu.workorder.configuration;

import brave.Tracing;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;

@Configuration
@Profile("!production")
public class PrintTraceIdConfiguration implements InitializingBean {

    @Resource
    private Tracing tracing;

    @Override
    public void afterPropertiesSet() throws Exception {
        NewResponseUtil.setTracing(tracing);
    }
}

