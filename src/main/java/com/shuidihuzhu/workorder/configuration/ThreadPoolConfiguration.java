package com.shuidihuzhu.workorder.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.enhancer.subject.threadpool.annotation.Dynamic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2019-06-05 10:20
 */
@Slf4j
@Configuration
public class ThreadPoolConfiguration {

    private static final int WAIT_TERMINAL_SECOND = 120;
    private static final int KEEP_ALIVE_SECONDS = 60;

    /**
     * async task thread pool config
     */
    private static final int TASK_CORE_POOL_SIZE = 100;
    public static final int TASK_MAX_POOL_SIZE = 600;
    public static final int TASK_QUEUE_CAPACITY = 2000;

    @Autowired
    private Tracing tracing;

    /**
     * async task thread pool config
     */
    /*@Bean(value = "asyncTaskThreadPool",destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor asyncTaskThreadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(TASK_CORE_POOL_SIZE);
        threadPoolTaskExecutor.setMaxPoolSize(TASK_MAX_POOL_SIZE);
        threadPoolTaskExecutor.setQueueCapacity(TASK_QUEUE_CAPACITY);
        threadPoolTaskExecutor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        //允许核心线程空闲时退出
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.setThreadNamePrefix("asyncTaskExecutor-");
        //设置优雅关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(WAIT_TERMINAL_SECOND);
        return threadPoolTaskExecutor;
    }*/

    /**
     * long time task thread pool config
     */
    @Bean(value = "longTimeTaskThreadPool",destroyMethod = "shutdown")
    @Dynamic
    public ThreadPoolTaskExecutor longTimeTaskThreadPoolTaskExecutor(){
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(1);
        threadPoolTaskExecutor.setMaxPoolSize(1);
        threadPoolTaskExecutor.setQueueCapacity(1);
        threadPoolTaskExecutor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS * 10);
        //允许核心线程空闲时退出
        threadPoolTaskExecutor.setAllowCoreThreadTimeOut(true);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        threadPoolTaskExecutor.setThreadNamePrefix("longTimeTaskExecutor-");
        //设置优雅关闭
        threadPoolTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(WAIT_TERMINAL_SECOND);
        return threadPoolTaskExecutor;
    }


    /**
     * localcache thread pool config
     */
    @Bean(WorkOrderConfig.Async.LocalCache)
    @Dynamic
    public Executor asyncTaskThreadPoolTaskExecutor(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(WorkOrderConfig.Async.LocalCache + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(3, 20, 10,
                TimeUnit.MINUTES, new ArrayBlockingQueue<>(30), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("LocalCache 线程不够了,走主线程执行");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

//    @Bean(WorkOrderConfig.Async.EVENT_SEND)
//    public Executor mqExecutor(){
//        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(WorkOrderConfig.Async.EVENT_SEND + "-%d").build();
//        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(8, 16, 10,
//                TimeUnit.SECONDS, new ArrayBlockingQueue<>(1), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy(){
//            @Override
//            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
//                log.warn("mq 线程不够了,走主线程执行");
//                super.rejectedExecution(r, e);
//            }
//        });
//        poolExecutor.allowCoreThreadTimeOut(true);
//        return tracing.currentTraceContext().executorService(poolExecutor);
//    }


    @Bean(WorkOrderConfig.Async.NOTICE)
    @Dynamic
    public Executor sendNotice(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(WorkOrderConfig.Async.NOTICE + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(3, 3, 10,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(30), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy(){
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("mq 线程不够了,走主线程执行");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }


    @Bean(WorkOrderConfig.Async.AUTO_CHECK)
    @Dynamic
    public Executor autoCheck(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(WorkOrderConfig.Async.AUTO_CHECK + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(1, 3, 5,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(300), threadFactory, new ThreadPoolExecutor.DiscardPolicy(){
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("线程不足,抛弃 自动检查");
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(WorkOrderConfig.Async.CLASSIFY_ALL_QUERY)
    @Dynamic
    public Executor classifyAllQuery(){
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(WorkOrderConfig.Async.CLASSIFY_ALL_QUERY + "-%d").build();
        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(12, 24, 10,
                TimeUnit.MINUTES, new ArrayBlockingQueue<>(20), threadFactory, new ThreadPoolExecutor.CallerRunsPolicy(){
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("CLASSIFY_ALL_QUERY 线程不够了,走主线程执行");
                super.rejectedExecution(r, e);
            }
        });
        poolExecutor.allowCoreThreadTimeOut(true);
        return tracing.currentTraceContext().executorService(poolExecutor);
    }

    @Bean(WorkOrderConfig.Async.VON_GROUP_STAT)
    @Dynamic
    public ExecutorService VonGroupStatPool() {
        return tracing.currentTraceContext().executorService(new ThreadPoolExecutor(
                3, 30, 10, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1),
                new ThreadFactoryBuilder().setNameFormat("von-group-stat-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        ));
    }

}
