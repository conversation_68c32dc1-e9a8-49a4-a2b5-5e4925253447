package com.shuidihuzhu.workorder.configuration;

import com.google.common.collect.Lists;
import com.shuidihuzhu.data.consumer.ConsumerTemplate;
import com.shuidihuzhu.data.consumer.container.ConcurrentMessageContainer;
import com.shuidihuzhu.workorder.service.mq.kafka.WorkOrderForDataStatisticsConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import javax.annotation.Resource;
import java.util.List;
import java.util.Properties;

import static com.shuidihuzhu.data.constants.KafkaConstants.*;

@Slf4j
@Configuration
@Profile("!canary & !canary2")
@RefreshScope
public class KafkaConsumerConfiguration implements SmartLifecycle {

    private boolean isRunning = false;

    @Resource
    private WorkOrderForDataStatisticsConsumer searchKafkaConsumerListener;
    @Resource
    private KafkaConfig.KafkaRmsConfig kafkaRmsConfig;

    public static final List<String> SEARCH_TOPIC_LIST = Lists.newArrayList(
            "shuidi_cf_admin_work_order"
    );

    public static final String SEARCH_GROUP_ID = "cf_work_order_data_statistics_consumer";

    private ConsumerTemplate consumerTemplate = new ConsumerTemplate();

    private List<ConcurrentMessageContainer> containers = Lists.newArrayList();

    public Properties consumerConfigs(String curGroupId, String clientIdPrefix) {
        Properties properties = new Properties();
        properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaRmsConfig.getBootstrapServers());
        properties.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, "300000");
        properties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        //properties.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeout);
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        properties.put(ConsumerConfig.GROUP_ID_CONFIG, curGroupId);
        properties.put(ConsumerConfig.CLIENT_ID_CONFIG, clientIdPrefix);
        //properties.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        // 鉴权必须
        properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY_PROTOCOL_CONFIG_VALUE);
        properties.put(SaslConfigs.SASL_MECHANISM, KAFKA_SASL_MECHANISM_VALUE);
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, String.format(KAFKA_SASL_JAAS_CONFIG_VALUE, kafkaRmsConfig.getUsername(), kafkaRmsConfig.getPassword()));
        return properties;
    }

    @Override
    public void start() {
        isRunning = true;
    }

    @Override
    public void stop() {
        try {
            if (CollectionUtils.isNotEmpty(containers)) {
                containers.forEach(ConcurrentMessageContainer::stop);
            }
        } catch (Exception e) {
            log.error("stop consumer container error! ", e);
        }
    }

    @Override
    public boolean isRunning() {
        return isRunning;
    }

    @Bean
    public void searchTopicContainer() {
        consumerTemplate.setListener(searchKafkaConsumerListener);
        ConcurrentMessageContainer container = consumerTemplate.createContainer(
                SEARCH_TOPIC_LIST,
                consumerConfigs(SEARCH_GROUP_ID, "cf_work_order_data_statistics_consumer"),
                false
        );
        container.start();
        containers.add(container);
    }

}
