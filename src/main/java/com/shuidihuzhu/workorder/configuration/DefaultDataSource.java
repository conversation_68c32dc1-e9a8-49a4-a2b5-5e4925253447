package com.shuidihuzhu.workorder.configuration;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;


@Configuration
public class DefaultDataSource {


    @Bean("workOrderDataSource")
    @ConfigurationProperties("spring.datasource.druid.cf-work-order-work-order.cf-work-order")
    public DataSource setStatDataSource(){
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("workOrderDataSourceSlave")
    @ConfigurationProperties("spring.datasource.druid.cf-work-order-slave.cf-work-order")
    public DataSource dataSource() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean("shuidiCfAdminTdDataSource")
    @ConfigurationProperties("spring.datasource.druid.td-shuidi-cf-admin.cf-work-order")
    public DataSource shuidiCfTdDataSource() {
        return DruidDataSourceBuilder.create().build();
    }

}
