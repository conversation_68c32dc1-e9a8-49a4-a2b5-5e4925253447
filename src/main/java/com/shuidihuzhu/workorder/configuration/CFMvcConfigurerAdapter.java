package com.shuidihuzhu.workorder.configuration;

import com.shuidihuzhu.common.web.filter.LogRequestFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import java.util.Collections;

/**
 * <AUTHOR>
 * @DATE 2018/12/19
 */
@Slf4j
@Configuration
public class CFMvcConfigurerAdapter  {

    @Bean
    public FilterRegistrationBean logRequestFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new LogRequestFilter());
        filterRegistrationBean.setUrlPatterns(Collections.singleton("/*"));
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }

}
