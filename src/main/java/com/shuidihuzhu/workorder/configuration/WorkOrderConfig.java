package com.shuidihuzhu.workorder.configuration;

/**
 * <AUTHOR>
 * @DATE 2018/12/26
 */
public final class WorkOrderConfig {

    private WorkOrderConfig() {
    }

    public interface Async {
        String LocalCache = "LOCAL_CACHE_ASYNC_POOL";
        String EVENT_SEND = "EVENT_SEND_ASYNC_POOL";
        String NOTICE = "NOTICE";

        String CLASSIFY_ALL_QUERY = "CLASSIFY_ALL_QUERY";

        /**
         * 自动检查数据修复
         * 量少 可丢
         */
        String AUTO_CHECK = "AUTO_CHECK";

        String VON_GROUP_STAT = "VON_GROUP_STAT";
    }


    /**
     * 分配工单数量
     */
    public final static int  assignation_count = 5;


    /**
     * 向前翻页
     */
    public final static String pre_paging = "pre";

    /**
     * 向后翻页
     */
    public final static String next_paging = "next";


    /**
     * 志愿者渠道
     */
    public final static String cf_volunteer = "cf_volunteer";



    /**
     * 延后状态 限制数量
     */
    public final static int  yanhou_count = 10;


    /**
     * 材料工单稍后处理 限制数量
     */
    public final static int  cailiao_later_count = 5;


    /**
     * 审核工单 延后状态 限制数量
     */
    public final static int  shenhe_yanhou_count = 5;


    /**
     * 分配工单数量为1
     */
    public final static int  assignation_one_count = 1;


    /**
     * 分配工单数量为10
     */
    public final static int  assignation_ten_count = 10;

    /**
     * 分配工单数量为100
     */
    public final static int assignation_one_hundred_count = 100;

    /**
     * 线上工单默认限制值
     */
    public final static int  PR_ONLINE_LIMIT = 10;

    /**
     * 补采审核工单分配工单数量为30
     */
    public final static int PR_SUPPLEMENT_AUDIT_COUNT = 30;

    /**
     * 复筛工单分配工单数量为30
     */
    public final static int PR_SECOND_AUDIT_COUNT = 30;

    /**
     * 复筛工单分配工单数量为30
     */
    public final static int FUND_USE_SHEN_HE = 10;

    public interface Ds {
        String MAIN = "workOrderDataSource";
        String SLAVE = "workOrderDataSourceSlave";
    }
}
