package com.shuidihuzhu.workorder.configuration;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLockAop;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;


@Slf4j
@Component
public class RobotKeyConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        HashMap<String, String> map = Maps.newHashMap();
        map.put("477eea0e-ee45-40be-aaf6-6e46a05cbbad", "2bb72a47-9c69-4307-8343-7c923451135e");
        AlarmBotService.setFeishu2WeworkKeyMap(map);
    }

}