package com.shuidihuzhu.workorder.util;

import com.shuidihuzhu.common.web.enums.MyErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.workorder.model.OpResult;

/**
 * <AUTHOR>
 * @date 2018-05-23  18:19
 */
public class ResultUtils {

    public static <T> Response<T> transformOpResult2Response(OpResult<T> opResult) {

        return NewResponseUtil.makeResponse(
                opResult.getErrorCode().getCode(),
                opResult.getErrorCode().getMsg(),
                opResult.getData()
        );
    }


    public static <T> Response<T> transform(Response<?> result, T data) {
        return NewResponseUtil.makeResponse(
                result.getCode(),
                result.getMsg(),
                data
        );
    }

    public static <T> Response<T> success() {
        return success(null);
    }

    public static <T> Response<T> success(T data) {
        return NewResponseUtil.makeSuccess(data);
    }

    public static <T> Response<T> fail(String msg) {
        return NewResponseUtil.makeFail(msg);
    }

    public static <T> Response<T> fail(MyErrorCode errorCode) {
        return NewResponseUtil.makeError(errorCode);
    }

    public static <T> Response<T> relayError(Response<?> resp) {
        return NewResponseUtil.makeResponse(resp.getCode(), resp.getMsg(), null);
    }
}
