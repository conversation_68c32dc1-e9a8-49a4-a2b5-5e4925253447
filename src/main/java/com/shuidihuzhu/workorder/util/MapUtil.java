package com.shuidihuzhu.workorder.util;

import org.apache.commons.collections4.MapUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @DATE 2019/5/21
 */
public final class MapUtil {

    public static int getFromMap(Map<Integer,Integer> map, int key){

        if (MapUtils.isEmpty(map) || map.get(key) == null){
            return 0;
        }
        return map.get(key);
    }

    public static  int getFromMap(Map<Long,Integer> map,long key){

        if (MapUtils.isEmpty(map) || map.get(key) == null){
            return 0;
        }
        return map.get(key);
    }
}
