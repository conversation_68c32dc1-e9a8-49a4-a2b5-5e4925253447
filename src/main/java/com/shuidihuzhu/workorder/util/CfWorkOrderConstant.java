package com.shuidihuzhu.workorder.util;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-12-15 17:40
 **/
public class CfWorkOrderConstant {

    public static List<Integer> checkUnCreateChushen = Lists.newArrayList(
            WorkOrderType.shenhe.getType(),
            WorkOrderType.dianhuashenhe.getType(),
            WorkOrderType.target_amount_reasonable_audit.getType(),
            WorkOrderType.highriskshenhe.getType(),
            WorkOrderType.ai_erci.getType(),
            WorkOrderType.ai_photo.getType(),
            WorkOrderType.ai_content.getType());
}
