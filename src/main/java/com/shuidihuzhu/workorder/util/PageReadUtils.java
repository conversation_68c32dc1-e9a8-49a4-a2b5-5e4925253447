package com.shuidihuzhu.workorder.util;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PageReadUtils {

    public static <ResItem, Item> List<ResItem> readAll(PageIterator<Item> iterator, IteratorProcessor<Item, ResItem> processor) {
        ArrayList<ResItem> res = Lists.newArrayList();
        while (iterator.hasNext()) {
            List<Item> nextList = iterator.next();
            if (nextList != null) {
                for (Item i : nextList) {
                    ResItem resItem = processor.process(i);
                    if (resItem != null) {
                        res.add(resItem);
                    }
                }
            }
        }
        return res;
    }

    public static <ResItem, Item, Resp> List<ResItem> readAll(PageAdapter<Resp, Item> adapter, IteratorProcessor<Item, ResItem> processor) {
        return readAll(new PaginationPageIterator<>(adapter), processor);
    }


    public static <Resp, Item> PageIterator<Item> paginationPageIterator(PageAdapter<Resp, Item> adapter) {
        return new PaginationPageIterator<>(adapter);
    }

    public static class PaginationPageIterator<Item, Resp> implements PageIterator<Item> {
        int current = 0;
        int pageSize = 10;
        private Resp resp;
        PageAdapter<Resp, Item> adapter;

        public PaginationPageIterator(PageAdapter<Resp, Item> adapter) {
            this.adapter = adapter;
        }

        @Override
        public List<Item> next() {
            resp = adapter.load(++current, pageSize);
            return adapter.getListByResp(resp);
        }

        @Override
        public boolean hasNext() {
            if (resp == null) {
                return true;
            }
            return adapter.hasNextByResp(resp);
        }
    }

    public interface IteratorProcessor<E, R> {
        R process(E e);
    }


    public interface PageIterator<E> {
        List<E> next();

        boolean hasNext();
    }

    public interface PageAdapter<Resp, Item> {
        Resp load(int current, int pageSize);

        boolean hasNextByResp(Resp resp);

        List<Item> getListByResp(Resp resp);
    }
}
