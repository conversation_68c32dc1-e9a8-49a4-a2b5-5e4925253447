package com.shuidihuzhu.workorder.elastic.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.cf.search.param.CfWorkOrderV2IndexSearchParam;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderTableParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/6/7
 */
@Service
public class SearchDelegate {

    @Resource
    private CfSearchClient cfSearchClient;

    public long searchWorkOrderCount(List<Long> userIds, int orderType, int handleResult) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0;
        }

        WorkOrderTableParam woTableParam = new WorkOrderTableParam();
        woTableParam.setOperatorIds(userIds);
        woTableParam.setOrderTypes(Lists.newArrayList(orderType));
        woTableParam.setHandleResult(Lists.newArrayList(handleResult));

        return searchWorkOrderCount(woTableParam);
    }

    public long searchWorkOrderCount(WorkOrderTableParam woTableParam) {
        if (Objects.isNull(woTableParam)) {
            return 0;
        }

        CfWorkOrderV2IndexSearchParam searchParam = new CfWorkOrderV2IndexSearchParam();
        searchParam.setWoTableParam(woTableParam);
        SearchRpcResult<Long> searchRpcResult = cfSearchClient.cfWorkOrderV2IndexSearchCount(searchParam);
        if (searchRpcResult == null || searchRpcResult.getCode() != 0) {
            return 0;
        }
        return searchRpcResult.getData();
    }
}
