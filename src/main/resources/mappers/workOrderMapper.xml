<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderDao">


    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,operator_org_id,assign_group_id,handle_time,create_time,update_time,finish_time
    </sql>



    <insert id="createWorkOrder" parameterType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase" useGeneratedKeys="true" keyProperty="id">

        insert into work_order (case_id,order_type,order_level,handle_result,operator_id,operator_org_id,assign_group_id)
        values(
          #{caseId},
          #{orderType},
          #{orderlevel},
          #{handleResult},
          #{operatorId},
          #{operatorOrgId},
            #{assignGroupId}
        )
    </insert>

    <update id="assignWorkOrder">

        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId},handle_result=#{handleResult},handle_time=now()
        where id=#{id} and operator_id = 0

    </update>


    <update id="assignWorkOrders">

        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId},handle_result=#{handleResult},handle_time=now()
        where id in
          <foreach collection="ids" item="item" open="(" separator="," close=")">
              #{item}
          </foreach>
         and operator_id = 0

    </update>


    <select id="getWorkorderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and handle_result in

        <foreach collection="handleResult" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        AND is_delete=0

        <if test="caseId > 0 ">
            and case_id = #{caseId}
        </if>

        <if test="orderLevel > 0">
            and order_level = #{orderLevel}
        </if>

        <if test="startTime != null and startTime!='' and endTime != null and endTime!='' ">
            <![CDATA[ and finish_time >= #{startTime} and finish_time < #{endTime} ]]>
        </if>

        <if test="workOrderId > 0 and paging == 'next'">
            <![CDATA[  and id < #{workOrderId} ]]>
            order by id desc
        </if>

        <if test="workOrderId > 0 and paging == 'pre' ">
            and id > #{workOrderId}
            order by id
        </if>

        <if test="workOrderId == 0 ">
            order by id desc
        </if>

        limit #{pageSize}
    </select>


    <select id="getWorkOrderListByCaseIds" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and handle_result in

        <foreach collection="handleResult" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        AND is_delete=0

        <if test="caseIds != null and caseIds.size() > 0">
            and case_id in
            <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
        </if>

        <if test="orderLevel > 0">
            and order_level = #{orderLevel}
        </if>

        <if test="startTime != null and startTime!='' and endTime != null and endTime!='' ">
            <![CDATA[ and finish_time >= #{startTime} and finish_time < #{endTime} ]]>
        </if>

        <if test="workOrderId > 0 and paging == 'next'">
            <![CDATA[  and id < #{workOrderId} ]]>
            order by id desc
        </if>

        <if test="workOrderId > 0 and paging == 'pre' ">
            and id > #{workOrderId}
            order by id
        </if>

        <if test="workOrderId == 0 ">
            order by id desc
        </if>

        limit #{pageSize}
    </select>

    <select id="getDoingCount" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount">

        select operator_id operatorId , count(1) num from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and handle_result=#{handleResult} AND is_delete=0

        group by operator_id
    </select>

    <select id="getDoingCountByOrderTypes" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount">

        select operator_id operatorId , count(1) num from work_order
        force index (idx_operator_id_order_type_handle_result)
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
            #{orderType}
        </foreach>
        and handle_result=#{handleResult} AND is_delete=0
        group by operator_id
    </select>


    <select id="queryAssignedCount" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderAssignedCount">
        select operator_id operatorId , count(1) num from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and handle_time >= #{handleTime} AND is_delete=0
        group by operator_id
    </select>

    <select id="getCountByHandleResult" resultType="java.lang.Integer">

        select count(*) from work_order
        where operator_id = #{operatorId}
        and order_type=#{orderType} and handle_result in

        <foreach collection="handleResult" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        AND is_delete=0

        <if test="handleFinish == 1">
            and <![CDATA[ `finish_time`>#{dayOfZero} ]]>
        </if>

    </select>


    <select id="getAllCountByHandleResult" resultType="java.lang.Integer">

        select count(1) from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} AND is_delete=0

    </select>

    <select id="getAllCountByHandleResultAndGroupId" resultType="java.lang.Integer">

        select count(1) from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} AND assign_group_id = #{assignGroupId} AND is_delete=0

    </select>

    <select id="getWorkorderListByUser" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/> from work_order
        where operator_id=#{operatorId}
        and order_type=#{orderType}
        and handle_result in
        <foreach collection="handleResult" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_delete=0

    </select>


    <update id="freeWorkOrder">

        update work_order set operator_id=0,operator_org_id=0,handle_result=#{newResult}
        where id in
        <foreach collection="wrokOrderIds" item="wrokOrderId" open="(" separator="," close=")">
            #{wrokOrderId}
        </foreach>
        and handle_result=#{oldResult}

    </update>

    <update id="freeWorkOrderWithNoCheckOldStatus">

        update work_order set operator_id=0,operator_org_id=0,handle_result=#{newResult}
        where id in
          <foreach collection="wrokOrderIds" item="wrokOrderId" open="(" separator="," close=")">
              #{wrokOrderId}
          </foreach>

    </update>



    <update id="handle">
        update work_order set handle_result=#{handleResult} , finish_time=now()
        where id = #{wrokOrderId} and operator_id>0 and handle_result in (1,3,35)
    </update>

    <update id="handleIsCheckStatus">
        update work_order set handle_result=#{handleResult} , finish_time=now()
        <where>
            id = #{wrokOrderId}
            <if test="checkOperator">
                and operator_id = #{operatorId}
            </if>
        </where>
    </update>

    <update id="reportHandle">
        update work_order set handle_result=#{handleResult}, finish_time=now()
        where id = #{wrokOrderId} and operator_id>0
    </update>

    <update id="handleBatch">
        update work_order set handle_result=#{handleResult}, finish_time=now()
        where id in
        <foreach collection="wrokOrderIds" item="wrokOrderId" open="(" separator="," close=")">
            #{wrokOrderId}
        </foreach>
        and handle_result in (0,1,3)
    </update>

    <update id="updateHandleResultByIds">
        update work_order set handle_result=#{handleResult}, finish_time=now()
        <where>
            id in
            <foreach collection="workOrderIds" item="workOrderId" open="(" separator="," close=")">
                #{workOrderId}
            </foreach>
            <if test="preHandleResultList != null and preHandleResultList.size() > 0">
                and handle_result in
                <foreach collection="preHandleResultList" item="preHandleResult" open="(" separator="," close=")">
                    #{preHandleResult}
                </foreach>
            </if>
        </where>
    </update>


    <select id="getWorkOrderById" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select  <include refid="select_fields"/>
        from work_order
        where id=#{wrokOrderId} AND is_delete=0

    </select>

    <select id="getWorkOrderListByIds" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select  <include refid="select_fields"/>
        from work_order
        where id in
        <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_delete=0
    </select>

    <select id="getOneUrgentWorkOrder" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select  <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and order_level=#{orderLevel} and operator_id=0 AND is_delete=0

        order by create_time limit 1

    </select>


    <select id="queryUndoOrderOrderByLevelAndTime" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        <if test="createTime != null and createTime != ''">
            and create_time > #{createTime}
        </if>
        order by order_level desc, create_time asc
        <if test="limit != null">
            limit #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <select id="recentAssignedOnExt" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select w.id,w.case_id,w.order_type,w.order_level,w.handle_result,w.operator_id,w.operator_org_id,w.handle_time,w.create_time,w.update_time,w.finish_time
        from work_order w join work_order_ext e on w.id = e.work_order_id
        where w.case_id = #{caseId}
          and w.order_type=#{orderType}
          and w.operator_id > 0
          AND w.is_delete=0
          and e.is_delete = 0
          and e.ext_name = #{extName,jdbcType=VARCHAR}
            <if test="extValues == null or extValues.size() == 0">
                and e.ext_value > ''
            </if>
            <if test="extValues != null and extValues.size() != 0">
                and e.ext_value in
                <foreach collection="extValues" open="(" item="extValue" separator="," close=")">
                    #{extValue,jdbcType=VARCHAR}
                </foreach>
            </if>
        order by w.id desc
        limit 1
    </select>
    
    <select id="queryUndoOrderExistsExtOrderByLevelAndTime"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
	    select w.id,w.case_id,w.order_type,w.order_level,w.handle_result,w.operator_id,w.operator_org_id,w.handle_time,w.create_time,w.update_time,w.finish_time
        from work_order w join work_order_ext e on w.id = e.work_order_id
        where w.order_type=#{orderType} and w.handle_result=0 and w.operator_id=0 AND w.is_delete=0
        and e.is_delete = 0
        and e.ext_name = #{extName,jdbcType=VARCHAR}
        <if test="extValue == null or extValue == ''">
            and e.ext_value > ''
        </if>
        <if test="extValue != null and extValue != ''">
            and e.ext_value = #{extValue,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            and w.create_time > #{createTime}
        </if>
        order by w.order_level desc, w.create_time asc
        limit #{limit}
    </select>

    <select id="queryUndoOrderOrderByLevelAndTimeFromTidb" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from shuidi_cf_admin.work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        <if test="createTime != null and createTime != ''">
            and create_time > #{createTime}
        </if>
        order by order_level desc, create_time asc
    </select>


    <select id="queryUndoOrderOrderByLevel" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        order by order_level desc, create_time asc
    </select>


    <select id="getWorkOrderBycaseIdAndTypes" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select  <include refid="select_fields"/>
        from work_order
        where case_id=#{caseId}

        and order_type in
        <foreach collection="types" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        <if test="results.size > 0">
            and handle_result in
            <foreach collection="results" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        AND is_delete=0
        order by id desc
        limit 1;
    </select>


    <update id="updateOrdertype">
        update work_order set order_type=#{ordertype}
        where id = #{id}
        and handle_result in
        <foreach collection="results" item="item" open="(" separator="," close=")">
        #{item}
        </foreach>
    </update>

    <update id="updateOperator">
        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId}
        where id = #{workOrderId} and is_delete = 0
    </update>

    <select id="getOrderCount" resultType="java.lang.Integer">
        select  count(1)
        from work_order
        where order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and handle_result in
        <foreach collection="results" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <![CDATA[ and create_time < #{time}  ]]>
        AND is_delete=0
    </select>

    <select id="getCaseOrderCountByType" resultType="java.lang.Integer">
        select  count(1)
        from work_order
        where order_type = #{orderType}
        and case_id = #{caseId}
        AND is_delete=0
    </select>

    <update id="closeWorkOrderByCaseId">
        update work_order set handle_result=#{handleResult},finish_time=now()
        where case_id = #{caseId} and operator_id = 0
        and order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="closeOrderById">

        update work_order set handle_result=#{handleResult}, finish_time=now()
        where  id  in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>


    <select id="getOrderByCaseId" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/> from work_order
        where case_id = #{caseId}
        and handle_result in
        <foreach collection="handleResults" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete=0
        and order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getOrderByTypeAndTime" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where order_type = #{orderType} and case_id = #{caseId}
        and create_time > #{time}
        limit 1;
    </select>

    <select id="listOrderByTypeAndTime" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type = #{orderType}
        and case_id = #{caseId}
        and create_time >= #{startTime,jdbcType=TIMESTAMP}
        limit 100
    </select>

    <select id="getAllWorkorderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select w.*
        from work_order w ${forceIndex}

        <if test="belonger > 0 ">
            left join work_order o on w.case_id = o.case_id
        </if>

        where w.is_delete=0

        <if test="belonger > 0 ">
            and o.order_type in (29,46) and o.operator_id = #{belonger}
        </if>

        <if test="operatorId > 0 ">
            and w.operator_id = #{operatorId}
        </if>

        <if test="outerUser > 0 ">
            and (w.operator_id = 0 or w.operator_id > 100000000 )
        </if>

        <if test="orderTypes.size>0">
            and w.order_type in
            <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="handleResult >= 0 ">
            and w.handle_result = #{handleResult}
        </if>

        <if test="workOrderId > 0 ">
            and w.id = #{workOrderId}
        </if>

        <if test="caseIds.size > 0 ">
            and w.case_id in
            <foreach collection="caseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="startCreateTime != null and startCreateTime!='' and endCreateTime != null and endCreateTime!='' ">
            <![CDATA[ and w.create_time >= #{startCreateTime} and w.create_time < #{endCreateTime} ]]>
        </if>

        <if test="startHandleTime != null and startHandleTime!='' and endHandleTime != null and endHandleTime!='' ">
            <![CDATA[ and w.handle_time >= #{startHandleTime} and w.handle_time < #{endHandleTime} ]]>
        </if>

        <if test="startDoneTime != null and startDoneTime!='' and endDoneTime != null and endDoneTime!='' ">
            <![CDATA[ and w.finish_time >= #{startDoneTime} and w.finish_time < #{endDoneTime} ]]>
        </if>

        <if test="pageWorkOrderId > 0 and paging == 'next'">
            <![CDATA[  and w.id < #{pageWorkOrderId} ]]>
            order by w.id desc
        </if>

        <if test="pageWorkOrderId > 0 and paging == 'pre' ">
            and w.id > #{pageWorkOrderId}
            order by w.id
        </if>

        <if test="pageWorkOrderId == 0">
            order by w.id desc
        </if>

        limit #{pageSize}
    </select>


    <update id="changeOrderLevel">
        update work_order set order_level=#{orderLevel}
        where id = #{workOrderId}
    </update>


    <select id="getOrderBycaseIdAndType" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where order_type = #{orderType} and case_id = #{caseId}
        order by create_time desc
        limit 1;

    </select>

    <select id="listByCaseId" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            `case_id` = #{caseId}
            and order_type = #{orderType}
            and `is_delete` = 0
        </where>
    </select>

    <select id="getLastByCaseIdAndOrderType"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            `case_id` = #{caseId}
            and order_type = #{orderType}
            and `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getLastByCaseIdAndOrderTypes"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            `case_id` = #{caseId}
            and order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            and `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="queryByCaseAndTypes" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            `case_id` = #{caseId}
            and order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            and `is_delete` = 0
        </where>
        order by id desc
    </select>

    <select id="listByCaseIdAndTypeAndResult"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            `case_id` = #{caseId}
            and order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            <if test="results != null and results.size() > 0">
                and `handle_result` in
                <foreach collection="results" item="result" open="(" separator="," close=")">
                    #{result}
                </foreach>
            </if>
            and `is_delete` = 0
        </where>
    </select>

    <select id="listCaseIdsByTypeAndCount"
            resultType="com.shuidihuzhu.workorder.model.WorkOrderMini">
        select case_id, count(*) as order_count
        from work_order
        where order_type = #{orderType}
        and case_id in
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        and `is_delete` = 0
        group by case_id, order_type
    </select>


    <select id="listById" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and `is_delete` = 0
        </where>
    </select>

    <select id="countForAnalysis" resultType="java.lang.Integer">
        select count(*)
        from work_order
        <where>
            order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            and `handle_result` in
            <foreach collection="results" item="result" open="(" separator="," close=")">
                #{result}
            </foreach>
            <![CDATA[and create_time > #{startTime} and create_time < #{endTime} ]]>
            and `is_delete` = 0
        </where>
    </select>


    <select id="analysisWholeDayData" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            <![CDATA[create_time > #{startTime} and create_time < #{endTime}]]>
            and `is_delete` = 0
        </where>
    </select>


    <select id="getChainIndexData" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        <where>
            <![CDATA[create_time > #{startTime} and create_time < #{endTime}]]>
            <![CDATA[and handle_time > #{handleStart} and handle_time < #{handleEnd}]]>
            and `is_delete` = 0
        </where>
    </select>



    <update id="reportTransfer">
        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId}
        where id in
        <foreach collection="ids" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>
        and order_type = #{orderType}
    </update>


    <select id="getWorkordesByUser" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select  <include refid="select_fields"/>
        from work_order
        where order_type = #{orderType}
        and operator_id in
        <foreach collection="operatorIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and handle_result in
        <foreach collection="handleResult" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <![CDATA[ and handle_time < #{endTime}  ]]>
        and handle_time >= #{startTime}
        AND is_delete=0
    </select>

    <select id="listWorkFor1v1" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select  <include refid="select_fields"/>
        from work_order
        where order_type = #{orderType}
        and operator_id = #{operatorId}
        <![CDATA[ and create_time < #{endTime}  ]]>
        and create_time >= #{startTime}
        AND is_delete=0
    </select>

    <select id="listByCaseIdsAndTypes" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select  <include refid="select_fields"/>
        from work_order
        where `case_id` in
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
            #{orderType}
        </foreach>
        and `is_delete` = 0
    </select>

    <select id="queryToDay"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select   <include refid="select_fields"/>
        from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and handle_result in
        <foreach collection="handleResults" open="(" close=")" item="handleResult" separator=",">
            #{handleResult}
        </foreach>
        <if test="handleTime != null">
            and handle_time >= #{handleTime}
        </if>
        <if test="finishTime != null">
            and finish_time >= #{finishTime}
        </if>
        AND is_delete=0
    </select>

    <select id="queryCountByHandleResult"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount">
        select operator_id operatorId , count(1) num from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and handle_result=#{handleResult} AND is_delete=0
        group by operator_id
    </select>

    <select id="queryCountGroupByOrderType"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderStatisticalCount">
        select order_type as orderType, count(1) num from work_order
        where operator_id = #{operatorId}
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and handle_result=#{handleResult} AND is_delete=0
        group by order_type
    </select>

    <select id="queryListByBatch"  resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where
        <if test="id > 0">
             id > #{id} and
        </if>
         handle_result = #{handleResult}
        and order_type = #{orderType}
         <![CDATA[ and create_time < #{endTime}]]>
        ORDER BY `id`
        limit #{limit}
    </select>

    <update id="deleteWorkOrderByIdAndOrderType">
        update work_order
        set is_delete = 1
        where id = #{id}
    </update>

    <update id="updateHandleResultByIdAndOrderType">
        update work_order
        set handle_result = #{handleResult}
        where id = #{id} and order_type = #{orderType}
    </update>

    <select id="getDoingOrders" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and handle_result=#{handleResult} AND is_delete=0
    </select>

    <select id="findByFinishTime" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        SELECT *
        FROM work_order
        FORCE INDEX(idx_finish_time)
        WHERE is_delete = 0
        AND  order_type = #{orderType}
        <![CDATA[ and finish_time >= #{finishStartTime} and finish_time < #{finishEndTime} ]]>
        AND id > #{currentWorkOrderId}
        ORDER BY id
        LIMIT #{pageSize}
    </select>

    <update id="assignPrWorkOrder">
        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId},handle_result=#{handleResult},handle_time=now()
        where id=#{id}
    </update>

    <update id="transferWorkOrders">
        update work_order
        <set>
            operator_id=#{operatorId}, operator_org_id=#{operatorOrgId},handle_time=now(),
            <if test="handleResult != null" >
                handle_result=#{handleResult},
            </if>
        </set>
        where id in
        <foreach collection="ids" open="(" item="id" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="listRawByIds" resultType="com.shuidihuzhu.client.cf.workorder.model.RawWorkOrder">
        select *
        from work_order
        where id in
        <foreach collection="collection" open="(" item="id" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

    <update id="updateWorkOrderOperatorId">
        update work_order set operator_id=#{operatorId},operator_org_id=#{operatorOrgId}
        where id in
        <foreach collection="workOrderIds" open="(" item="id" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateFinishTime">
        update work_order
        set finish_time = now()
        where id = #{workOrderId};
    </update>

    <select id="getLastOperatorIdByCaseIdAndType" resultType="java.lang.Long">
        select operator_id
        from work_order
        <where>
            case_id = #{caseId}
            and order_type in
            <foreach collection="types" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and operator_id > 0
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getCountByOperatorIdAndHandleResult" resultType="int">
        select count(*)
        from work_order
        <where>
            operator_id = #{operatorId}
            and order_type = #{orderType}
            and handle_result = #{handleResult}
            and is_delete = 0
        </where>
    </select>

    <select id="recentAssignedOrder"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where case_id = #{caseId}
        and order_type = #{workOrderType}
        and operator_id > 0
        and is_delete = 0
        order by id desc
        limit 1
    </select>

    <update id="callbackOrder">
        update work_order set operator_id=0,operator_org_id=0,handle_result=#{targetStatus}
        <where>
            id in
            <foreach collection="workOrderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
            and handle_result in
            <foreach collection="sourceStatusList" item="sourceResult" open="(" separator="," close=")">
                #{sourceResult}
            </foreach>
        </where>
    </update>

    <select id="getTypeWithCountByUserAndHandleResult"
            resultType="com.shuidihuzhu.workorder.model.order.VonDoingCountTypeVO">
        select order_type , count(1) num from work_order
        <where>
            operator_id = #{operatorId}
            and order_type in
            <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
                #{orderType}
            </foreach>
            and handle_result = #{handleResult}
        </where>
        group by order_type
    </select>

    <select id="getUndoOrderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        order by order_level desc, create_time asc
        limit #{limit}
    </select>

    <select id="getUndoGroupOrderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        and assign_group_id = #{groupId}
        order by order_level desc, create_time asc
        limit #{limit}
    </select>

    <select id="getUndoGroupOrderListByTime" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select <include refid="select_fields"/>
        from work_order
        where order_type=#{orderType} and handle_result=#{handleResult} and operator_id=0 AND is_delete=0
        and assign_group_id = #{groupId}
        and create_time > #{dateTime}
        order by order_level desc, create_time asc
        limit #{limit}
    </select>

    <select id="getIdsByTypeResultTime" resultType="java.lang.Long">
        select id
        from work_order
        <where>
            order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            and `handle_result` in
            <foreach collection="results" item="result" open="(" separator="," close=")">
                #{result}
            </foreach>
            and  <![CDATA[ create_time <= #{createTime}  ]]>
            and `is_delete` = 0
        </where>
    </select>

    <select id="getIdsByTypeResultBetweenTime" resultType="java.lang.Long">
        select id
        from work_order
        <where>
            order_type in
            <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
                #{orderType}
            </foreach>
            and `handle_result` in
            <foreach collection="results" item="result" open="(" separator="," close=")">
                #{result}
            </foreach>
            and  <![CDATA[ create_time <= #{endTime} and create_time >= #{createTime} ]]>
            and `is_delete` = 0
        </where>
    </select>

    <select id="getCountByUserIdAndHandleResult"
            resultType="com.shuidihuzhu.workorder.model.order.OrderUserHandleResultCount">
        select operator_id, handle_result, count(*) as 'count' from work_order
        <where>
            operator_id = #{userId}
            and handle_result in
            <foreach collection="handleResultList" open="(" close=")" item="handleResult" separator=",">
                #{handleResult}
            </foreach>
            and order_type = #{orderType}
            and is_delete = 0
        </where>
        group by handle_result
    </select>
</mapper>

