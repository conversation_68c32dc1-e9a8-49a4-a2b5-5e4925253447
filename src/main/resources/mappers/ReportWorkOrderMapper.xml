<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.ReportWorkOrderDAO">
    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,handle_time,create_time,update_time,finish_time
    </sql>

    <select id="queryWorkOrderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select <include refid="select_fields"/>
        from work_order
        where operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType}
        and handle_result in
        <foreach collection="handleResult" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>
        <if test="caseId > 0">
            and case_id = #{caseId}
        </if>
        AND is_delete=0
        <if test="startTime != null and startTime!='' and endTime != null and endTime!='' ">
            <![CDATA[ and finish_time >= #{startTime} and finish_time < #{endTime} ]]>
        </if>
        <if test="workOrderId > 0 and paging == 'next'">
            and id <![CDATA[ < ]]> #{workOrderId}
            order by id desc
        </if>

        <if test="workOrderId > 0 and paging == 'pre' ">
            <![CDATA[  and id > #{workOrderId} ]]>
            order by id
        </if>

        <if test="workOrderId == 0 ">
            order by id desc
        </if>
        limit #{pageSize}
    </select>
</mapper>

