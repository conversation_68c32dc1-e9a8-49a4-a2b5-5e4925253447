<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.VonConfigDAO">

    <sql id="tableName">von_config</sql>

    <sql id="select_fields">
        id,
        order_type,
        content,
        create_time,
        update_time
    </sql>

    <select id="get" resultType="java.lang.String">
        select config_value from <include refid="tableName"/>
        <where>
            namespace = #{namespace}
            and config_key = #{key}
        </where>
    </select>

    <insert id="save">
        insert into <include refid="tableName"/>
        (namespace, config_key, config_value)
        values
        (
        #{namespace},
        #{key},
        #{value}
        )
        ON DUPLICATE KEY UPDATE
        config_value = #{value}
    </insert>

    <select id="getAllByNamespace" resultType="com.shuidihuzhu.workorder.core.service.von.core.config.VonConfigDO">
        select * from <include refid="tableName"/>
        <where>
            namespace = #{namespace}
        </where>
    </select>
</mapper>

