<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.VonAssignGroupDAO">

    <sql id="tableName">von_assign_group</sql>

    <sql id="select_fields">
        id,
        order_type,
        permission,
        create_time,
        update_time
    </sql>

    <select id="getGroupIdByOrderTypeAndPermission" resultType="java.lang.Long">
        select id
        from <include refid="tableName"/>
        <where>
            order_type = #{orderType}
            and permission = #{permission}
            and is_delete = 0
        </where>
        limit 1
    </select>

    <select id="getPermissionByGroupId" resultType="java.lang.String">
        select permission
        from <include refid="tableName"/>
        <where>
            id = #{id}
        </where>
    </select>

    <select id="getById" resultType="com.shuidihuzhu.workorder.core.model.von.AssignGroupDO">
        select *
        from <include refid="tableName"/>
        <where>
            id = #{id}
        </where>

    </select>

    <select id="getListByOrderType" resultType="com.shuidihuzhu.workorder.core.model.von.AssignGroupDO">
        select *
        from <include refid="tableName"/>
        <where>
            order_type = #{orderType}
            and is_delete = 0
        </where>
    </select>
</mapper>

