<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.VonOrderConfigDAO">

    <sql id="tableName">von_order_config</sql>

    <sql id="select_fields">
        id,
        order_type,
        content,
        create_time,
        update_time
    </sql>

    <select id="getByOrderType" resultType="java.lang.String">
        select content
        from
        <include refid="tableName">
        </include>
        <where>
            order_type = #{orderType}
        </where>
    </select>

    <insert id="saveConfig">
        insert into
        <include refid="tableName">
        </include>
        (order_type, content)
        values
        (
        #{orderType},
        #{content}
        )
        on duplicate key update
        content = #{content}
    </insert>
</mapper>

