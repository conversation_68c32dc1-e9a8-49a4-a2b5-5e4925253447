<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.VonPermissionDisableDAO">

    <sql id="tableName">von_permission_disable</sql>

    <sql id="select_fields">
        id,
        operator_id,
        permission,
        create_time,
        update_time
    </sql>

    <select id="getDisablePermissionByOperatorId" resultType="java.lang.String">
        select distinct permission
        from <include refid="tableName"/>
        <where>
            operator_id = #{operatorId}
            and is_delete = 0
        </where>
    </select>

    <update id="removeDisable">
        update <include refid="tableName"/>
        <set>
            is_delete = 1
        </set>
        <where>
            operator_id = #{operatorId}
            and permission = #{permission}
            and is_delete = 0
        </where>
    </update>

    <insert id="disable">
        insert into <include refid="tableName"/>
        (operator_id, permission)
        values
        (
        #{operatorId},
        #{permission}
        )
    </insert>

    <select id="isDisable" resultType="boolean">
        select count(*)
        from <include refid="tableName"/>
        <where>
            operator_id = #{operatorId}
            and permission = #{permission}
            and is_delete = 0
        </where>
    </select>

    <select id="getDisableList" resultType="java.lang.Long">
        select operator_id
        from <include refid="tableName"/>
        <where>
            operator_id in
            <foreach collection="userIds" open="(" close=")" separator="," item="userId">
                #{userId}
            </foreach>
            and permission = #{permission}
            and is_delete = 0
        </where>
    </select>
</mapper>

