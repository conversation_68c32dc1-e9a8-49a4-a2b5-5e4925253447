<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderDataStatisticsDao">

    <sql id = "table_name">
        work_order_data_statistics
    </sql>

    <insert id="updateAssignCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, assign_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            assign_count = assign_count + 1
    </insert>

    <insert id="updateManualLockCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, manual_lock_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            manual_lock_count = manual_lock_count + 1
    </insert>

    <insert id="updateExceptionDoneCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, exception_done_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            exception_done_count = exception_done_count + 1
    </insert>

    <insert id="updateStopCaseCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, stop_case_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            stop_case_count = stop_case_count + 1
    </insert>

    <insert id="updateAuditPassCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, audit_pass_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            audit_pass_count = audit_pass_count + 1
    </insert>

    <insert id="updateAuditRejectCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, audit_reject_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            audit_reject_count = audit_reject_count + 1
    </insert>

    <insert id="updateWithinThreeMinutesCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, within_three_minutes_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            within_three_minutes_count = within_three_minutes_count + 1
    </insert>

    <insert id="updateOutsideThreeMinutesCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, outside_three_minutes_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            outside_three_minutes_count = outside_three_minutes_count + 1
    </insert>

    <insert id="updateWithinTenMinutesCount">
        insert into <include refid="table_name"/>
        (stat_day, stat_hour, operator_id, order_type, within_ten_minutes_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        within_ten_minutes_count = within_ten_minutes_count + 1
    </insert>

    <insert id="updateOutsideTenMinutesCount">
        insert into <include refid="table_name"/>
        (stat_day, stat_hour, operator_id, order_type, outside_ten_minutes_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        outside_ten_minutes_count = outside_ten_minutes_count + 1
    </insert>

    <insert id="updateCallCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, call_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            call_count = call_count + 1
    </insert>

    <insert id="updateDoneCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, done_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            done_count = done_count + 1
    </insert>

    <insert id="updateHandleManualLockCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, done_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            handle_manual_lock_count = handle_manual_lock_count + 1
    </insert>

    <insert id="updateEffectiveCallCount">
        insert into <include refid="table_name"/>
            (stat_day, stat_hour, operator_id, order_type, done_count)
        values
            (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
            effective_call_count = effective_call_count + 1
    </insert>

    <select id="getByUserIdAndOrderType" resultType="com.shuidihuzhu.workorder.model.WorkOrderDataStatistics">
        select * from <include refid="table_name"/>
        where stat_day = #{statDay} and order_type = #{orderType} and operator_id = #{userId}
    </select>

    <select id="getByUserIdsAndOrderType" resultType="com.shuidihuzhu.workorder.model.WorkOrderDataStatistics">
        select * from <include refid="table_name"/>
        where stat_day = #{statDay} and order_type = #{orderType} and operator_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="getSumByUserIdsAndOrderType" resultType="com.shuidihuzhu.workorder.model.WorkOrderDataStatistics">
        select
            coalesce(sum(call_count),0) as callCount,
            coalesce(sum(done_count),0) as doneCount,
            coalesce(sum(assign_count),0) as assignCount,
            coalesce(sum(manual_lock_count),0) as manualLockCount,
            coalesce(sum(exception_done_count),0) as exceptionDoneCount,
            coalesce(sum(audit_reject_count),0) as auditRejectCount,
            coalesce(sum(audit_pass_count),0) as auditPassCount,
            coalesce(sum(stop_case_count),0) as stopCaseCount,
            coalesce(sum(within_three_minutes_count),0) as withinThreeMinutesCount,
            coalesce(sum(outside_three_minutes_count),0) as outsideThreeMinutesCount,
            coalesce(sum(within_ten_minutes_count),0) as withinTenMinutesCount,
            coalesce(sum(outside_ten_minutes_count),0) as outsideTenMinutesCount,
            coalesce(sum(handle_manual_lock_count),0) as handleManualLockCount,
            coalesce(sum(effective_call_count),0) as effectiveCallCount
        from <include refid="table_name"/>
        where stat_day = #{statDay} and order_type = #{orderType} and operator_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
</mapper>

