<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.ShouciWorkOrderDao">



    <select id="getWorkorderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select
            w.id, case_id,order_type,order_level,handle_result,operator_id,handle_time,w.create_time,w.update_time,w.finish_time
            <if test="callStatus != null and callStatus != '' ">
            ,e.ext_value callStatus
            </if>

        from work_order w

        <if test="callStatus != null and callStatus != '' ">
          left join work_order_ext e on w.id=e.work_order_id
        </if>

        where operator_id = #{operatorId} and w.is_delete = 0
        and order_type=#{orderType} and handle_result in

        <foreach collection="handleResults" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        <if test="caseId > 0 ">
            and case_id = #{caseId}
        </if>

        <if test="startTime != null and startTime != '' and endTime != null and endTime != '' ">
            <![CDATA[ and w.finish_time >= #{startTime} and w.finish_time < #{endTime} ]]>
        </if>

        <if test="callStatus != null and callStatus != '' ">
            and e.ext_name="CallStatus" and e.ext_value = #{callStatus} and e.is_delete = 0
        </if>

        order by w.order_level desc,w.id desc

        limit #{limit}, #{pageSize}
    </select>


</mapper>

