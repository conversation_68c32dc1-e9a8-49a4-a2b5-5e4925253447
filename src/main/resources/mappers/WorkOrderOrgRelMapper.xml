<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderOrgRelDao">

    <sql id="tableName">
        work_order_org_rel
    </sql>

    <insert id="addList">
        insert into <include refid="tableName"/>
        (work_order_id,order_type,org_id)
        values
        <foreach collection="workOrderOrgRels" item="workOrderOrgRel" separator=",">
            (#{workOrderOrgRel.workOrderId},#{workOrderOrgRel.orderType},#{workOrderOrgRel.orgId})
        </foreach>
    </insert>

    <select id="getByWorkOrderId" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderOrgRel">
        select *
        from <include refid="tableName"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
    </select>


</mapper>