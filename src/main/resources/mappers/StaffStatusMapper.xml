<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.StaffStatusDao">


    <insert id="changeStatus" parameterType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        insert into staff_status (user_id,order_type,staff_status,oper_type,operation_id,receipt_threshold,auto_allocation)
        values(
          #{userId},
          #{orderType},
          #{staffStatus},
          #{operType},
          #{operationId},
          #{receiptThreshold},
          #{autoAllocation}
        )
         ON DUPLICATE KEY UPDATE
         staff_status = #{staffStatus},
         oper_type=#{operType},
         operation_id=#{operationId},
         receipt_threshold=#{receiptThreshold},
         auto_allocation=#{autoAllocation},
         update_time=now()
    </insert>


    <select id="getStaffStatus" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,receipt_threshold,auto_allocation, allow_auto_online, assign_time,create_time,update_time,allow_assign_ordertype from staff_status
        where user_id=#{userId}  and order_type=#{orderType} and is_delete=0

    </select>



    <insert id="saveStaffStatusRecord" parameterType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        insert into staff_status_record (user_id,order_type,staff_status,oper_type,operation_id, `on_job`, `allow_auto_online`)
        values(
          #{userId},
          #{orderType},
          #{staffStatus},
          #{operType},
          #{operationId},
          #{onJob},
          #{allowAutoOnline}
        )

    </insert>


    <select id="countOfflineTodayRecord" resultType="java.lang.Integer">
        select count(0) from staff_status_record where `user_id` = #{userId}
        and `order_type` = #{orderType}
        and `staff_status` = #{status}
        and oper_type = #{operatorType} and
        <![CDATA[ update_time > #{beginTime}]]>
    </select>


    <select id="getStaffsByStatus" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,receipt_threshold,auto_allocation, allow_auto_online, assign_time,allow_assign_ordertype,operation_id from staff_status
        where  user_id in
        <foreach collection="userIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType} and staff_status=#{staffStatus}
    </select>

    <select id="getStaffsByStatusList" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,receipt_threshold,auto_allocation, allow_auto_online, assign_time,allow_assign_ordertype,operation_id from staff_status
        where  user_id in
        <foreach collection="userIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType}
        and staff_status in
        <foreach collection="staffStatusList" item="staffStatus" open="(" separator="," close=")">
            #{staffStatus}
        </foreach>
    </select>


    <select id="getStaffs" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,receipt_threshold,auto_allocation, allow_auto_online, assign_time,allow_assign_ordertype from staff_status
        where  user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="addStaffAssignTime">

        update staff_status set assign_time=now()
        where user_id = #{userId} and order_type=#{orderType}

    </update>



    <select id="getStaffOrderByAssignTime" resultType="java.lang.Long">

        select user_id from staff_status
        where  user_id in
        <foreach collection="userIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and order_type=#{orderType}
        order by assign_time limit 1

    </select>




    <select id="getOnJobStaffStatusStat" resultType="com.shuidihuzhu.workorder.model.StaffStatusNum">

        select order_type orderType,staff_status staffStatus , count(staff_status) num from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and staff_status in
        <foreach collection="userStatus" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and on_job = 0
        group by staff_status,order_type

    </select>



    <select id="getStaffOnlineStatusStat" resultType="com.shuidihuzhu.workorder.model.StaffStatusNum">

        select order_type orderType,staff_status staffStatus , count(staff_status) num from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and staff_status = 1 and update_time > #{today}
        group by staff_status,order_type

    </select>


    <select id="getDistinctUserOnlineCount" resultType="java.lang.Integer">
        select count(distinct user_id) from staff_status
        where order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and staff_status = 1 and on_job = 0
</select>

    <select id="getOnJobStaffStatusList" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,update_time,receipt_threshold,auto_allocation, allow_auto_online, assign_time,allow_assign_ordertype
        from staff_status
        where order_type in
        <foreach collection="orderTypes" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

        <if test="operatorId > 0">
            and user_id=#{operatorId}
        </if>

        AND is_delete=0
        AND on_job = 0

        <if test="userId > 0 and paging == 'next'">
            and user_id > #{userId}
            order by user_id
        </if>

        <if test="userId > 0 and paging == 'pre' ">
            <![CDATA[  and user_id < #{userId} ]]>
            order by user_id desc
        </if>

        <if test="userId == 0">
            order by user_id
        </if>

        limit #{pageSize}
    </select>


    <select id="getUserIdByTypesAndStatus" resultType="java.lang.Long">

        select user_id from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        <if test="staffStatus > 0">
            and staff_status = #{staffStatus}
        </if>
    </select>



    <select id="getStaffStatusByTypes" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,update_time, allow_auto_online, assign_time ,receipt_threshold from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and user_id=#{userId}
    </select>


    <select id="getUserIdByTypes" resultType="java.lang.Long">

        select user_id from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
    </select>


    <select id="getAllOnJobByTypes" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,update_time, allow_auto_online, assign_time,allow_assign_ordertype
        from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and on_job = 0
    </select>

    <update id="autoOff">
        update staff_status set staff_status = 3 ,oper_type=9
        where  user_id = #{userId} and  order_type = #{orderType}
    </update>


    <select id="getStaffsByStatusAndTime" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">

        select user_id,order_type,staff_status,oper_type,update_time, allow_auto_online, assign_time
        from staff_status
        where  staff_status in
        <foreach collection="status" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        <![CDATA[  and assign_time < #{time} ]]>
    </select>

    <select id="queryStaffsByStatusAndType" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">
        select user_id,order_type,staff_status,oper_type,update_time, allow_auto_online, assign_time
        from staff_status
        where  staff_status in
        <foreach collection="status" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
            #{orderType}
        </foreach>
    </select>


    <update id="updateAutoOnlineStatus">
        update staff_status set allow_auto_online = #{autoStatus}
        where user_id = #{userId}
        and  order_type = #{orderType}
    </update>


    <update id="updateLeaveOfficeStatus">
        update staff_status set on_job = #{onJob}
        where  user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="selectOnJobUsers" resultType="java.lang.Integer">
        select distinct user_id
        from staff_status
        where on_job = 0
    </select>
    
    <update id="clearAutoOfflineStatus">
        update staff_status set allow_auto_online = 0
        where allow_auto_online = 1 AND on_job = 0
    </update>


    <select id="selectOrderTypeByUser" resultType="java.lang.Integer">

        select order_type
        from staff_status
        where  user_id=#{userId}
    </select>


    <select id="getStaffStatusRecords" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatusRecord">
        select * from shuidi_cf_admin.staff_status_record
        where user_id=#{userId} and order_type=#{orderType} and create_time > #{createTime} order by create_time asc
    </select>

    <select id="getLastOneLtCreateTime" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatusRecord">
       select * from shuidi_cf_admin.staff_status_record
        force index(idx_user_id_order_type)
        where user_id=#{userId} and order_type=#{orderType} and create_time &lt; #{createTime} order by create_time desc limit 1
    </select>

    <update id="allowAssign">
        update staff_status set allow_assign_ordertype = #{allowAssign}
        where  user_id=#{userId}
    </update>

    <update id="updateThresholdByType">
        update staff_status set receipt_threshold = #{threshold}
        where  order_type=#{orderType}
    </update>

    <select id="getThresholdByType" resultType="com.shuidihuzhu.client.cf.workorder.model.StaffStatus">
       select * from staff_status 
        where  order_type=#{orderType}
        <if test="userId>0">
            and user_id=#{userId}
        </if>
        order by update_time desc limit 1
    </select>
    
    <update id="updateJobAndStatusBatch">
        update staff_status
        set staff_status = #{staffStatus},
            on_job = #{onJob}
        where user_id in
        <foreach collection="userIds" open="(" item="userId" separator="," close=")">
            #{userId,jdbcType=BIGINT}
        </foreach>
        and order_type in
        <foreach collection="orderTypes" open="(" separator="," item="orderType" close=")">
            #{orderType,jdbcType=INTEGER}
        </foreach>
        and is_delete = 0
    </update>

    <select id="getAllUserIdOnJobByTypes" resultType="java.lang.Long">
        select distinct user_id
        from staff_status
        where  order_type in
        <foreach collection="orderType" item="ot" open="(" separator="," close=")">
            #{ot}
        </foreach>
        and on_job = 0
    </select>
</mapper>

