<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.DianhuaWorkOrderDao">

    <select id="getWorkorderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select
            id, case_id,order_type,order_level,handle_result,operator_id,handle_time,create_time,update_time,finish_time
        from work_order

        where operator_id = #{operatorId} and is_delete = 0
        and order_type=#{orderType} and handle_result in

        <foreach collection="handleResults" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        <if test="caseId > 0 ">
            and case_id = #{caseId}
        </if>

        <if test="orderLevel > 0">
            and order_level = #{orderLevel}
        </if>

        <if test="startTime != null and startTime != '' and endTime != null and endTime != '' ">
            <![CDATA[ and finish_time >= #{startTime} and finish_time < #{endTime} ]]>
        </if>

        order by order_level desc,id desc

        limit #{limit}, #{pageSize}
    </select>


</mapper>

