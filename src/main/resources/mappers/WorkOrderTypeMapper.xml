<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderTypeDAO">

    <sql id = "table_name">
        work_order_type
    </sql>
    <sql id="select_fields">
        id, order_type_code, msg, permission, relate_id
    </sql>
    <insert id="insertWorkOrder">
        insert into <include refid="table_name"/>
        (order_type_code, msg, permission, relate_id)
        values(
        #{orderTypeCode},
        #{msg},
        #{permission},
        #{relateId}
        )
    </insert>
    <select id="getByOrderTypeCode" resultType="com.shuidihuzhu.workorder.model.WorkOrderTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `order_type_code` = #{orderType}
        and
        `is_delete` = 0
    </select>
    <select id="getAllOrderType" resultType="com.shuidihuzhu.workorder.model.WorkOrderTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `is_delete` = 0
    </select>
    <select id="getAllOrderTypeByOneTypeCode" resultType="com.shuidihuzhu.workorder.model.WorkOrderTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `relate_id` = #{relateId}
        and
        `is_delete` = 0
    </select>
    <select id="getByPermission" resultType="com.shuidihuzhu.workorder.model.WorkOrderTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `permission` = #{permission}
        and
        `is_delete` = 0
    </select>


</mapper>