<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.write.WorkOrderWriteDAO">

    <sql id="tableName">work_order</sql>

    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,operator_org_id,handle_time,create_time,update_time,finish_time
    </sql>

    <update id="updateHandleResultByIds">
        update
        <include refid="tableName">
        </include>
        <set>
            handle_result = #{handleResult}
        </set>
        <where>
            id in
            <foreach collection="orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="updateAssignGroupIdById">
        update <include refid="tableName"/>
        <set>
            assign_group_id = #{assignGroupId}
        </set>
        <where>
            id = #{workOrderId}
            and order_type = #{orderType}
        </where>
    </update>
</mapper>

