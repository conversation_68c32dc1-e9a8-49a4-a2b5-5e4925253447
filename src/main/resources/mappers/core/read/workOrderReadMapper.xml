<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.core.dao.WorkOrderReadDAO">

    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,operator_org_id,handle_time,create_time,update_time,finish_time
    </sql>

    <select id="getListByIds" resultType="com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder">
        select  <include refid="select_fields"/>
        from work_order
        where id in
        <foreach collection="workOrderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_delete=0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder">
        select  <include refid="select_fields"/>
        from work_order
        where id = #{workOrderId}
        AND is_delete=0
    </select>
</mapper>

