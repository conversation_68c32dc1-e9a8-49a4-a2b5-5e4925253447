<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.workorder.core.dao.StorageDAO">
    <sql id="tableName">work_order_storage</sql>

    <sql id="insertFields">
        work_order_id,
        storage_type,
        storage_value
    </sql>

    <sql id="selectFields">
        id,
        create_time,
        update_time,
        <include refid="insertFields"/>
    </sql>

    <sql id="commonWhere">
        and is_delete = 0
    </sql>

    <select id="getLastByType" resultType="com.shuidihuzhu.workorder.core.model.domain.StorageDO">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        <where>
            work_order_id = #{workOrderId}
            and storage_type = #{type}
            <include refid="commonWhere"/>
        </where>
        order by id desc
        limit 1
    </select>

    <select id="getListByType" resultType="com.shuidihuzhu.workorder.core.model.domain.StorageDO">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        <where>
            work_order_id = #{workOrderId}
            and storage_type = #{type}
            <include refid="commonWhere"/>
        </where>
    </select>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        values
        (
        #{workOrderId},
        #{storageType},
        #{storageValue}
        )
    </insert>

    <insert id="addBatch">
        insert into
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        values
        <foreach collection="list" item="i" separator=",">
            (
            #{i.workOrderId},
            #{i.type},
            #{i.value}
            )
        </foreach>
    </insert>

    <update id="updateByTypeValue">
        update <include refid="tableName"/>
        set
        storage_value = #{value}
        <where>
            work_order_id = #{workOrderId}
            and storage_type = #{type}
            <include refid="commonWhere"/>
        </where>
    </update>

    <select id="getAllByOrderIds" resultType="com.shuidihuzhu.workorder.core.model.domain.StorageDO">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        <where>
            work_order_id in
            <foreach collection="workOrderIds" item="orderId" open="(" separator="," close=")">
                #{orderId}
            </foreach>
            <include refid="commonWhere"/>
        </where>
    </select>
</mapper>

