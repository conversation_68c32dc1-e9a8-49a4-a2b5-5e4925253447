<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderStatDao">



    <select id="getRemainingAmount" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where  <![CDATA[ create_time < #{time}  ]]> and is_delete=0
        and handle_result in
            <foreach collection="results" item="result" open="(" separator="," close=")">
                #{result}
            </foreach>
        and order_type in
            <foreach collection="types" item="t" open="(" separator="," close=")">
                #{t}
            </foreach>

    </select>




    <select id="getNewAmount" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where create_time >= #{time} and is_delete=0
        and order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

    </select>




    <select id="getUndoingAmount" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where handle_result = 0 and is_delete=0
        and order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

    </select>



    <select id="getResultStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select order_type twoLevel ,operator_id userId ,count(1) queryNum
        from work_order
        where  handle_result = #{handleResult} and is_delete=0
        <if test="time != null and time != '' ">
           and update_time >= #{time}
        </if>
        and order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

        <if test="users.size > 0">
            and operator_id in
            <foreach collection="users" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>

        group by order_type

        <if test="users.size > 0"> ,operator_id </if>

    </select>

    <select id="getLaterStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select order_type twoLevel ,operator_id userId ,count(1) queryNum
        from work_order
        where  handle_result = 3 and is_delete=0
        <![CDATA[ and finish_time >= #{start} and finish_time<#{end}  ]]>
        and order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

        <if test="users.size > 0">
            and operator_id in
            <foreach collection="users" item="user" open="(" separator="," close=")">
                #{user}
            </foreach>
        </if>

        group by order_type

        <if test="users.size > 0"> ,operator_id </if>

    </select>



    <select id="getCailiaoStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.order_type twoLevel ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where  w.finish_time >= #{time} and w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and e.material_audit_status =#{auditStatus}
        group by w.order_type

    </select>



    <select id="getCailiaoUserStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.operator_id userId ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where w.finish_time >= #{time}  and  w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and w.operator_id in
        <foreach collection="users" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and e.material_audit_status =#{auditStatus}
        group by w.operator_id

    </select>



    <select id="getCailiaoTypeStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.order_type twoLevel ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where w.finish_time >= #{time}  and  w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and w.operator_id = #{user} and e.material_audit_status =#{auditStatus}
        group by w.order_type

    </select>


    <select id="getExtStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.order_type twoLevel ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where  w.finish_time >= #{time} and w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and w.is_delete=0
        and e.ext_name =#{extName} and e.ext_value=#{extValue} and e.is_delete=0
        group by w.order_type

    </select>


    <select id="getExtUserStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.operator_id userId ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where w.finish_time >= #{time}  and  w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and w.operator_id in
        <foreach collection="users" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and w.is_delete=0
        and e.ext_name =#{extName} and e.ext_value=#{extValue} and e.is_delete=0
        group by w.operator_id

    </select>


    <select id="getExtTypeStat" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderStat">

        select w.order_type twoLevel ,count(1) queryNum
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where w.finish_time >= #{time}  and  w.order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        and w.operator_id =#{user}
        and w.is_delete=0
        and e.ext_name =#{extName} and e.ext_value=#{extValue} and e.is_delete=0
        group by w.order_type

    </select>


    <select id="getTypeAndResult" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where  order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and handle_time > #{startTime}
        and  <![CDATA[ handle_time <= #{endTime}  ]]>


        <if test="statType == 2">
            and  operator_id > 0 and <![CDATA[ operator_id < #{userIdLimit} ]]>
        </if>

        <if test="statType == 3">
            and  operator_id > #{userIdLimit}
        </if>

        <if test="userIds.size > 0">
            and  operator_id in
            <foreach collection="userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="handleResults.size > 0">
          and  handle_result in
            <foreach collection="handleResults" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and is_delete=0
    </select>


    <select id="getTypeAndAssign" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where
        order_type in

        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

        and handle_time > #{startTime}
        and  <![CDATA[ handle_time <= #{endTime}  ]]>

        <if test="statType == 1">
            and  operator_id > 0
        </if>
        <if test="statType == 2">
            and  operator_id > 0 and <![CDATA[ operator_id < #{userIdLimit} ]]>
        </if>

        <if test="statType == 3">
            and  operator_id > #{userIdLimit}
        </if>

        <if test="userIds.size > 0">
            and  operator_id in
            <foreach collection="userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        and is_delete=0
    </select>


    <select id="getExtTypeStatNum" resultType="java.lang.Integer">

        select count(distinct w.id)
        from  work_order w left join work_order_ext e on w.id=e.work_order_id
        where
         w.handle_time > #{startTime}
        and  <![CDATA[ w.handle_time <= #{endTime}  ]]>
        and  w.order_type in
        <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and w.is_delete=0

        <if test="statType == 2">
            and  w.operator_id > 0 and <![CDATA[ w.operator_id < #{userIdLimit} ]]>
        </if>

        <if test="statType == 3">
            and  w.operator_id > #{userIdLimit}
        </if>

        <if test="userIds.size > 0">
            and  w.operator_id in
            <foreach collection="userIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and e.ext_name =#{extName}
        and e.ext_value in
        <foreach collection="extValues" item="item" open="(" separator="," close=")">
            #{item}
         </foreach>
        and e.is_delete=0

    </select>


    <select id="getOrderData" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">
        select *
        from work_order
        <where>
            order_type in
            <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="statType == 1">
                and  operator_id > 0
            </if>
            <if test="statType == 2">
                and  operator_id > 0 and <![CDATA[ operator_id < #{userIdLimit} ]]>
            </if>

            <if test="statType == 3">
                and  operator_id > #{userIdLimit}
            </if>

            <if test="userIds.size > 0">
                and  operator_id in
                <foreach collection="userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            and <![CDATA[handle_time > #{startTime} and handle_time <= #{endTime}]]>
            and `is_delete` = 0
        </where>
    </select>

</mapper>

