<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkTypePropertyDao">

    <sql id = "table_name">
        work_order_type_property
    </sql>


    <insert id="addPropertyList" parameterType = "com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty">
        insert into <include refid="table_name"/>
        (`order_level`, `order_type`, `property_type`, `property_value`)
        values
        <foreach collection="propertyList" item="item" separator=",">
            (#{item.orderLevel}, #{item.orderType},
            #{item.propertyType}, #{item.propertyValue})
        </foreach>
    </insert>


    <select id = "selectByPropertyTypes" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkTypeProperty">
        select *
        from <include refid="table_name"/>
        where
        `order_level` = #{orderLevel}
        and
        `order_type` IN
         <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
             #{orderType}
         </foreach>
        and
        `property_type` IN
        <foreach collection="propertyTypes" item="propertyType" open="(" separator="," close=")">
            #{propertyType}
        </foreach>
        AND is_delete = 0
    </select>


    <update id = "deleteWorkOrderPropertys" >
        update
        <include refid="table_name"/>
        set is_delete = 1
        where
        `order_level` = #{orderLevel}
        and
        `order_type` IN
        <foreach collection="orderTypes" item="orderType" open="(" separator="," close=")">
            #{orderType}
        </foreach>
        and
        `property_type` IN
        <foreach collection="propertyTypes" item="propertyType" open="(" separator="," close=")">
            #{propertyType}
        </foreach>
        AND is_delete = 0
    </update>

</mapper>