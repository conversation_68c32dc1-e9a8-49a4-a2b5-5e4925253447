<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.OrderStatDao">


    <insert id="save" parameterType="com.shuidihuzhu.client.cf.workorder.model.OrderStat">

        insert into order_stat (
        user_id,
        user_name,
        d01_num,
        d01_assign_num,
        d01_call_rate,
        d01_call_succ_rate,
        d02_num,
        d02_assign_num,
        d02_call_rate,
        d02_call_succ_rate,
        share_rate,
        verify_rate,
        donation_rate,
        batch_hour,
        batch_tag,
        stat_type,
        d01_call_num,
        d02_call_num
        )
        values(
          #{userId},
          #{userName},
          #{d01Num},
          #{d01AssignNum},
          #{d01CallRate},
          #{d01CallSuccRate},
          #{d02Num},
          #{d02AssignNum},
          #{d02CallRate},
          #{d02CallSuccRate},
          #{shareRate},
          #{verifyRate},
          #{donationRate},
          #{batchHour},
          #{batchTag},
          #{statType},
          #{d01CallNum},
          #{d02CallNum}
        )
         ON DUPLICATE KEY UPDATE
        d01_num = #{d01Num},
        d01_assign_num = #{d01AssignNum},
        d01_call_rate =#{d01CallRate},
        d01_call_succ_rate = #{d01CallSuccRate},
        d02_num = #{d02Num},
        d02_assign_num = #{d02AssignNum},
        d02_call_rate = #{d02CallRate},
        d02_call_succ_rate = #{d02CallSuccRate},
        share_rate = #{shareRate},
        verify_rate = #{verifyRate},
        donation_rate = #{donationRate},
        d01_call_num = #{d01CallNum},
        d02_call_num = #{d02CallNum}
    </insert>

    <insert id="saveBatch" parameterType="com.shuidihuzhu.client.cf.workorder.model.OrderStat">

        insert into order_stat (
        user_id,
        user_name,
        d01_num,
        d01_assign_num,
        d01_call_rate,
        d01_call_succ_rate,
        d02_num,
        d02_assign_num,
        d02_call_rate,
        d02_call_succ_rate,
        share_rate,
        verify_rate,
        donation_rate,
        batch_hour,
        batch_tag,
        stat_type,
        d01_call_num,
        d02_call_num
        )
        values
        <foreach collection="orderStatList" item="item" separator=",">
            (
            #{item.userId},
            #{item.userName},
            #{item.d01Num},
            #{item.d01AssignNum},
            #{item.d01CallRate},
            #{item.d01CallSuccRate},
            #{item.d02Num},
            #{item.d02AssignNum},
            #{item.d02CallRate},
            #{item.d02CallSuccRate},
            #{item.shareRate},
            #{item.verifyRate},
            #{item.donationRate},
            #{item.batchHour},
            #{item.batchTag},
            #{item.statType},
            #{item.d01CallNum},
            #{item.d02CallNum}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        d01_num = VALUES(d01_num),
        d01_assign_num = VALUES(d01_assign_num),
        d01_call_rate =VALUES(d01_call_rate),
        d01_call_succ_rate = VALUES(d01_call_succ_rate),
        d02_num = VALUES(d02_num),
        d02_assign_num = VALUES(d02_assign_num),
        d02_call_rate = VALUES(d02_call_rate),
        d02_call_succ_rate = VALUES(d02_call_succ_rate),
        share_rate = VALUES(share_rate),
        verify_rate = VALUES(verify_rate),
        donation_rate = VALUES(donation_rate),
        d01_call_num = VALUES(d01_call_num),
        d02_call_num = VALUES(d02_call_num)
    </insert>


    <select id="getByBatchHourAndBatchTag" resultType="com.shuidihuzhu.client.cf.workorder.model.OrderStat">
        select * from order_stat
        where batch_hour=#{batchHour} and batch_tag=#{batchTag} and is_delete=0
    </select>

    <select id="getStatList" resultType="com.shuidihuzhu.client.cf.workorder.model.OrderStat">
        select * from order_stat
        where batch_hour in
        <foreach collection="batchHours" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>
        and stat_type=#{statType} and is_delete=0
    </select>

</mapper>

