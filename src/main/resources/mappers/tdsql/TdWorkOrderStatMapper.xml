<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.tdsql.TdWorkOrderStatDao">

    <select id="getResultAmount" resultType="java.lang.Integer">

        select count(1)
        from work_order
        where finish_time >= #{time} and
        handle_result in
        <foreach collection="results" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete=0
        and order_type in
        <foreach collection="types" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>

        <if test="userId > 0">
            and operator_id = #{userId}
        </if>

    </select>

</mapper>

