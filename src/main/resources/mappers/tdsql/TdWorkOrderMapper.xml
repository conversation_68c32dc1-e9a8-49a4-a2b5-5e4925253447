<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.tdsql.TdWorkOrderDao">


    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,operator_org_id,assign_group_id,handle_time,create_time,update_time,finish_time
    </sql>

    <select id="getAllWorkorderCount" resultType="java.lang.Integer">

        select count(w.id)
        from work_order w
        <if test="belonger > 0 ">
            left join work_order o on w.case_id = o.case_id
        </if>

        where w.is_delete=0

        <if test="belonger > 0 ">
            and o.order_type in (29,46) and o.operator_id = #{belonger}
        </if>

        <if test="operatorId > 0 ">
            and w.operator_id = #{operatorId}
        </if>

        <if test="outerUser > 0 ">
            and (w.operator_id = 0 or w.operator_id > 100000000 )
        </if>

        <if test="orderTypes.size>0">
            and w.order_type in
            <foreach collection="orderTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="handleResult >= 0 ">
            and w.handle_result = #{handleResult}
        </if>

        <if test="workOrderId > 0 ">
            and w.id = #{workOrderId}
        </if>

        <if test="caseIds.size > 0 ">
            and w.case_id in
            <foreach collection="caseIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="startCreateTime != null and startCreateTime!='' and endCreateTime != null and endCreateTime!='' ">
            <![CDATA[ and w.create_time >= #{startCreateTime} and w.create_time < #{endCreateTime} ]]>
        </if>

        <if test="startHandleTime != null and startHandleTime!='' and endHandleTime != null and endHandleTime!='' ">
            <![CDATA[ and w.handle_time >= #{startHandleTime} and w.handle_time < #{endHandleTime} ]]>
        </if>

        <if test="startDoneTime != null and startDoneTime!='' and endDoneTime != null and endDoneTime!='' ">
            <![CDATA[ and w.finish_time >= #{startDoneTime} and w.finish_time < #{endDoneTime} ]]>
        </if>

    </select>

    <select id="getMinIdQcByBatchV3" resultType="java.lang.Long">
        select Min(id)
        from work_order
        where is_delete= 0
        <if test="orderType > 0">
            and order_type = #{orderType}
        </if>
        <if test="finishStartTime != null and finishStartTime != '' and  finishEndTime != null and finishEndTime != ''">
            <![CDATA[ and finish_time >= #{finishStartTime} and finish_time < #{finishEndTime} ]]>
        </if>
        <if test="handleResult >= 0 ">
            and handle_result = #{handleResult}
        </if>
    </select>

    <select id="staffHandleCount"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderDoingCount">
        select operator_id operatorId , count(1) num from work_order
        where order_type = #{orderType}
        and handle_result=#{handleResult}
        AND is_delete=0
        group by operator_id
    </select>

    <select id="getTodayTypeWithCountByUserAndHandleResult"
            resultType="com.shuidihuzhu.workorder.model.order.VonDoingCountTypeVO">
        select order_type , count(1) num from work_order
        <where>
            operator_id = #{operatorId}
            and order_type in
            <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
                #{orderType}
            </foreach>
            and handle_result in
            <foreach collection="handleResults" open="(" close=")" item="handleResult" separator=",">
                #{handleResult}
            </foreach>
            and finish_time between date(now()) and date(now() - interval -1 day)
        </where>
        group by order_type
    </select>

</mapper>

