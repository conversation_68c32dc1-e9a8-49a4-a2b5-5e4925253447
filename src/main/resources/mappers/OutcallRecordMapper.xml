<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.workorder.dao.IOutcallRecordDAO">
    <sql id="Table_Name">
        cf_call_record
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.workorder.model.OutcallRecordDO">
        insert into <include refid="Table_Name"/>
        (case_id,work_order_id,call_unicode)
        values
        (#{caseId},#{workOrderId},#{callUnicode})
    </insert>
</mapper>