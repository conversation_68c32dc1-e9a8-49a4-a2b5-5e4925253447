<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.workorder.dao.UgcWorkOrderDao">

    <sql id="select_fields">
        id, case_id,order_type,order_level,handle_result,operator_id,operator_org_id,handle_time,create_time,update_time,finish_time
    </sql>


    <update id="handle">
        update work_order set handle_result=#{handleResult},operator_id=#{operatorId},finish_time=now()
        where id = #{wrokOrderId} and handle_result in (1,3)
    </update>


    <select id="getWorkorderList" resultType="com.shuidihuzhu.workorder.model.order.WorkOrderBaseVo">

        select w.*
        from work_order w
        <if test="reasonCode != null and reasonCode!='' ">
            left join work_order_ext e on w.id = e.work_order_id
        </if>
        where w.operator_id in
        <foreach collection="operatorIds" item="user" open="(" separator="," close=")">
            #{user}
        </foreach>
        and w.order_type=#{orderType} and w.handle_result in

        <foreach collection="handleResult" item="result" open="(" separator="," close=")">
            #{result}
        </foreach>

        AND w.is_delete=0

        <if test="caseId > 0 ">
            and w.case_id = #{caseId}
        </if>

        <if test="orderLevel > 0">
            and w.order_level = #{orderLevel}
        </if>

        <if test="startTime != null and startTime!='' and endTime != null and endTime!='' ">
            <![CDATA[ and w.finish_time >= #{startTime} and w.finish_time < #{endTime} ]]>
        </if>

        <if test="reasonCode != null and reasonCode!='' ">
            and e.ext_name='reasonCode' and e.ext_value=#{reasonCode} and e.is_delete = 0
        </if>

        <if test="workOrderId > 0 and paging == 'next'">
            <![CDATA[  and w.id < #{workOrderId} ]]>
            order by w.id desc
        </if>

        <if test="workOrderId > 0 and paging == 'pre' ">
            and w.id > #{workOrderId}
            order by w.id
        </if>

        <if test="workOrderId == 0 ">
            order by w.id desc
        </if>

        limit #{pageSize}
    </select>

</mapper>

