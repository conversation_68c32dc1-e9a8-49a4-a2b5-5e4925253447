<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderReportStatisticsDao">
    <sql id="tableName">
        work_order_report_statistics
    </sql>


    <insert id="updateAssignCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, assign_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        assign_count = assign_count + 1
    </insert>

    <insert id="updateOfflineCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, offline_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        offline_count = offline_count + 1
    </insert>

    <insert id="updateDoneCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, done_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        done_count = done_count + 1
    </insert>

    <insert id="updateCallCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, call_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        call_count = call_count + 1
    </insert>

    <insert id="updateCallDuration">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type,call_duration)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType},#{duration})
        ON DUPLICATE KEY UPDATE
        call_duration = call_duration + #{duration}
    </insert>

    <insert id="updateEndDealCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, end_deal_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        end_deal_count = end_deal_count + 1
    </insert>

    <insert id="updateNoneedDealCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, noneed_deal_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        noneed_deal_count = noneed_deal_count + 1
    </insert>

    <insert id="updateUpgradeCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, upgrade_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        upgrade_count = upgrade_count + 1
    </insert>

    <insert id="updateLostCount">
        insert into
        <include refid="tableName"/>
        (stat_day, stat_hour, operator_id, order_type, lost_count)
        values
        (#{statDay}, #{statHour}, #{operatorId}, #{orderType}, 1)
        ON DUPLICATE KEY UPDATE
        lost_count = lost_count + 1
    </insert>

    <select id="getSumByUserIdsAndOrderType"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderReportStatistics">
        select
        operator_id,
        coalesce(sum(offline_count),0) as offlineCount,
        coalesce(sum(assign_count),0) as assignCount,
        coalesce(sum(done_count),0) as doneCount,
        coalesce(sum(call_count),0) as callCount,
        coalesce(sum(call_duration),0) as callDuration,
        coalesce(sum(end_deal_count),0) as endDealCount,
        coalesce(sum(noneed_deal_count),0) as noNeedDealCount,
        coalesce(sum(upgrade_count),0) as upgradeCount,
        coalesce(sum(lost_count),0) as lostCount
        from
        <include refid="tableName"/>
        where stat_day = #{statDay}
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and operator_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        group by operator_id
    </select>

    <select id="getByUserIdAndOrderType"
            resultType="com.shuidihuzhu.workorder.model.order.WorkOrderReportStatistics">
        select
        order_type,
        coalesce(sum(offline_count),0) as offlineCount,
        coalesce(sum(assign_count),0) as assignCount,
        coalesce(sum(done_count),0) as doneCount,
        coalesce(sum(call_count),0) as callCount,
        coalesce(sum(call_duration),0) as callDuration,
        coalesce(sum(end_deal_count),0) as endDealCount,
        coalesce(sum(noneed_deal_count),0) as noNeedDealCount,
        coalesce(sum(upgrade_count),0) as upgradeCount,
        coalesce(sum(lost_count),0) as lostCount
        from
        <include refid="tableName"/>
        where stat_day = #{statDay}
        and order_type in
        <foreach collection="orderTypes" open="(" close=")" item="orderType" separator=",">
            #{orderType}
        </foreach>
        and operator_id = #{userId}
        group by order_type
    </select>


</mapper>