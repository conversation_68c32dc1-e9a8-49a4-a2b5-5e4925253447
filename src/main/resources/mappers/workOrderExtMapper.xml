<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderDaoExt">


    <sql id="select_column_list">
        id,work_order_id,ext_name,ext_value,create_time
    </sql>

    <insert id="createWorkOrderExt" parameterType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt" useGeneratedKeys="true" keyProperty="id">

        insert into work_order_ext (work_order_id,ext_name,ext_value)
        values
        <foreach collection="workOrderExts" item="item"  separator=",">
            (
            #{item.workOrderId},#{item.extName},#{item.extValue}
            )
        </foreach>
    </insert>

    <update id="updateWorkOderExt">
        update work_order_ext
        set material_audit_status = #{materialAuditStatus}
        where work_order_id = #{workOrderId}
    </update>

    <update id="updateByNameValue">
        update work_order_ext
        set ext_value = #{extValue}
        where work_order_id = #{workOrderId} and ext_name=#{extName} and is_delete = 0
    </update>

    <update id="delete">
        update work_order_ext
        set is_delete = 1
        where work_order_id = #{workOrderId} and ext_name=#{extName}
    </update>

    <insert id="insertWorkOrderExt">

        insert into work_order_ext (work_order_id,ext_name,ext_value)
        values(
            #{workOrderId},#{extName},#{extValue}
            )
    </insert>


    <select id="getWorkOrderExt" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id=#{workOrderId} and ext_name=#{extName} and is_delete=0
        order by id desc
        limit 1

    </select>

    <insert id="insertWorkOrderExtGetId" parameterType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt" useGeneratedKeys="true" keyProperty="id">

        insert into work_order_ext (work_order_id,ext_name,ext_value)
        values(
                  #{workOrderId},#{extName},#{extValue}
              )
    </insert>


    <select id="getWorkOrderExtByName" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id=#{workOrderId} and ext_name in
        <foreach collection="extNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete=0

    </select>

    <select id="getWorkOrderExtByNameMaster" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id=#{workOrderId} and ext_name in
        <foreach collection="extNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete=0

    </select>

    <select id="getWorkOrderExts" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id in
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ext_name=#{extName} and is_delete=0

    </select>

    <select id="getWorkOrderExtList" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id in
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ext_name=#{extName} and ext_value=#{extValue} and is_delete=0
        order by work_order_id desc

    </select>

    <update id="updateExtValue">
        update work_order_ext set ext_value = #{extValue}
        where work_order_id in
        <foreach collection="workOrderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ext_name = #{extName}
    </update>

    <select id="getWorkOrderExtDesc" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id=#{workOrderId} and ext_name=#{extName} and is_delete=0
        order by id desc limit 1
    </select>

    <select id="getWorkOrderExtIgnoreDelete"
            resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from  work_order_ext
        where work_order_id = #{workOrderId}
        and ext_name = #{extName}
    </select>

    <select id="getWorkOrderExtNoDelete"
            resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from  work_order_ext
        where work_order_id = #{workOrderId}
        and ext_name = #{extName} and is_delete=0
    </select>

    <select id="getWorkOrderExtByWorkOrderId"
            resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from  work_order_ext
        where work_order_id = #{workOrderId} and is_delete=0
    </select>

    <select id="getAllOrderExtIgnoreDelete" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id=#{workOrderId}
        <if test="extNames != null and extNames.size() > 0">
        and ext_name in
            <foreach collection="extNames" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="listOrderExtByIdsAndExtNames" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from work_order_ext
        where work_order_id in
        <foreach collection="workOrderIds" open="(" item="workOrderId" separator="," close=")">
            #{workOrderId}
        </foreach>
        and ext_name in
        <foreach collection="extNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete=0
    </select>

    <select id="getWorkOrderExtByWorkOrderIds" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderExt">
        select <include refid="select_column_list"/>
        from  work_order_ext
        where work_order_id in
        <foreach collection="workOrderIds" open="(" item="workOrderId" separator="," close=")">
            #{workOrderId}
        </foreach>
        and is_delete=0
    </select>
</mapper>

