<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.UserOperationRecordDao">

    <sql id="table_name">
		work_order_record
	</sql>

    <sql id="base_column_list">
		id,
		create_time,
		<include refid="insert_column_list"/>
	</sql>

    <sql id="insert_column_list">
         work_order_id,
         work_order_type,
         operator_id,
         operate_mode,
         operate_desc,
         comment
	</sql>


    <insert id="saveRecord" parameterType="com.shuidihuzhu.workorder.model.WorkOrderRecord" useGeneratedKeys="true" keyProperty="id">

        insert into work_order_record (work_order_id,work_order_type,operator_id,operate_mode,operate_desc,comment)
        values(
         #{workOrderId},
         #{workOrderType},
         #{operatorId},
         #{operateMode},
         #{operateDesc},
         #{comment}
        )
    </insert>




    <insert id="saveRecordList" parameterType="com.shuidihuzhu.workorder.model.WorkOrderRecord" useGeneratedKeys="true" keyProperty="id">

        insert into work_order_record (work_order_id,work_order_type,operator_id,operate_mode,operate_desc,comment)
        values
        <foreach collection="workOrderRecords" item="record"  separator=",">
            (
            #{record.workOrderId},
            #{record.workOrderType},
            #{record.operatorId},
            #{record.operateMode},
            #{record.operateDesc},
            #{record.comment}
            )
        </foreach>

    </insert>

    <select id="listByWorkOrderId" resultType="com.shuidihuzhu.workorder.model.WorkOrderRecord">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        <where>
         and `work_order_id` = #{workOrderId}
         and `is_delete` = 0
        </where>
        order by id desc
    </select>
    <select id="listByWorkOrderIdsAndOptModes"
                     resultType="com.shuidihuzhu.workorder.model.WorkOrderRecord">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        <where>
         and `work_order_id` in
            <foreach collection="workOrderIds" open="(" item="workOrderId" separator="," close=")">
                #{workOrderId,jdbcType=BIGINT}
            </foreach>
         <if test="optModes != null and optModes.size() != 0">
             and operate_mode in
             <foreach collection="optModes " open="(" item="optMode" separator="," close=")">
                 #{optMode,jdbcType=INTEGER}
             </foreach>
         </if>
         and `is_delete` = 0
        </where>
    </select>


</mapper>


