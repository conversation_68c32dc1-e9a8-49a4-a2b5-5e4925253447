<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.CfWorkOrderQualitySpotDao">



    <insert id="save" parameterType="com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig">
        insert into risk_quality_spot_work_order_user_config
        (user_id, scene, distribution_type, range_limit, rule_info )
        values (#{userId}, #{scene}, #{distributionType}, #{rangeLimit}, #{ruleInfo})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig">
        update risk_quality_spot_work_order_user_config
        set distribution_type = #{distributionType},
        range_limit = #{rangeLimit},
        rule_info = #{ruleInfo}
        where `id` = #{id}
    </update>

    <select id="listByUserIds"
            resultType="com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig">
        select *
        from risk_quality_spot_work_order_user_config
        where scene = #{scene}
        AND `user_id` in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND is_delete  = 0
     </select>

    <select id="getByUserId"
            resultType="com.shuidihuzhu.client.cf.workorder.model.RiskQualitySpotWorkOrderUserConfig">
        select *
        from risk_quality_spot_work_order_user_config
        where scene = #{scene}
        AND `user_id` = #{userId}
        AND is_delete  = 0
        order by id desc
        limit 1
    </select>
</mapper>

