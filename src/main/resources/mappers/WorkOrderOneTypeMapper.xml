<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderOneTypeDAO">

    <sql id = "table_name">
        work_order_one_type
    </sql>
    <sql id="select_fields">
        id, one_type_code, msg
    </sql>
    <insert id="insertWorkOrder">
        insert into <include refid="table_name"/>
        (one_type_code, msg)
        values(
        #{oneTypeCode},
        #{msg}
        )
    </insert>

    <select id="getByOneTypeCode" resultType="com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `one_type_code` = #{oneTypeCode}
        and
        `is_delete` = 0
    </select>
    <select id="getAllOrderType" resultType="com.shuidihuzhu.workorder.model.WorkOrderOneTypeDO">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where
        `is_delete` = 0
    </select>
</mapper>