<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >


<mapper namespace="com.shuidihuzhu.workorder.dao.WorkOrderTimelineDao">

    <insert id="insertTimeLine" parameterType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderTimeline">

        insert into work_order_timeline (work_order_id,work_order_type,work_result,operator_id,operate_type,comment)
        values(
            #{workOrderId},#{workOrderType},#{workResult},
             #{operatorId},#{operateType},#{comment}
            )
    </insert>


    <select id="getWorkOrderTimeline" resultType="com.shuidihuzhu.client.cf.workorder.model.WorkOrderTimeline">

        select work_order_id,work_order_type,work_result,operator_id,operate_type
        from work_order_timeline
        where work_order_id=#{workOrderId}
        and work_order_type=#{workOrderType}
        and work_result=#{workResult}
        and is_delete=0
        limit 1;

    </select>


</mapper>

