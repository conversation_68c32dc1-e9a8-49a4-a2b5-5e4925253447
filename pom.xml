<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

	<groupId>com.shuidihuzhu.cf</groupId>
	<artifactId>cf-work-order</artifactId>
	<version>1.0.489-SNAPSHOT</version>
	<packaging>jar</packaging>

    <name>cf-work-order</name>
    <description>新工单系统</description>
    <parent>
        <artifactId>spring-cloud-shuidi-parent</artifactId>
        <groupId>com.shuidihuzhu.infra</groupId>
        <version>3.1.42</version>
    </parent>
    <scm>
        <connection>scm:git:http://git.shuiditech.com/cf/cf-work-order.git</connection>
        <developerConnection>scm:git:**********************:cf/cf-work-order.git</developerConnection>
        <url>http://git.shuiditech.com/cf/cf-work-order.git</url>
        <tag>1.0.340</tag>
    </scm>

    <properties>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.source.skip>false</maven.source.skip>
        <boot-image.tag>2</boot-image.tag>
        <java.version>11</java.version>

        <servicelog-meta-cf.version>1.0.54</servicelog-meta-cf.version>
        <guava-retrying.version>2.0.0</guava-retrying.version>
        <simpleclient-pushgateway.version>0.9.0</simpleclient-pushgateway.version>
        <easyexcel.version>2.1.6</easyexcel.version>

        <cf-data-platform-client.version>1.0.04</cf-data-platform-client.version>
        <simpleclient-pushgateway.version>0.9.0</simpleclient-pushgateway.version>

        <shuidi-auth-saas-client.version>0.0.20</shuidi-auth-saas-client.version>
        <cf-api-client.version>3.6.191</cf-api-client.version>
        <cf-client.version>1.2.421</cf-client.version>
        <cf-risk-rpc-client.version>1.0.43</cf-risk-rpc-client.version>
        <cf-patient-recruit-client.version>1.0.269</cf-patient-recruit-client.version>
        <cf-client-base.version>9.0.86</cf-client-base.version>
        <cf-ugc-client.version>1.1.32</cf-ugc-client.version>
        <pr-patient-info-client.version>0.0.113</pr-patient-info-client.version>
        <cf-admin-api-pure-client.version>9.0.18</cf-admin-api-pure-client.version>
        <cf-enhancer-starter.version>1.0.92</cf-enhancer-starter.version>
        <account-grpc-client.version>2.2.124</account-grpc-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-common-dependencies</artifactId>
                <version>RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.shuidihuzhu.cf</groupId>
                <artifactId>cf-enhancer-starter</artifactId>
                <version>${cf-enhancer-starter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
            <version>${cf-admin-api-pure-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
            <version>${cf-ugc-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client-base</artifactId>
            <version>${cf-client-base.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>kafka-sdk</artifactId>
            <version>1.1.25-acl</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-data-platform-client</artifactId>
            <version>${cf-data-platform-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>${easyexcel.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>${guava-retrying.version}</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
            <version>${servicelog-meta-cf.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <version>${cf-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.msg</groupId>
                    <artifactId>msg-rpc-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.pf</groupId>
                    <artifactId>pf-common-v2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.pf</groupId>
                    <artifactId>pf-tools</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
            <version>${cf-api-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-util</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>shuidi-wx-provider</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>web-core-v2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-rpc-client</artifactId>
            <version>2.1.35</version>
            <exclusions>
                <exclusion>
                    <artifactId>feign-enhance-spring-boot-starter</artifactId>
                    <groupId>com.shuidihuzhu.infra</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf-risk</groupId>
            <artifactId>cf-risk-rpc-client</artifactId>
            <version>${cf-risk-rpc-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>web-model</artifactId>
                    <groupId>com.shuidihuzhu.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pr</groupId>
            <artifactId>cf-patient-recruit-client</artifactId>
            <version>${cf-patient-recruit-client.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>pr-framework</artifactId>
                    <groupId>com.shuidihuzhu.pr</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pr-override-detect-client</artifactId>
                    <groupId>com.shuidihuzhu.pr</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_pushgateway</artifactId>
            <version>${simpleclient-pushgateway.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
            <version>${shuidi-auth-saas-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-alarm-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pr</groupId>
            <artifactId>pr-patient-info-client</artifactId>
            <version>${pr-patient-info-client.version}</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>javax.activation</groupId>
                        <artifactId>activation</artifactId>
                        <version>1.1.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <id>enforce</id>
                        <configuration>
                            <skip>true</skip>
                            <rules />
                        </configuration>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.0</version>
                    </dependency>
                    <dependency>
                        <groupId>com.shuidihuzhu.cf</groupId>
                        <artifactId>custom-rule</artifactId>
                        <version>1.0.21</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
